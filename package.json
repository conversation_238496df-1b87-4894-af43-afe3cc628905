{"name": "low-flight-air-space", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "deploy": "./deploy.sh", "preview": "vite preview", "clean": "npm cache clean --force", "lint": "eslint --fix", "fixcode": "prettier --config prettier.config.js . --write", "code": "./git_code.sh"}, "dependencies": {"@antv/l7": "^2.23.0", "@antv/l7-draw": "^3.1.5", "@antv/l7-maps": "^2.23.0", "@turf/turf": "^7.1.0", "@vueuse/core": "^13.5.0", "axios": "^1.10.0", "element-plus": "^2.10.4", "file-saver": "2.0.5", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.0", "sm-crypto": "^0.3.13", "vue": "^3.5.17", "vue-i18n": "^11.0.1", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.11.1", "@mistjs/vite-plugin-px2viewport": "^0.1.0", "@vitejs/plugin-vue": "^5.2.3", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^9.33.0", "globals": "^16.3.0", "less": "^4.3.0", "prettier": "^3.6.2", "sass-embedded": "^1.83.0", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^28.8.0", "vite": "^6.3.5"}}