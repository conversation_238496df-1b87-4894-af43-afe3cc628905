import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { px2viewport } from '@mistjs/vite-plugin-px2viewport'
import { ProxyUrl } from './src/request/common/baseUrl.js'

export default defineConfig({
  base: process.env.NODE_ENV === 'development' ? '/' : './',
  plugins: [
    // px2viewport({
    //   viewportWidth: 2560,
    //   viewportUnit: 'vw',
    //   cssOptions: {
    //     unitToConvert: 'px', // 要转化的单位
    //     viewportWidth: 2560, // 视窗的宽度，对应的是我们设计稿的宽度，一般是375
    //     // viewportHeight: 945, // 视窗的高度，对应的是我们设计稿的高度，一般是667
    //     unitPrecision: 5, // 指定`px`转换为视窗单位值的小数位数（很多时候无法整除）
    //     propList: ['*'], // 能转化为vw的属性列表
    //     viewportUnit: 'vw', // 指定需要转换成的视窗单位，建议使用vw
    //     fontViewportUnit: 'vw', // 字体使用的视窗单位
    //     selectorBlackList: ['.ignore'], // 指定不转换为视窗单位的类，可以自定义，可以无限添加,建议定义一至两个通用的类名
    //     minPixelValue: 1, // 小于或等于`1px`不转换为视窗单位，你也可以设置为你想要的值
    //     mediaQuery: false, // 允许在媒体查询中转换`px`
    //     replace: true, // 是否直接更换属性值，而不添加备用属性
    //     exclude: [/node_modules/], // 忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
    //     landscape: false, // 是否添加根据 landscapeWidth 生成的媒体查询条件 @media (orientation: landscape)
    //     landscapeUnit: false, // 横屏时使用的单位
    //   }
    // }),
    vue({}),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ["vue", "vue-router"],
      dts: "src/auto-imports.d.ts",
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: "src/auto-imports.d.ts",
    })
  ],
  resolve: {
    // 配置路径别名
    alias: {
      "@": resolve(__dirname, "./src"),
    },
  },
  server: {
    proxy: {
      "/req": {
        target: ProxyUrl,
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/req/, ""),
      },
    },
    host: "0.0.0.0",
  },
  build: {
    manifest: true,
    outDir: "./dist",
    target: "modules",
    assetsDir: "assets",
    assetsInlineLimit: 360000,
  }
})
