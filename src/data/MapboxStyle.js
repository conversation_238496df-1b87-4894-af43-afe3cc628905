const MapboxStyle = {
  version: 8,
  sources: {
    'raster-tiles': {
      type: 'raster',
      tiles: [(process.env.NODE_ENV === 'development' ? '/req/map-tile' : '/map-tile') + '/{z}/{x}/{y}.png'],
      tileSize: 256,
      bounds: [103.999178, 18.562035, 137.215731, 41.448997],
      scheme: 'xyz',
    },
  },
  layers: [
    {
      id: 'simple-tiles',
      type: 'raster',
      source: 'raster-tiles',
    },
  ],
}
export default MapboxStyle
