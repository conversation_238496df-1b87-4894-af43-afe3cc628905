const MenusData = [
  {
    name: '数字化空域',
    icon: 'MapLocation',
    url: 'map/ShowMap.vue',
  },
  {
    name: '空域管理',
    icon: 'Place',
    nodeList: [
      {
        url: 'airSpaceManagement/AirSpaceDesign.vue',
        name: '空域规划',
      },
      {
        url: 'airSpaceManagement/AirWayDesign.vue',
        name: '航路管理',
      },
      {
        url: 'airSpaceManagement/AirLineDesign.vue',
        name: '航线管理',
      },
      {
        url: 'airSpaceManagement/CommonAirSpace.vue',
        name: '常用空域',
      },
      {
        url: 'airSpaceManagement/airspaceApplication/ApplicationManage.vue',
        name: '空域申请',
      },
      {
        url: 'airSpaceManagement/airspaceApproval/ApprovalManage.vue',
        name: '空域审批',
      },
      {
        url: '',
        name: '重要地点',
        nodeList: [
          {
            url: 'mapManagement/obstacleManagement/ObstacleManagement.vue',
            name: '障碍物管理',
          },
          {
            url: 'mapManagement/poiManagement/PoiPoint.vue',
            name: 'POI点',
          },
        ],
      },
    ],
  },
  {
    name: '规则管理',
    icon: 'MessageBox',
    nodeList: [
      {
        url: 'rule/staticRules/List.vue',
        name: '静态规则',
      },
      {
        url: 'rule/dynamicRules/List.vue',
        name: '动态规则',
      },
      {
        url: 'rule/whiteList/List.vue',
        name: '白名单',
      },
    ],
  },
  {
    name: '用空管理',
    icon: 'Guide',
    url: '',
    nodeList: [
      {
        name: '禁飞区管理',
        url: 'useAirManagement/NoFlyManage.vue',
      },
      {
        name: '管制区管理',
        url: 'useAirManagement/RestrictedManage.vue',
      },
      {
        name: '飞行活动审批',
        url: '',
        nodeList: [
          {
            name: '一般飞行活动',
            url: 'flightActivity/general/General.vue',
          },
          {
            name: '长期飞行活动',
            url: 'flightActivity/longTerm/LongTerm.vue',
          },
        ],
      },
      {
        name: '重点关注',
        url: 'focus/Focus.vue',
      },
    ],
  },
  {
    name: '系统管理',
    icon: 'SetUp',
    nodeList: [
      {
        name: '类型管理',
        url: 'system/TypeList.vue',
      },
      {
        name: '标签管理',
        url: 'system/TagList.vue',
      },
    ],
  },

  {
    name: '航空信息资料',
    icon: 'Document',
    url: '',
  },
]
export default MenusData
