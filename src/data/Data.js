import { PlanTagInfoData, TagInfoData } from '@/components/common/commonTitle/Data.js'
import { useDateFormat } from '@vueuse/core'

export const TypeData = [
  {
    label: '个人',
    value: 'PERSONAL',
  },
  {
    label: '企业',
    value: 'ENTERPRISE',
  },
]

export const FlightActivityTypeData = [
  {
    label: '一般飞行活动',
    value: 'GENERAL',
  },
  {
    label: '长期飞行活动',
    value: 'LONG_TERM',
  },
  {
    label: '场内飞行活动',
    value: 'IN_FIELD',
  },
]

export const IdTypeData = [
  {
    label: '身份证',
    value: 'ID_CARD',
    length: 18,
  },
  {
    label: '港澳居民往来内地通行证',
    value: 'HK_MACAO_PASS',
    length: 11,
  },
  {
    label: '台湾居民往来大陆通行证',
    value: 'TW_PASS',
    length: 8,
  },
  {
    label: '护照',
    value: 'PASSPORT',
    length: 9,
  },
  {
    label: '外国人永久居留身份证',
    value: 'FOREIGN_RESIDENT_CARD',
    length: 18,
  },
  {
    label: '港澳居民居住证',
    value: 'HK_MACAO_RESIDENCE_PERMIT',
    length: 18,
  },
  {
    label: '台湾居民居住证',
    value: 'TW_RESIDENCE_PERMIT',
    length: 18,
  },
  {
    label: '其他',
    value: 'OTHER',
    length: 20,
  },
]

export const AuthMethodData = [
  {
    label: '企业/农民专业合作社',
    value: 'ENTERPRISE',
  },
  {
    label: '机关事业单位法人',
    value: 'GOV_AGENCY',
  },
  {
    label: '个体工商户',
    value: 'INDIVIDUAL',
  },
  {
    label: '其他类型单位',
    value: 'OTHER',
  },
]

export const StatusTagData = [
  {
    status: 0,
    tag: TagInfoData.CANCEL,
  },
  {
    status: 1,
    tag: TagInfoData.AUTH,
  },
  {
    status: 2,
    tag: TagInfoData.DISABLE,
  },
  {
    status: 3,
    tag: TagInfoData.NOT_AUTH,
  },
  {
    status: 4,
    tag: TagInfoData.REFUSE,
  },
  {
    status: -1,
    tag: TagInfoData.DELETE,
  },
]

export const PlanStatusTagData = [
  {
    status: -1,
    tag: PlanTagInfoData.NOT_SUBMIT,
  },
  {
    status: 0,
    tag: PlanTagInfoData.CANCEL,
  },
  {
    status: 3,
    tag: PlanTagInfoData.NOT_AUTH,
  },
  {
    status: 4,
    tag: PlanTagInfoData.REFUSE,
  },
  {
    status: 5,
    tag: PlanTagInfoData.AUTHING,
  },
  {
    status: 6,
    tag: PlanTagInfoData.AUTH,
  },
  {
    status: 7,
    tag: PlanTagInfoData.EXECUTING,
  },
  {
    status: 8,
    tag: PlanTagInfoData.COMPLETED,
  },
  {
    status: 9,
    tag: PlanTagInfoData.REVOKE,
  },
]

export const InputLengthData = {
  NAME: 16,
  LOGIN_NAME: 16,
  UNIT_NAME: 32,
  CONTENT: 200,
  ADDRESS: 64,
}

export const CommonDatetimeRange = [
  new Date(
    new Date().getFullYear(),
    new Date().getMonth(),
    new Date().getDate() - 6, // 天数
    0,
    0,
    0,
    0,
  ),
  new Date().setHours(23, 59, 59, 0),
]

export const GetTimeRange = times => {
  return [useDateFormat(times[0], 'YYYYMMDD').value + '000000', useDateFormat(times[1], 'YYYYMMDD').value + '235959']
}
