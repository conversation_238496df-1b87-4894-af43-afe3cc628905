// 一般登录rules
export const CommonRule = () => ({
  username: [
    {
      required: true,
      message: '请输入您的账号',
      trigger: 'blur',
    },
  ],
  password: [
    {
      required: true,
      message: '请输入您的密码',
      trigger: 'blur',
    },
  ],
  captchaValue: [
    {
      required: true,
      message: '请输入验证码',
      trigger: 'blur',
    },
  ],
})

// 重置密码登录rules
export const ResetRule = (loginRef, formData) => ({
  username: [
    {
      required: true,
      message: '请输入您的账号',
      trigger: 'blur',
    },
  ],
  password: [
    {
      required: true,
      message: '请输入您的原密码',
      trigger: 'blur',
    },
  ],
  newPassword: [
    {
      required: true,
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (!value.trim()) return callback(new Error('请输入您的新密码'))
        if (value.length < 8) return callback(new Error('密码长度不能少于8位'))
        if (!/[a-z]/.test(value) || !/[A-Z]/.test(value)) return callback(new Error('密码必须包含大小写字母'))
        if (!/[0-9]/.test(value)) return callback(new Error('密码必须包含数字'))
        // 包含特殊字符
        if (!/[^a-zA-Z0-9]/.test(value)) return callback(new Error('密码必须包含特殊字符'))
        if (!loginRef) return
        if (formData.confirmPassword) loginRef.validateField('confirmPassword')
        callback()
      },
    },
  ],
  confirmPassword: [
    {
      required: true,
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (!value.trim()) callback(new Error('请确认您的新密码'))
        if (value.trim() !== formData.newPassword.trim()) return callback(new Error('两次输入的密码不一致'))
        callback()
      },
    },
  ],
  captchaValue: [
    {
      required: true,
      message: '请输入验证码',
      trigger: 'blur',
    },
  ],
})
