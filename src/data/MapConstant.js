export const MapType = {
  POINT: 'POINT', // 点
  LINE: 'LINE', // 线
  PLANE: 'PLANE', // 面
}
export const DrawType = {
  Polygon: 'polygon', // 多边形
  Point: 'point', // 点
  Line: 'line', // 线
  Circle: 'circle', // 圆
  Rectangle: 'rectangle', // 矩形
}

export const BaseOptions = {
  autoActive: false,
  multiple: false,
  editable: true,
  keyboard: { remove: false },
  areaOptions: {
    format: squareMeters =>
      squareMeters > 1000 ? `${+(squareMeters / 1000000).toFixed(3)}km²` : `${+squareMeters.toFixed(2)}m²`,
  },
  distanceOptions: {},
}
