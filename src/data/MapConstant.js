export const MapType = {
  POINT: 'POINT', // 点
  LINE: 'LINE', // 线
  PLANE: 'PLANE', // 面
}
export const DrawType = {
  Polygon: 'polygon', // 多边形
  Point: 'point', // 点
  Line: 'line', // 线
  Circle: 'circle', // 圆
  Rectangle: 'rectangle', // 矩形
}

export const BaseOptions = {
  autoActive: false,
  multiple: false,
  editable: true,
  keyboard: { remove: false },
  areaOptions: {
    format: squareMeters =>
      squareMeters > 100000 ? `${+(squareMeters / 1000000).toFixed(3)}km²` : `${+squareMeters.toFixed(2)}m²`,
  },
  distanceOptions: {},
}

export const ModeType = {
  ADD: 'add', // 添加
  EDIT: 'edit', // 编辑
  SHOW: 'show', // 查看
}

// 空域各个类型（适飞， 管制， 禁飞）默认颜色
export const SpaceDefaultColor = {
  SUITABLE: '#F7712F', // 适飞
  CONTROLLED: '#FFD900', // 管制
  NO_FLY: '#FF2A24', // 禁飞
}

export const EmptyData = { type: 'FeatureCollection', features: [] }
