import { createRouter, createWebHashHistory } from 'vue-router'
import useUser from '@/store/user.js'

const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/Login.vue'),
    beforeEnter: (to, from, next) => {
      const user = useUser()
      next(user.getAccessId() ? '/' : true)
    },
  },
  {
    path: '/',
    name: 'home',
    component: () => import('@/views/home/<USER>'),
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes: routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

router.beforeEach((to, from, next) => {
  const user = useUser()
  if (user.getAccessId() || to.path === '/login') next()
  else next(`/login`)
})
export default router
