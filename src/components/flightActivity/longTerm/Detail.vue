<template>
  <div class="custom">
    <Title
      title="长期飞行活动详情"
      :is-back="true"
      back-path="FlightActivity/general/General.vue"
      :tag-info="PlanStatusTagData.find(a => a.status === (data.deleteTime ? -1 : data.status))?.tag"
    >
      <div v-if="data.status === -1 || data.status === 0 || data.status === 3">
        <el-button type="primary" @click="pass">通过</el-button>
        <el-button type="danger" @click="reject">拒绝</el-button>
      </div>
    </Title>
    <div class="detail-container">
      <CommonCard title="基本信息">
        <el-row class="row">
          <el-col :span="6">
            <span class="label">飞行活动名称：</span>
            <span class="value">{{ data.flightActivityName }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">单号：</span>
            <span class="value">{{ data.orderNo }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">申请时间：</span>
            <span class="value">{{ formatDate(data.createTime) }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">申请人：</span>
            <span class="value">{{ data.createUserName }}</span>
          </el-col>
        </el-row>
        <el-row class="row">
          <el-col :span="6">
            <span class="label">城市：</span>
            <span class="value">{{ data.cityName }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">机构：</span>
            <span class="value">{{ data.orgName }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">审批机构：</span>
            <span class="value">{{ data.approvalOrgName }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">属地：</span>
            <span class="value">{{ data.regionNames.join('、') }}</span>
          </el-col>
        </el-row>
        <el-row class="row">
          <el-col :span="6">
            <span class="label">区域类型：</span>
            <span class="value">{{ RegionTypeData.find(a => a.value === data.regionType)?.label }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">遥控模式：</span>
            <span class="value">{{ ControlModeData.find(a => a.value === data.controlMode)?.label }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">飞行模式：</span>
            <span class="value">{{ FlightModeData.find(a => a.value === data.flightMode)?.label }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">飞行等级：</span>
            <span class="value">{{ FlightPlanLevelData.find(a => a.value === data.flightPlanLevel)?.label }}</span>
          </el-col>
        </el-row>
        <el-row class="row">
          <el-col :span="6">
            <span class="label">预计开始时间：</span>
            <span class="value">{{ formatDate(data.startTime) }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">预计结束时间：</span>
            <span class="value">{{ formatDate(data.endTime) }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">实际开始时间：</span>
            <span class="value">{{ formatDate(data.actualStartTime) }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">实际结束时间：</span>
            <span class="value">{{ formatDate(data.actualEndTime) }}</span>
          </el-col>
        </el-row>
        <el-row class="row">
          <el-col :span="6">
            <span class="label">飞行活动类型：</span>
            <span class="value">{{
              FlightActivityTypeData.find(a => a.value === data.flightActivityType)?.label
            }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">任务性质：</span>
            <span class="value">{{ data.taskType.value }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">来源：</span>
            <span class="value">{{ SourceTypeData.find(a => a.value === data.source)?.label }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">活动空域类型：</span>
            <span class="value">{{
              ActivityAirspaceTypeData.find(a => a.value === data.activityAirspaceType)?.label
            }}</span>
          </el-col>
        </el-row>
        <el-row class="row">
          <el-col :span="6">
            <span class="label">高度层：</span>
            <span class="value">{{ data.flightLevelName }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">最大高度(米)：</span>
            <span class="value">{{ data.maxFlightAltitude }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">飞行距离(米)：</span>
            <span class="value">{{ data.flightDistance }}</span>
          </el-col>
          <el-col :span="6">
            <span class="label">乘客人数：</span>
            <span class="value">{{ data.passengerCount }}</span>
          </el-col>
        </el-row>
        <el-row class="row">
          <el-col :span="6">
            <span class="label">载货重量(千克)：</span>
            <span class="value">{{ data.cargoWeight }}</span>
          </el-col>
        </el-row>
        <el-row class="row">
          <el-col :span="24">
            <span class="label">备注：</span>
            <span class="value">{{ data.memo }}</span>
          </el-col>
        </el-row>
        <el-row v-if="data.approveMemo" class="row">
          <el-col :span="24">
            <span class="label" style="color: var(--el-color-danger)">拒绝理由：</span>
            <span class="value" style="color: var(--el-color-danger)">{{ data.approveMemo }}</span>
          </el-col>
        </el-row>
      </CommonCard>
      <CommonCard v-if="data.applyAccountId" title="申请主体">
        <MainPartTable :id="data.applyAccountId" />
      </CommonCard>
      <CommonCard v-if="data.aircraftPersonIds?.length" title="飞手">
        <ManipulatorsTable :id="data.aircraftPersonIds" />
      </CommonCard>
      <CommonCard v-if="data.uavIds?.length" title="无人驾驶航空器">
        <AircraftTable :id="data.uavIds" />
      </CommonCard>
      <CommonCard v-if="data.flightActivityAirlineIds?.length" title="空域">
        <AirLineTable :id="data.flightActivityAirlineIds" ref="airLineRef" />
        <!--      <Map style="width: 100%; aspect-ratio: 5 / 2; border-radius: 12px" :map-data="airSpaceRef?.geometries"></Map>-->
      </CommonCard>
      <CommonCard title="更多的UOM信息">
        <UOMDetail :data="data" />
      </CommonCard>
    </div>
  </div>
</template>

<script setup>
import CommonCard from '@/components/Common/CommonCard/CommonCard.vue'
import Title from '@/components/Common/CommonTitle/CommonTitle.vue'
import { PlanStatusTagData } from '@/data/Data.js'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import { formatDate } from '@/utils/formatUtils.js'
import {
  ActivityAirspaceTypeData,
  ControlModeData,
  FlightActivityTypeData,
  FlightModeData,
  FlightPlanLevelData,
  RegionTypeData,
  SourceTypeData,
} from '../data.js'
import MainPartTable from '@/components/flightActivity/common/MainPartTable/MainPartTable.vue'
import ManipulatorsTable from '@/components/flightActivity/common/ManipulatorsTable/ManipulatorsTable.vue'
import AircraftTable from '@/components/flightActivity/common/AircraftTable/AircraftTable.vue'
import UOMDetail from '@/components/flightActivity/common/UOMDetail/UOMDetail.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { flight_activity_update_status } from '@/request/api/index.js'
import AirLineTable from '@/components/flightActivity/common/AirLineTable/AirLineTable.vue'
const { pathsData } = useComponent()
const data = ref(pathsData.value.param.data)
const airLineRef = useTemplateRef('airLineRef')
const pass = () => {
  ElMessageBox.confirm('是否确定要同意审批当前活动申请', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    flight_activity_update_status({
      id: data.value.id,
      modifyTime: data.value.modifyTime,
      status: 6,
    }).then(() => {
      ElMessage.success('审批通过')
      setPath('flightActivity/longTerm/LongTerm.vue', null, { type: PathType.normal })
    })
  })
}
const reject = () => {
  ElMessageBox.prompt('是否确定要拒绝当前活动申请，确认请填写拒绝原因', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValidator: value => {
      // 如果值为空返回错误信息
      if (!value || value.trim() === '') {
        return '拒绝原因不能为空！'
      }
      // 返回true或undefined表示验证通过
      return true
    },
  }).then(({ value }) => {
    flight_activity_update_status({
      id: data.value.id,
      modifyTime: data.value.modifyTime,
      status: 4,
      approvalMemo: value,
    }).then(() => {
      ElMessage.success('审批拒绝')
      setPath('flightActivity/longTerm/LongTerm.vue', null, { type: PathType.normal })
    })
  })
}
</script>

<style scoped lang="less">
.detail-container {
  width: 100%;
  height: calc(100vh - 64px - 64px - 48px);
  overflow-y: auto;

  .row {
    min-height: 24px;
    margin-bottom: 24px;

    .label {
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.9);
    }

    .value {
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.6);
    }
  }

  .image-row {
    margin-bottom: 24px;

    .image-list {
      width: 100%;
      display: flex;

      .image {
        height: 112px;
        width: 112px;
        margin-right: 24px;
        background-color: #f0f4ff;
      }
    }

    .preview {
      width: 792px;
      height: 450px;
      background-color: #eff6fa;
    }
  }
}
</style>
<style>
@import '../common/custom.css';
</style>
