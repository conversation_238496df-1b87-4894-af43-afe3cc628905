<template>
  <el-row>
    <div style="width: 100%">
      <el-table :data="options" row-key="id" row-class-name="default-row">
        <el-table-column prop="airlineTitle" label="航线标题" :show-overflow-tooltip="true" />
        <el-table-column prop="airlineNo" label="航线编号" :show-overflow-tooltip="true" />
        <el-table-column prop="airlineType" label="航线类型" :show-overflow-tooltip="true">
          <template #default="scope">
            <span v-if="scope.row.airlineType === 0">固定航线</span>
            <span v-else-if="scope.row.airlineType === 1">临时航线</span>
            <span v-else-if="scope.row.airlineType === 2">自定义航线</span>
          </template>
        </el-table-column>
        <el-table-column prop="airlineKind" label="航线性质" :show-overflow-tooltip="true">
          <template #default="scope">
            <span v-if="scope.row.airlineKind === 0">自由</span>
            <span v-else-if="scope.row.airlineKind === 1">物流</span>
            <span v-else-if="scope.row.airlineKind === 2">文旅</span>
            <span v-else-if="scope.row.airlineKind === 3">农林</span>
            <span v-else-if="scope.row.airlineKind === 4">客运</span>
            <span v-else-if="scope.row.airlineKind === 5">政务</span>
            <span v-else>{{ scope.row.airlineKind }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="airlineHeight" label="航线巡航高度(米)" :show-overflow-tooltip="true" />
        <el-table-column prop="airlineWidth" label="航线宽度(米)" :show-overflow-tooltip="true" />
      </el-table>
    </div>
  </el-row>
</template>

<script setup>
import { planList as list } from '@/request/api/airline.js'
const { id = [] } = defineProps({
  id: {
    type: Array,
  },
})
const options = ref([])

onMounted(() => {
  nextTick(() => {
    if (id) query(id)
  })
})

const geometries = computed(() => {
  const ids = []
  let displayStyle = {}
  options.value.forEach(row => {
    if (row.airspaceModelId) {
      ids.push(row.airspaceModelId)
      displayStyle = row.displayStyle
    }
  })
  return { ids, displayStyle }
})

const query = id => {
  options.value.splice(0, options.value.length)
  const param = { idList: JSON.stringify(id), offset: 0, limit: 10 }
  list(param).then(res => {
    res.list.forEach(item => {
      options.value.push(item)
    })
  })
}

defineExpose({
  geometries,
})
</script>

<style scoped lang="less">
.flex {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
<style lang="less"></style>
