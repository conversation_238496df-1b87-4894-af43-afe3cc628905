<template>
  <el-row>
    <div style="width: 100%">
      <el-table :data="options" row-key="id" row-class-name="default-row" highlight-current-row @row-click="clickRow">
        <el-table-column prop="airspaceName" label="空域名称" :show-overflow-tooltip="true" width="300" />
        <el-table-column prop="airspaceType" label="空域类型" :show-overflow-tooltip="true" width="300" />
        <el-table-column prop="description" label="简介" :show-overflow-tooltip="true" />
      </el-table>
    </div>
  </el-row>
</template>

<script setup>
import { planList as list } from '@/request/api/airspace.js'
const { id = [] } = defineProps({
  id: {
    type: Array,
  },
})
const options = ref([])

const airSpaceObj = ref({})

onMounted(() => {
  nextTick(() => {
    if (id) query(id)
  })
})

const clickRow = e => {
  airSpaceObj.value = e
}

const query = id => {
  options.value.splice(0, options.value.length)
  const param = { idList: JSON.stringify(id) }
  list(param).then(res => {
    res.list.forEach(item => {
      options.value.push(item)
    })
  })
}

defineExpose({
  airSpaceObj,
})
</script>

<style scoped lang="less">
.flex {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
<style lang="less"></style>
