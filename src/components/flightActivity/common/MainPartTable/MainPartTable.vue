<template>
  <el-row>
    <div style="width: 100%">
      <el-table :data="options" row-key="id" row-class-name="default-row">
        <el-table-column prop="type" label="类型" :show-overflow-tooltip="true" width="160">
          <template #default="scope">
            {{ scope.row.accountType === 'PERSONAL' ? '个人' : '企业' }}
          </template>
        </el-table-column>
        <el-table-column prop="userName" label="名称" :show-overflow-tooltip="true" width="360" />
        <el-table-column prop="phone" label="电话" :show-overflow-tooltip="true" width="240" />
        <el-table-column prop="cardNum" label="身份证/社会统一信用码" :show-overflow-tooltip="true" width="360">
          <template #default="scope">
            {{ scope.row.cardNum || scope.row.uscCode }}
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" :show-overflow-tooltip="true" width="260" />
        <el-table-column prop="contactAddr1" label="地址" :show-overflow-tooltip="true" />
      </el-table>
    </div>
  </el-row>
</template>

<script setup>
import { list } from '@/request/api/user.js'
const { id = null } = defineProps({
  id: {
    type: String,
  },
})
const options = ref([])

onMounted(() => {
  nextTick(() => {
    if (id) query(id)
  })
})

const query = id => {
  options.value.splice(0, options.value.length)
  const param = { id: id, offset: 0, limit: 10 }
  list(param).then(res => {
    res.list.forEach(item => {
      options.value.push(item)
    })
  })
}
</script>

<style scoped lang="less">
.flex {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
<style lang="less"></style>
