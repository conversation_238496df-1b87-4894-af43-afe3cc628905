<template>
  <el-row>
    <div style="width: 100%">
      <el-table :data="options" row-key="id" row-class-name="default-row">
        <el-table-column prop="name" label="名称" :show-overflow-tooltip="true" />
        <el-table-column prop="phone" label="电话" :show-overflow-tooltip="true" />
        <el-table-column prop="cardNum" label="证件号码" :show-overflow-tooltip="true" />
        <el-table-column label="执照种类" :show-overflow-tooltip="true" width="220">
          <template #default="scope">
            {{ scope.row.licenseType.value }}
          </template>
        </el-table-column>
        <el-table-column prop="licenseNumber" label="执照编号" :show-overflow-tooltip="true" />
        <el-table-column label="超视距等级" :show-overflow-tooltip="true" width="200">
          <template #default="scope">
            {{ scope.row.beyondVisualLevel.value }}
          </template>
        </el-table-column>
        <el-table-column label="类别等级" :show-overflow-tooltip="true" width="160">
          <template #default="scope">
            {{ scope.row.categoryLevel.value }}
          </template>
        </el-table-column>
        <el-table-column label="教员等级" :show-overflow-tooltip="true" width="160">
          <template #default="scope">
            {{ scope.row.categoryLevel.value }}
          </template>
        </el-table-column>
        <el-table-column label="级别等级" :show-overflow-tooltip="true" width="160">
          <template #default="scope">
            {{ scope.row.level.value }}
          </template>
        </el-table-column>
        <el-table-column prop="issueDate" label="签发日期" :show-overflow-tooltip="true" width="160">
          <template #default="scope">
            {{ (scope.row.issueDate || null) && formatDate(scope.row.issueDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="expirationDate" label="失效日期" :show-overflow-tooltip="true" width="160">
          <template #default="scope">
            {{ (scope.row.expirationDate || null) && formatDate(scope.row.expirationDate) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-row>
</template>

<script setup>
import { list } from '@/request/api/manipulators.js'
import { formatDate } from '@/utils/formatUtils.js'
const { id = [] } = defineProps({
  id: {
    type: Array,
  },
})
const options = ref([])

onMounted(() => {
  nextTick(() => {
    if (id) query(id)
  })
})

const query = id => {
  options.value.splice(0, options.value.length)
  const param = { idList: JSON.stringify(id), offset: 0, limit: 10 }
  list(param).then(res => {
    res.list.forEach(item => {
      options.value.push(item)
    })
  })
}
</script>

<style scoped lang="less">
.flex {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
<style lang="less"></style>
