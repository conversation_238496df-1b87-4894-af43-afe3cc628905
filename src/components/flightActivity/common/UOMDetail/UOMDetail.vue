<template>
  <div class="uom-form">
    <el-row class="row">
      <el-col :span="12">
        <span class="label">通信联络方法：</span>
        <span class="value">{{ form.communicationMethod }}</span>
      </el-col>
      <el-col :span="12">
        <span class="label">应急处置程序：</span>
        <span class="value">{{ form.emergencyProcedure }}</span>
      </el-col>
    </el-row>
    <el-row class="row">
      <el-col :span="24">
        <span class="label">起降备降场地：</span>
        <span class="value">{{ form.takeoffLandingSite }}</span>
      </el-col>
    </el-row>
    <el-row class="row">
      <el-col :span="24">
        <span class="label">特殊飞行保障需求：</span>
        <span class="value">{{ form.specialSupportRequirements }}</span>
      </el-col>
    </el-row>
    <el-row class="row">
      <el-col :span="24">
        <span class="label">通讯，导航和被监视能力：</span>
        <span class="value">{{ form.monitoringCapability }}</span>
      </el-col>
    </el-row>
    <el-row class="row">
      <el-col :span="24">
        <span class="label">飞行航线，高度，速度和空域范围，进出空域方法：</span>
        <span class="value">{{ form.flightMethod }}</span>
      </el-col>
    </el-row>
    <el-row class="row">
      <el-col :span="24">
        <span class="label">指挥控制链路无线电频率以带宽：</span>
        <span class="value">{{ form.cmdCtrlLink }}</span>
      </el-col>
    </el-row>
    <el-row class="row">
      <el-col :span="24">
        <span class="label">二次雷达应答机和有关自动监视设备代码：</span>
        <span class="value">{{ form.monitorDeviceCode }}</span>
      </el-col>
    </el-row>
    <el-row class="row">
      <el-col :span="24">
        <span class="label">其他必要信息：</span>
        <span class="value">{{ form.otherInfo }}</span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24"
        ><span class="label">是否涉及以下活动：</span>
        <el-checkbox-group v-model="form.involvedActivities" disabled>
          <el-checkbox value="relayFlightViaCommInternet" name="type">
            通过通信基站或者互联网进行无人驾驶航空器中继飞行
          </el-checkbox>
          <el-checkbox value="carryDangerousGoodsDrop" name="type">
            运载危险品或者投放物品(常规农用无人驾驶航空器作业飞行活动除外)
          </el-checkbox>
          <el-checkbox value="flyOverGatheredCrowds" name="type"> 飞越集会人群上空 </el-checkbox>
          <el-checkbox value="operateOnMovingVehicle" name="type"> 在移动的交通工具上操控无人驾驶航空器 </el-checkbox>
          <el-checkbox value="distributedOperationCluster" name="type"> 实施分布式操作或者集群飞行 </el-checkbox>
          <el-checkbox value="notInvolvedInActivities" name="type"> 不涉及以上活动 </el-checkbox>
        </el-checkbox-group>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
const { data = {} } = defineProps({
  data: {
    type: Object,
  },
})
const form = ref({})

watch(
  () => data,
  () => {
    if (data) {
      let involvedActivities = []
      if (data.involvedActivities)
        Object.keys(data.involvedActivities).forEach(key => {
          if (data.involvedActivities[key] === 1) involvedActivities.push(key)
        })
      form.value = {
        communicationMethod: data.communicationMethod,
        emergencyProcedure: data.emergencyProcedure,
        takeoffLandingSite: data.takeoffLandingSite,
        specialSupportRequirements: data.specialSupportRequirements,
        monitoringCapability: data.monitoringCapability,
        flightMethod: data.flightMethod,
        cmdCtrlLink: data.cmdCtrlLink,
        monitorDeviceCode: data.monitorDeviceCode,
        otherInfo: data.otherInfo,
        involvedActivities: involvedActivities,
      }
    }
  },
  {
    immediate: true,
  },
)
</script>

<style scoped lang="less">
.uom-form {
  .row {
    height: 100%;
    margin-bottom: 24px;

    .label {
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.9);
    }

    .value {
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.6);
    }
  }
}
</style>
