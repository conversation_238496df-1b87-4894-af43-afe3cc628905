<template>
  <el-row>
    <div style="width: 100%">
      <el-table :data="options" row-key="id" row-class-name="default-row">
        <el-table-column prop="sn" label="序列号" :show-overflow-tooltip="true" width="200" />
        <el-table-column prop="uavName" label="产品名称" :show-overflow-tooltip="true" width="200" />
        <el-table-column prop="uavModel" label="产品型号" :show-overflow-tooltip="true" width="300" />
        <el-table-column prop="uavManufacturer" label="生产厂商" :show-overflow-tooltip="true" width="300" />
        <el-table-column label="产品类别" :show-overflow-tooltip="true" width="200">
          <template #default="scope">
            {{ scope.row.uavType.name }}
          </template>
        </el-table-column>
        <el-table-column label="产品类型" :show-overflow-tooltip="true" width="180">
          <template #default="scope">
            {{ scope.row.uavType.value }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" :show-overflow-tooltip="true">
          <template #default="scope">
            {{ scope.row.createTime && formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-row>
</template>

<script setup>
import { list } from '@/request/api/uav.js'
import { formatDate } from '@/utils/formatUtils.js'
const { id = [] } = defineProps({
  id: {
    type: Array,
  },
})
const options = ref([])

onMounted(() => {
  nextTick(() => {
    if (id) query(id)
  })
})

const query = id => {
  options.value.splice(0, options.value.length)
  const param = { idList: JSON.stringify(id), offset: 0, limit: 10 }
  list(param).then(res => {
    res.list.forEach(item => {
      options.value.push(item)
    })
  })
}
</script>

<style scoped lang="less">
.flex {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
<style lang="less"></style>
