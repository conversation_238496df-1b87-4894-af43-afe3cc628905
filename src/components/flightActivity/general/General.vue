<template>
  <div class="custom">
    <div class="navigation">
      <div class="title">{{ paintTitle }}</div>
      <CommonTab ref="tabRef" :list="tabList" :default-index="0" />
    </div>

    <div class="search">
      <div class="card">
        <el-row>
          <el-col :span="6">
            <div class="line">
              <div class="label">名称</div>
              <div class="in">
                <el-input
                  v-model="tableConfig.search.airspaceOrAccountLike"
                  placeholder="输入空域或申请主体名称模糊查询"
                ></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">任务性质</div>
              <div class="in">
                <DictSelect :id="DictIdEnum.taskType" v-model="tableConfig.search.taskTypeId" :is-json="false" />
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">申请时间</div>
              <div class="in">
                <el-date-picker
                  v-model="tableConfig.search.createTime"
                  size="large"
                  type="daterange"
                  range-separator="-"
                  format="YYYY-MM-DD"
                  date-format="YYYY-MM-DD"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  style="width: 100%"
                />
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="operate">
              <div class="btn n" @click="search">查询</div>
              <div class="btn d" @click="reset">重置</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="content">
      <div class="card">
        <div class="filter"></div>
        <Table
          v-model:config="tableConfig"
          v-model:reload="tableConfigReload"
          @pass-plan="pass"
          @reject-plan="reject"
          @to-detail="toDetail"
        ></Table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance()
import * as common from '@/utils/common.js'
import * as api from '@/request/api'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const { size } = defineProps(['size'])
const { orgId } = useOrg()
const { setPath } = useComponent()
// 组件
import Table from '@/components/common/Table.vue'
const tabPosition = ref(null)
const tabList = ['全部', '待审批', '已通过', '已拒绝']
const tabRef = useTemplateRef('tabRef')

const queryStatus = computed(() => {
  switch (tabRef.value?.currentIndex) {
    case 1:
      return 3
    case 2:
      return 6
    case 3:
      return 4
    default:
      return null
  }
})
const tableConfig = reactive({
  select: true,
  pagination: true,
  active: true,
  api: 'flight_activity_list',
  timeSearch: null,
  operate: 2,
  search: {
    airspaceOrAccountLike: undefined,
    flightActivityTypeList: '["GENERAL", "IN_FIELD"]',
    taskTypeId: undefined,
    isApprovalQuery: '1',
    approvalOrgIdList: JSON.stringify([orgId.value]),
    createTime: CommonDatetimeRange,
    startCreateTime: GetTimeRange(CommonDatetimeRange)[0],
    endCreateTime: GetTimeRange(CommonDatetimeRange)[1],
    deleted: 0,
    offset: 0,
    limit: 10,
    statusList: "['3', '4', '6']",
  },
  column: [
    {
      prop: 'regionNames',
      label: '属地',
      width: 120,
      template: false,
      formatter: v => {
        return v.regionNames.join('、')
      },
    },
    {
      prop: 'orderNo',
      label: '单号',
      width: 200,
      template: true,
    },
    {
      prop: 'flightActivityName',
      label: '飞行活动名称',
      template: true,
    },
    {
      prop: 'airspaceNames',
      label: '空域名称',
      template: true,
    },
    {
      prop: 'flightActivityType',
      label: '飞行活动类型',
      template: false,
      formatter: v => {
        return FlightActivityTypeData.find(a => a.value === v.flightActivityType).label
      },
    },
    {
      prop: 'taskType',
      label: '任务性质',
      template: false,
      formatter: v => {
        return v.taskType?.value
      },
    },
    {
      prop: 'startTime',
      label: '开始时间',
      template: false,
      formatter: v => {
        return common.formatTime(v.startTime).t6
      },
    },
    {
      prop: 'endTime',
      label: '结束时间',
      template: false,
      formatter: v => {
        return common.formatTime(v.endTime).t6
      },
    },
    {
      prop: 'memo',
      label: '备注',
      template: true,
    },
    {
      prop: 'status',
      label: '状态',
      template: false,
      formatter: v => {
        if (v.status === -1) return '未提交'
        if (v.status === 0) return '取消'
        if (v.status === 3) return '待审批'
        if (v.status === 4) return '审批未通过'
        if (v.status === 5) return '审批中'
        if (v.status === 6) return '审批通过'
        if (v.status === 7) return '执行中'
        if (v.status === 8) return '完成'
        if (v.status === 9) return '用户撤销'
      },
    },
  ],
})
const tableConfigReload = ref(0)

// 标题
import useComponent, { PathType } from '@/hooks/useComponent.js'
import { CommonDatetimeRange, FlightActivityTypeData, GetTimeRange } from '@/data/Data.js'
import useOrg from '@/hooks/useOrg.js'
import DictSelect from '@/components/common/dictSelect/DictSelect.vue'
import { DictIdEnum } from '@/data/DictData.js'
import { flight_activity_update_status } from '@/request/api'
import CommonTab from '@/components/common/CommonTab/CommonTab.vue'
const { pathData } = useComponent()
const paintTitle = ref(pathData.value.name)

/** 查询 **/
const search = () => {
  if (queryStatus.value) {
    tableConfig.search.status = queryStatus.value
    delete tableConfig.search.statusList
  } else {
    tableConfig.search.statusList = "['3','4','6']"
    delete tableConfig.search.status
  }
  tableConfig.search.startCreateTime = GetTimeRange(tableConfig.search.createTime)[0]
  tableConfig.search.endCreateTime = GetTimeRange(tableConfig.search.createTime)[1]
  tableConfigReload.value = Math.random()
}

watch(
  () => tabRef.value?.currentIndex,
  () => {
    search()
  },
)
/** 重置 **/
const reset = () => {
  tableConfig.search.airspaceOrAccountLike = undefined
  tableConfig.search.flightActivityTypeList = '["GENERAL", "IN_FIELD"]'
  tableConfig.search.taskTypeId = undefined
  tableConfig.search.regionCodeList = JSON.stringify([cityCode.value])
  tableConfig.search.createTime = CommonDatetimeRange
  tableConfig.search.startCreateTime = GetTimeRange(tableConfig.search.createTime)[0]
  tableConfig.search.endCreateTime = GetTimeRange(tableConfig.search.createTime)[1]
  search()
}

const toDetail = o => setPath('flightActivity/general/Detail.vue', o, { type: PathType.detail })

const pass = o => {
  ElMessageBox.confirm('是否确定要同意审批当前活动申请', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    flight_activity_update_status({
      id: o.id,
      modifyTime: o.modifyTime,
      status: 6,
    }).then(() => {
      ElMessage.success('审批通过')
      search()
    })
  })
}
const reject = o => {
  ElMessageBox.prompt('是否确定要拒绝当前活动申请，确认请填写拒绝原因', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValidator: value => {
      // 如果值为空返回错误信息
      if (!value || value.trim() === '') {
        return '拒绝原因不能为空！'
      }
      // 返回true或undefined表示验证通过
      return true
    },
  }).then(({ value }) => {
    flight_activity_update_status({
      id: o.id,
      modifyTime: o.modifyTime,
      status: 4,
      approvalMemo: value,
    }).then(() => {
      ElMessage.success('审批拒绝')
      search()
    })
  })
}
</script>

<style>
@import '../common/custom.css';
</style>
