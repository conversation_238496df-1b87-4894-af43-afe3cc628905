export const FlightPlanModeData = {
  general: 'GENERAL',
  longTerm: 'LONG_TERM',
  inField: 'IN_FIELD',
  filing: 'FILING',
}

export const RegionTypeData = [
  {
    label: '省',
    value: 'PROVINCE',
  },
  {
    label: '跨省',
    value: 'CROSS_PROVINCE',
  },
  {
    label: '市',
    value: 'CITY',
  },
  {
    label: '跨市',
    value: 'CORSS_CITY',
  },
  {
    label: '区',
    value: 'DISTRICT',
  },
  {
    label: '跨区',
    value: 'CORSS_DISTRICT',
  },
]

export const FlightActivityTypeData = [
  {
    label: '一般飞行活动',
    value: FlightPlanModeData.general,
  },
  {
    label: '长期飞行活动',
    value: FlightPlanModeData.longTerm,
  },
  {
    label: '场内飞行活动',
    value: FlightPlanModeData.inField,
  },
  {
    label: '备案',
    value: FlightPlanModeData.filing,
  },
]

export const ControlModeData = [
  {
    label: '遥控飞行',
    value: 'REMOTE_CONTROL',
  },
  {
    label: '自主飞行',
    value: 'AUTONOMOUS',
  },
]

export const FlightModeData = [
  {
    label: '超视距飞行',
    value: 'BEYOND_VISUAL_LINE_OF_SIGHT',
  },
  {
    label: '视距内飞行',
    value: 'VISUAL_LINE_OF_SIGHT',
  },
]

export const FlightPlanLevelData = [
  {
    label: '常规',
    value: 0,
  },
  {
    label: '固定',
    value: 1,
  },
  {
    label: '紧急',
    value: 2,
  },
]

export const SourceTypeData = [
  {
    label: '本系统',
    value: 'SYSTEM',
  },
  {
    label: '省平台',
    value: 'SPT',
  },
  {
    label: '信产',
    value: 'XC',
  },
]

export const ActivityAirspaceTypeData = [
  {
    label: '空域',
    value: 1,
  },
  {
    label: '航线',
    value: 2,
  },
]
