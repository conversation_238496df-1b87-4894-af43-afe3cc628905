<template>
  <div class="detail-map">
    <div id="detailMap"></div>
  </div>
</template>

<script setup>
import useEditMap from '@/hooks/useEditMap.js'

const mapData = defineModel('map-data')
const { type } = defineProps(['type'])
useEditMap('detailMap', mapData, type)
</script>

<style scoped lang="less">
.detail-map {
  width: 100%;
  height: 100%;
  border-radius: 12px 0 0 12px;
  overflow: clip;
  position: relative;
}
</style>
