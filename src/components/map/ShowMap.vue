<template>
  <div class="show-map">
    <div id="showMap"></div>
  </div>
</template>

<script setup>
import useShowMap from '@/hooks/useShowMap.js'
const mapData = defineModel('map-data')
onActivated(() => useShowMap('showMap', mapData))
useShowMap('showMap', mapData)
</script>

<style scoped lang="less">
.show-map {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: clip;
  position: relative;
}
</style>
