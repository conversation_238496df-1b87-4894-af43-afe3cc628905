<template>
  <div class="restricted-management">
    <CommonTitle title="临时管制区">
      <template #default>
        <el-tabs v-model="activeTab" class="restricted-tabs">
          <el-tab-pane label="审批通过" name="RESTRICTED" />
          <el-tab-pane label="审批" name="RESTRICTED_APPROVAL" />
        </el-tabs>
      </template>
    </CommonTitle>

    <!-- 动态组件渲染区域 -->
    <div class="restricted-content">
      <component :is="currentComponent" v-bind="componentProps" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, markRaw } from 'vue'
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import Restricted from './Restricted.vue'
import AirSpaceApproval from '@/components/airSpaceManagement/airspaceApproval/AirspaceApproval.vue'
import useComponent from '@/hooks/useComponent.js'
const { getParam } = useComponent()

// 当前激活的标签页
const activeTab = ref('RESTRICTED')

// 组件映射
const componentMap = {
  RESTRICTED: markRaw(Restricted),
  RESTRICTED_APPROVAL: markRaw(AirSpaceApproval),
}

const currentComponent = computed(() => componentMap[activeTab.value] || markRaw(Restricted))

const componentProps = computed(() => {
  switch (activeTab.value) {
    case 'RESTRICTED':
      return { showTitle: false }
    case 'RESTRICTED_APPROVAL':
      return { showTitle: true, category: 'CONTROLLED', source: 'TEMP_DELIMITATION', temporary: activeTab.value }
    default:
      return {}
  }
})

// 组件挂载时的初始化
onMounted(() => (activeTab.value = getParam()?.data?.temporary || 'RESTRICTED'))

// 暴露给父组件的方法
defineExpose({
  // 获取当前激活的标签页
  getCurrentTab: () => activeTab.value,
  // 刷新当前组件
  refreshCurrentComponent: () => {
    // 通过改变 key 来强制重新渲染组件
    const currentKey = activeTab.value
    activeTab.value = ''
    nextTick(() => {
      activeTab.value = currentKey
    })
  },
})
</script>

<style scoped lang="less">
.restricted-management {
  width: 100%;
  height: 100%;
}

// 自定义标签页样式
.restricted-tabs {
  // 移除默认的下边框
  :deep(.el-tabs__header) {
    margin: 0;
    border-bottom: none;
  }

  // 标签页样式
  :deep(.el-tabs__item) {
    width: 80px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-regular);
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    margin-right: 8px;
    background-color: var(--el-fill-color-blank);
    transition: all 0.3s ease;
    padding: 0;

    &:hover {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary-light-7);
      background-color: var(--el-color-primary-light-9);
    }

    &.is-active {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
      font-weight: 600;
    }
  }

  // 隐藏默认的激活指示器
  :deep(.el-tabs__active-bar) {
    display: none;
  }

  // 标签页导航容器
  :deep(.el-tabs__nav-wrap) {
    &::after {
      display: none;
    }
  }
}
</style>
