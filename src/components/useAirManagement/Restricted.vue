<template>
  <div class="airspace-design">
    <CommonForm v-model="searchForm" :form-items="formItems" @query="handleSearch" @reset="handleReset">
      <template #cityCodeList>
        <AreaSelect v-model="searchForm.cityCodeList" />
      </template>
      <template #status>
        <StatusSelect v-model="searchForm.statusInfo" />
      </template>
      <template #timeRange>
        <DateRange
          v-model="searchForm.validity"
          :default-props="{
            type: 'datetimerange',
            format: 'YYYY-MM-DD HH:mm',
            valueFormat: 'YYYYMMDDHHmm',
            clearable: false,
            rangeSeparator: '至',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
          }"
        />
      </template>

      <template #tagSelection>
        <TagSelect ref="tagSelectRef" v-model="tagSelection" dic-type="空域标签" />
      </template>
    </CommonForm>

    <CommonTable
      v-model:page="pagination.page"
      v-model:page-size="pagination.limit"
      :loading="state.loading"
      :data="currentLevel?.airspaces || []"
      :columns="tableColumns"
      :subtract-height="6"
      :show-pagination="true"
      :total="pagination.total"
      @pagination-change="handlePaginationChange"
      @action-click="handleActionClick"
    >
      <template #toolbar-left>
        <el-tabs v-model="state.activeLevelIndex" :lazy="false">
          <el-tab-pane
            v-for="level in state.levels"
            :key="level.index"
            :lazy="false"
            :label="level.label"
            :name="String(level.index)"
          />
        </el-tabs>
      </template>
      <!-- <template #toolbar-right>
        <el-button type="primary" :disabled="!searchForm.airspaceCategory" @click="addAirspace">新增</el-button>
      </template> -->
    </CommonTable>
  </div>
</template>

<script setup>
import { reactive, computed, watch, ref, defineProps } from 'vue'
import * as api from '@/request/api/index'
import { ElMessageBox, ElMessage } from 'element-plus'
import CommonForm from '@/components/common/CommonForm/CommonForm.vue'
import TagSelect from '@/components/common/tagSelect/TagSelect.vue'
import DateRange from '@/components/common/dateRange/DateRange.vue'
import { airspaceFormItems, airspaceTableColumns } from './config.js'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import useFlightLevel from '@/hooks/useFlightLevel.js'
import StatusSelect from '../common/statusSelect/StatusSelect.vue'
import CommonTable from '@/components/common/commonTable/CommonTable.vue'
import AreaSelect from '@/components/common/areaSelect/AreaSelect.vue'
import { checkBeforeDelete } from '@/utils/helper.js'
import { approvalList } from '@/request/api/airspace.js'

const { getParam, setPath } = useComponent()

const props = defineProps({
  category: {
    type: String,
    default: '',
  },
  source: {
    type: String,
    default: '',
  },
  temporary: {
    type: String,
    default: '',
  },
})

const formItems = ref(airspaceFormItems)

const searchForm = reactive({
  airspaceCategory: 'CONTROLLED',
  validity: {},
  statusInfo: null,
  cityCodeList: [],
})

const tableColumns = ref(airspaceTableColumns)

const tagSelectRef = ref(null)
const tagSelection = ref({
  matchAllTagList: [],
  matchAnyTagList: [],
})
const levels = useFlightLevel()

const state = reactive({
  levels: levels,
  activeLevelIndex: getParam()?.data?.activeLevelIndex || '0',
  loading: false,
})

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0,
})

const activeLevelIndexNumber = computed(() => parseInt(state.activeLevelIndex, 10))
const currentLevel = computed(() => {
  if (state.activeLevelIndex < 0 || !state.levels[activeLevelIndexNumber.value]) {
    return null
  }
  return state.levels[activeLevelIndexNumber.value]
})

const loadAirspacesForLevel = async level => {
  if (!level) return
  try {
    state.loading = true
    const { list, total } = await fetchAggregateList(level.id)
    level.airspaces = list
    pagination.total = total
  } finally {
    state.loading = false
  }
}

const fetchAggregateList = async flightLevelId => {
  const { statusInfo, ...rest } = searchForm
  const params = {
    ...rest,
    flightLevelId: flightLevelId,
    cityCodeList: JSON.stringify(searchForm.cityCodeList),
    airspaceCategoryList: JSON.stringify([searchForm.airspaceCategory]),
    matchAllAirspaceTagList: JSON.stringify(tagSelection.value.matchAllTagList),
    matchAnyAirspaceTagList: JSON.stringify(tagSelection.value.matchAnyTagList),
    source: 'TEMP_DELIMITATION',
    deleted: 0,
    offset: (pagination.page - 1) * pagination.limit,
    limit: pagination.limit,
    status: statusInfo?.status,
    runningStatus: statusInfo?.runningStatus,
  }

  if (searchForm.validity?.startTime && searchForm.validity?.endTime) {
    params.startTime = searchForm.validity.startTime
    params.endTime = searchForm.validity.endTime
  }

  const response = await api.airspace_aggregate_list(params)

  return {
    list: response.list,
    total: response.total || 0,
  }
}

const handleActionClick = async ({ action, row }) => {
  if (action.code === 'delete') {
    // 删除
    deleteAirspace(row)
  }
  if (action.code === 'view') {
    approvalList({ approval_subjectId: row.id, offset: 0, limit: 1 }).then(res => {
      if (res.list?.length) {
        // 查看详情
        setPath?.(
          'airSpaceManagement/airspaceApplication/detail/AirspaceApplicationEdit.vue',
          { id: res.list[0].id, mode: 'view', temporary: props.temporary },
          { name: '临时管制区详情', type: PathType.normal },
        )
      }
    })
  }
}

const deleteAirspace = airspace => {
  const hasNoDefaultRule = checkBeforeDelete(airspace)
  const message = hasNoDefaultRule
    ? '当前管制区空域存在非默认规则，删除后将无法恢复，是否确定删除？'
    : '是否确定删除当前管制区空域？'
  ElMessageBox.confirm(message, '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.airspace_delete({ idList: JSON.stringify([airspace.id]) })
    ElMessage.success('删除成功')
    if (currentLevel.value) {
      await loadAirspacesForLevel(currentLevel.value)
    }
  })
}

const handlePaginationChange = () => {
  loadAirspacesForLevel(currentLevel.value)
}

const handleSearch = () => {
  pagination.page = 1
  if (currentLevel.value) {
    loadAirspacesForLevel(currentLevel.value)
  }
}

const handleReset = () => {
  searchForm.validity = {}
  searchForm.statusInfo = null
  tagSelectRef.value?.clearSelection()
  handleSearch()
}

watch(currentLevel, async newLevel => {
  if (newLevel) {
    pagination.page = 1
    await loadAirspacesForLevel(newLevel)
  }
})

onMounted(() => {
  loadAirspacesForLevel(currentLevel.value)
})
</script>

<style scoped lang="less">
.common-table {
  padding-top: 0;
}
.airspace-design {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
  :deep(.el-tabs__nav-wrap:after) {
    height: 0;
  }
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: #e8e8e8;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__nav-scroll) {
  padding: 16px 0;
}
</style>
