import { mapStatusToText } from '@/utils/helper.js'
const commonStatusColumn = {
  prop: 'status',
  label: '状态',
  minWidth: 100,
  type: 'tag',
  tagText: row => {
    if (row.status === 2) return '停用'
    return mapStatusToText(row.runningStatus, 'poi_runningStatus', '-')
  },
  tagTypeMap: { 1: 'success', 2: 'danger', RUNNING: 'success', PLANNING: 'warning' },
}

export const airspaceFormItems = [
  {
    type: 'input',
    prop: 'airspaceNameLike',
    label: '空域名称',
    placeholder: '请输入空域名称',
    span: 8,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'cityCodeList',
    prop: 'cityCodeList',
    label: '所属地区',
    span: 8,
  },
  {
    type: 'slot',
    slotName: 'status',
    prop: 'statusInfo',
    label: '状态',
    placeholder: '请选择状态',
    span: 8,
  },
  {
    type: 'slot',
    slotName: 'timeRange',
    label: '有效期',
    span: 8,
    prop: 'timeRange',
  },
  // {
  //   type: 'select',
  //   prop: 'source',
  //   label: '来源',
  //   placeholder: '请选择来源',
  //   span: 8,
  //   options: [
  //     { label: '空域划设', value: 'DELIMITATION' },
  //     { label: '常用空域', value: 'COMMON' },
  //   ],
  // },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 8,
  },
]

export const airspaceCategoryOptions = [
  { label: '适飞区域', value: 'SUITABLE' },
  { label: '管制区域', value: 'CONTROLLED' },
  { label: '禁飞区域', value: 'NO_FLY' },
  // { label: '协调军民航单位划设区域', value: 'COORDINATED_DELIMITATION' },
  // { label: '按需划设区域', value: 'ON_DEMAND' },
  // { label: '活动区域', value: 'ACTIVITY' },
  // { label: '协调军民航单位申请使用区域', value: 'COORDINATED_USAGE' },
  // { label: '军航活动区域', value: 'MILITARY' },
  // { label: '直升机场区域', value: 'HELIPORT' },
]

export const airspaceTableColumns = [
  { prop: 'airspaceName', label: '名称', minWidth: 120 },
  { prop: 'airspaceType', label: '空域类型', minWidth: 100 },
  { prop: 'cityName', label: '所属地区', minWidth: 100 },
  {
    prop: 'startTime',
    label: '开始时间',
    minWidth: 150,
    formatter: row => {
      if (row.startTime === -1) return '长期有效'
      // 将 yyyyMMddHHmm 格式转换为可读格式
      const timeStr = row.startTime.toString()
      if (timeStr.length === 12) {
        return `${timeStr.substring(0, 4)}-${timeStr.substring(4, 6)}-${timeStr.substring(6, 8)} ${timeStr.substring(8, 10)}:${timeStr.substring(10, 12)}`
      }
      return timeStr
    },
  },
  {
    prop: 'endTime',
    label: '结束时间',
    minWidth: 150,
    formatter: row => {
      if (row.endTime === -1) return '长期有效'
      // 将 yyyyMMddHHmm 格式转换为可读格式
      const timeStr = row.endTime.toString()
      if (timeStr.length === 12) {
        return `${timeStr.substring(0, 4)}-${timeStr.substring(4, 6)}-${timeStr.substring(6, 8)} ${timeStr.substring(8, 10)}:${timeStr.substring(10, 12)}`
      }
      return timeStr
    },
  },
  commonStatusColumn,
  {
    fixed: 'right',
    label: '操作',
    type: 'action',
    width: 180,
    actions: [
      {
        label: '编辑',
        type: 'primary',
        code: 'edit',
      },
      {
        label: '删除',
        type: 'danger',
        code: 'delete',
      },
    ],
  },
]
