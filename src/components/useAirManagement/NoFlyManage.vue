<template>
  <div class="airspace-design">
    <CommonTitle />
    <CommonForm
      v-model="searchForm"
      :form-items="formItems"
      :default-show-count="3"
      @query="handleSearch"
      @reset="handleReset"
    >
      <template #cityCodeList>
        <AreaSelect v-model="searchForm.cityCodeList" />
      </template>
      <template #status>
        <StatusSelect v-model="searchForm.statusInfo" />
      </template>
      <template #timeRange>
        <DateRange
          v-model="searchForm.validity"
          :default-props="{
            type: 'datetimerange',
            format: 'YYYY-MM-DD HH:mm',
            valueFormat: 'YYYYMMDDHHmm',
            clearable: true,
            rangeSeparator: '至',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
          }"
          :show-radio-group="true"
        />
      </template>

      <template #tagSelection>
        <TagSelect ref="tagSelectRef" v-model="tagSelection" dic-type="空域标签" />
      </template>
    </CommonForm>

    <main class="main-content">
      <div class="height-tabs">
        <el-tabs v-model="state.activeLevelIndex" :lazy="false">
          <el-tab-pane
            v-for="level in state.levels"
            :key="level.index"
            :lazy="false"
            :label="level.label"
            :name="String(level.index)"
          />
        </el-tabs>
        <el-button type="primary" :disabled="!searchForm.airspaceCategory" @click="addAirspace">新增</el-button>
      </div>
      <CommonTable
        v-model:page="pagination.page"
        v-model:limit="pagination.limit"
        :loading="state.loading"
        :data="currentLevel?.airspaces || []"
        :columns="tableColumns"
        :show-pagination="true"
        :total="pagination.total"
        @pagination-change="handlePaginationChange"
        @action-click="handleActionClick"
      >
      </CommonTable>
    </main>
  </div>
</template>

<script setup>
import { reactive, computed, watch, ref } from 'vue'
import * as api from '@/request/api/index'
import { ElMessageBox, ElMessage } from 'element-plus'
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import CommonForm from '@/components/common/CommonForm/CommonForm.vue'
import TagSelect from '@/components/common/tagSelect/TagSelect.vue'
import DateRange from '@/components/common/dateRange/DateRange.vue'
import { airspaceFormItems, airspaceTableColumns } from './config.js'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import useFlightLevel from '@/hooks/useFlightLevel.js'
import StatusSelect from '../common/statusSelect/StatusSelect.vue'
import CommonTable from '@/components/common/commonTable/CommonTable.vue'
import AreaSelect from '@/components/common/areaSelect/AreaSelect.vue'

const { setPath, getParam } = useComponent()

const formItems = ref(airspaceFormItems)

const searchForm = reactive({
  airspaceCategory: 'NO_FLY',
  validity: null,
  statusInfo: null,
  cityCodeList: [],
})

const tableColumns = ref(airspaceTableColumns)

const tagSelectRef = ref(null)
const tagSelection = ref({
  matchAllTagList: [],
  matchAnyTagList: [],
})
const levels = useFlightLevel()

const state = reactive({
  levels: levels,
  activeLevelIndex: getParam()?.data?.activeLevelIndex || '0',
  loading: false,
})

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0,
})

const activeLevelIndexNumber = computed(() => parseInt(state.activeLevelIndex, 10))
const currentLevel = computed(() => {
  if (state.activeLevelIndex < 0 || !state.levels[activeLevelIndexNumber.value]) {
    return null
  }
  return state.levels[activeLevelIndexNumber.value]
})

const loadAirspacesForLevel = async (level, page = 1, limit = 10) => {
  if (!level) return
  if (!searchForm.airspaceCategory) {
    level.airspaces = []
    pagination.total = 0
    return
  }
  try {
    state.loading = true
    const { list, total } = await fetchAggregateList(level.id, page, limit)
    level.airspaces = list
    pagination.total = total
  } finally {
    state.loading = false
  }
}

const fetchAggregateList = async (flightLevelId, page, limit) => {
  const { statusInfo, ...rest } = searchForm
  const params = {
    ...rest,
    flightLevelId: flightLevelId,
    cityCodeList: JSON.stringify(searchForm.cityCodeList),
    airspaceCategoryList: JSON.stringify([searchForm.airspaceCategory]),
    matchAllAirspaceTagList: JSON.stringify(tagSelection.value.matchAllTagList),
    matchAnyAirspaceTagList: JSON.stringify(tagSelection.value.matchAnyTagList),
    source: 'DELIMITATION',
    deleted: 0,
    offset: (page - 1) * limit,
    limit: limit,
    status: statusInfo?.status,
    runningStatus: statusInfo?.runningStatus,
  }

  if (searchForm.validity && searchForm.validity.beginTime) {
    params.beginTime = searchForm.validity.beginTime
    params.endTime = searchForm.validity.endTime
  }

  const response = await api.airspace_aggregate_list(params)
  return {
    list: response.list || [],
    total: response.total || 0,
  }
}

const addAirspace = () => {
  if (!currentLevel.value) {
    ElMessage.error('请先选择一个高度层')
    return
  }
  setPath(
    'airSpaceManagement/AirSpaceEdit.vue',
    {
      bottom: currentLevel.value.bottom,
      top: currentLevel.value.top,
      flightLevelId: currentLevel.value.id,
      activeLevelIndex: state.activeLevelIndex,
      airspaceCategory: searchForm.airspaceCategory,
    },
    { name: '新增禁飞区', type: PathType.add },
  )
}

const handleActionClick = async ({ action, row }) => {
  if (action.code === 'edit') {
    // 编辑
    editAirspace(row)
  } else if (action.code === 'delete') {
    // 删除
    deleteAirspace(row)
  }
}

const editAirspace = airspace => {
  setPath(
    'airSpaceManagement/AirSpaceEdit.vue',
    {
      id: airspace.id,
      bottom: currentLevel.value.bottom,
      top: currentLevel.value.top,
      flightLevelId: currentLevel.value.id,
      activeLevelIndex: state.activeLevelIndex,
      airspaceCategory: searchForm.airspaceCategory,
    },
    { name: '编辑禁飞区', type: PathType.edit },
  )
}

const deleteAirspace = airspace => {
  ElMessageBox.confirm('是否确定删除当前空域', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.airspace_aggregate_delete({ idList: JSON.stringify([airspace.id]) })
    ElMessage.success('删除成功')
    if (currentLevel.value) {
      await loadAirspacesForLevel(currentLevel.value, pagination.page, pagination.limit)
    }
  })
}

const handlePaginationChange = ({ page, limit }) => {
  pagination.page = page
  pagination.limit = limit
  loadAirspacesForLevel(currentLevel.value, page, limit)
}

const handleSearch = () => {
  pagination.page = 1
  if (currentLevel.value) {
    loadAirspacesForLevel(currentLevel.value, pagination.page, pagination.limit)
  }
}

const handleReset = () => {
  searchForm.validity = null
  searchForm.statusInfo = null
  tagSelectRef.value?.clearSelection()
  handleSearch()
}

watch(currentLevel, async newLevel => {
  if (newLevel) {
    pagination.page = 1
    await loadAirspacesForLevel(newLevel, pagination.page, pagination.limit)
  }
})

onMounted(() => {
  loadAirspacesForLevel(currentLevel.value, pagination.page, pagination.limit)
})
</script>

<style scoped lang="less">
.airspace-design {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
  :deep(.el-tabs__nav-wrap:after) {
    height: 0;
  }
}

.main-content {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-top: 0;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  min-height: 0;

  .height-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #e8e8e8;
    flex: 0 0 auto;
    background: #fff;
  }
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: #e8e8e8;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__nav-scroll) {
  padding: 16px 0;
}
</style>
