<template>
  <div class="no-fly-management">
    <CommonTitle title="临时禁飞区">
      <template #default>
        <el-tabs v-model="activeTab" class="no-fly-tabs" @tab-click="handleTabClick">
          <el-tab-pane label="审批通过" name="NO_FLY" />
          <el-tab-pane label="审批" name="AIR_SPACE_APPROVAL" />
        </el-tabs>
      </template>
    </CommonTitle>

    <!-- 动态组件渲染区域 -->
    <div class="no-fly-content">
      <component :is="currentComponent" :key="activeTab" v-bind="componentProps" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, markRaw } from 'vue'
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import NoFly from './NoFly.vue'
import AirSpaceApproval from '@/components/airSpaceManagement/airspaceApproval/AirspaceApproval.vue'
import useComponent from '@/hooks/useComponent.js'
const { getParam } = useComponent()

// 当前激活的标签页
const activeTab = ref('NO_FLY')

// 组件映射
const componentMap = {
  NO_FLY: markRaw(NoFly),
  AIR_SPACE_APPROVAL: markRaw(AirSpaceApproval),
}

// 当前渲染的组件
const currentComponent = computed(() => componentMap[activeTab.value] || NoFly)

// 标签页切换事件处理
const handleTabClick = tab => {
  activeTab.value = tab.name
}

const componentProps = computed(() => {
  switch (activeTab.value) {
    case 'NO_FLY':
      return { showTitle: false }
    case 'AIR_SPACE_APPROVAL':
      return { showTitle: true, category: 'NO_FLY', source: 'TEMP_DELIMITATION' }
    default:
      return {}
  }
})

// 组件挂载时的初始化
onMounted(() => {
  activeTab.value = getParam()?.data?.tab
})

// 暴露给父组件的方法
defineExpose({
  // 获取当前激活的标签页
  getCurrentTab: () => activeTab.value,

  // 刷新当前组件
  refreshCurrentComponent: () => {
    // 通过改变 key 来强制重新渲染组件
    const currentKey = activeTab.value
    activeTab.value = ''
    nextTick(() => {
      activeTab.value = currentKey
    })
  },
})
</script>

<style scoped lang="less">
.no-fly-management {
  width: 100%;
  height: 100%;
}

// 自定义标签页样式
.no-fly-tabs {
  // 移除默认的下边框
  :deep(.el-tabs__header) {
    margin: 0;
    border-bottom: none;
  }

  // 标签页样式
  :deep(.el-tabs__item) {
    width: 80px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-regular);
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    margin-right: 8px;
    background-color: var(--el-fill-color-blank);
    transition: all 0.3s ease;
    padding: 0;

    &:hover {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary-light-7);
      background-color: var(--el-color-primary-light-9);
    }

    &.is-active {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
      font-weight: 600;
    }
  }

  // 隐藏默认的激活指示器
  :deep(.el-tabs__active-bar) {
    display: none;
  }

  // 标签页导航容器
  :deep(.el-tabs__nav-wrap) {
    &::after {
      display: none;
    }
  }
}
</style>
