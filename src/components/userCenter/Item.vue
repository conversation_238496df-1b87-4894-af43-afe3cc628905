<template>
  <div>
    <div v-if="!show" style="width: 400px; font-size: var(--el-font-size-large)">
      {{ value }}
    </div>
    <el-input v-if="show" ref="inputRef" v-model="value" />
  </div>
  <div v-if="!disabled" style="margin-left: 12px">
    <el-button v-if="!show" type="primary" @click="show = true">修改</el-button>
    <el-button v-if="show" @click="emit('cancel', type)">取消</el-button>
    <el-button v-if="show && originalValue !== value" type="primary" @click="submit">确认</el-button>
  </div>
</template>

<script setup>
import { ElNotification } from 'element-plus'
import { validEmail, validPhone } from '@/utils/validate.js'

const show = defineModel('show')
const value = defineModel('value')
const {
  type = null,
  originalValue = null,
  disabled = false,
} = defineProps({
  type: {
    type: String,
  },
  originalValue: {
    type: String,
  },
  disabled: {
    type: Boolean,
  },
})

const inputRef = useTemplateRef('inputRef')

const emit = defineEmits(['cancel', 'updateUserInfo'])
const submit = () => {
  if (type === 'mobile') {
    if (!validPhone(value.value)) {
      ElNotification.error({ title: '请输入正确的手机号' })
      emit('cancel')
      return
    }
  }
  if (type === 'email') {
    if (!validEmail(value.value)) {
      ElNotification.error({ title: '请输入正确的邮箱' })
      emit('cancel', type, true)
      return
    }
  }
  emit('updateUserInfo')
  show.value = false
}
</script>

<style scoped lang="less"></style>
