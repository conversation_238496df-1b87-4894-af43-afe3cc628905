<template>
  <div class="user-center">
    <CommonTitle />
    <div class="common-container" style="height: calc(100% - 48px - 48px)">
      <el-form style="height: 100%">
        <CommonCard style="height: 100%">
          <el-form-item label-width="80" label="登录名：">
            <Item
              v-model:show="showInfo.loginName"
              v-model:value="form.loginName"
              :disabled="true"
              :original-value="originalData.loginName"
              type="loginName"
            />
          </el-form-item>
          <el-form-item label-width="80" label="用户名：">
            <Item
              v-model:show="showInfo.userName"
              v-model:value="form.userName"
              :original-value="originalData.userName"
              type="userName"
              @update-user-info="updateUserInfo('userName')"
              @cancel="cancel"
            />
          </el-form-item>
          <el-form-item label-width="80" label="手机号：">
            <Item
              v-model:show="showInfo.mobile"
              v-model:value="form.mobile"
              :original-value="originalData.mobile"
              type="mobile"
              @update-user-info="updateUserInfo('mobile')"
              @cancel="cancel"
            />
          </el-form-item>
          <el-form-item label-width="80" label="邮箱：">
            <Item
              v-model:show="showInfo.email"
              v-model:value="form.email"
              :original-value="originalData.email"
              type="email"
              @update-user-info="updateUserInfo('email')"
              @cancel="cancel"
            />
          </el-form-item>
          <el-form-item label-width="80" label="密码：">
            <PasswordItem
              v-model:show="showInfo.password"
              v-model:value="passwordForm.password"
              v-model:new-password="passwordForm.newPassword"
              @update-password="updatePassword()"
              @cancel-password="cancelPassword()"
            />
          </el-form-item>
        </CommonCard>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import CommonTitle from '@/components/common/commonTitle/CommonTitle.vue'
import CommonCard from '@/components/common/commonCard/CommonCard.vue'
import { changePassword, getUser, updateUser } from '@/request/api/user.js'
import Item from '@/components/userCenter/Item.vue'
import { ElMessage } from 'element-plus'
import useUser from '@/store/user.js'
import PasswordItem from '@/components/userCenter/PasswordItem.vue'
import { hexToBase64 } from '@/utils/helper.js'
import { sm3 } from 'sm-crypto'

const { size = {} } = defineProps({
  size: Object,
})

const userRouter = useRouter()
const user = useUser()

const form = ref({})
const originalData = ref({})
const passwordForm = ref({})

const showInfo = ref({
  userName: false,
  mobile: false,
  email: false,
  password: false,
})

const getUserInfo = () => {
  getUser().then(res => {
    form.value = res.user
    passwordForm.value.password = user.getPassword()
    passwordForm.value.originalPassword = user.getPassword()
    originalData.value = { ...form.value }
  })
}

const updateUserInfo = type => {
  const data = { ...originalData.value }
  data[type] = form.value[type]
  delete data.password
  delete data.loginName
  updateUser(data)
    .then(() => {
      ElMessage({
        message: '修改成功',
        type: 'success',
      })
      originalData.value[type] = data[type]
      showInfo.value[type] = false
      if (type === 'userName') user.setUserInfoName(originalData.value[type])
    })
    .catch(() => {
      cancel(type)
    })
}

const updatePassword = () => {
  const data = {
    password: hexToBase64(sm3(passwordForm.value.password.trim())),
    newPassword: hexToBase64(sm3(passwordForm.value.newPassword.trim())),
  }
  changePassword(data)
    .then(() => {
      ElMessage({
        message: '修改成功',
        type: 'success',
      })
      setTimeout(() => {
        user.clearUserInfo()
        userRouter.replace('/login')
      }, 200)
    })
    .catch(() => {
      cancelPassword()
    })
}

const cancel = (type, show = false) => {
  form.value[type] = originalData.value[type]
  showInfo.value[type] = show
}

const cancelPassword = () => {
  passwordForm.value.password = passwordForm.value.originalPassword
  showInfo.value.password = false
}

getUserInfo()
</script>

<style scoped lang="less">
.user-center {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
}
</style>
