<template>
  <div style="width: 400px; font-size: var(--el-font-size-large)">
    <div v-if="!show">******</div>
    <el-input
      v-if="show"
      ref="oldPasswordRef"
      v-model="form.oldPassword"
      type="password"
      placeholder="请输入原密码"
      style="margin-bottom: 8px"
    />
    <el-input
      v-if="show"
      ref="newPasswordRef"
      v-model="form.newPassword"
      type="password"
      placeholder="请输入新密码"
      style="margin-bottom: 8px"
    />
    <el-input
      v-if="show"
      ref="checkPasswordRef"
      v-model="form.checkPassword"
      type="password"
      placeholder="请确认输入新密码"
    />
  </div>
  <div style="margin-left: 12px">
    <el-button v-if="!show" type="primary" @click="show = true">修改</el-button>
    <el-button v-if="show" @click="emit('cancelPassword')">取消</el-button>
    <el-button v-if="show && form.oldPassword && form.newPassword && form.checkPassword" type="primary" @click="submit"
      >确认</el-button
    >
  </div>
</template>

<script setup>
import { ElMessageBox, ElNotification } from 'element-plus'
import { validPassword } from '@/utils/validate.js'

const show = defineModel('show')
const value = defineModel('value')
const newPassword = defineModel('newPassword')
const form = ref({
  oldPassword: undefined,
  newPassword: undefined,
  checkPassword: undefined,
})

const oldPasswordRef = useTemplateRef('oldPasswordRef')
const newPasswordRef = useTemplateRef('newPasswordRef')
const checkPasswordRef = useTemplateRef('checkPasswordRef')

const emit = defineEmits(['cancelPassword', 'updatePassword'])
const submit = () => {
  if (!form.value.oldPassword) {
    ElNotification.error({ title: '请输入原密码' })
    oldPasswordRef.value.focus()
    return
  }
  if (form.value.oldPassword !== value.value) {
    ElNotification.error({ title: '原密码错误，修改失败' })
    oldPasswordRef.value.focus()
    return
  }
  if (!form.value.newPassword) {
    ElNotification.error({ title: '请输入新密码' })
    newPasswordRef.value.focus()
    return
  }
  if (!form.value.checkPassword) {
    ElNotification.error({ title: '请输入确认新密码' })
    checkPasswordRef.value.focus()
    return
  } else {
    const errorMsg = validPassword(form.value.newPassword)
    if (errorMsg) {
      ElNotification.error({ title: errorMsg })
      checkPasswordRef.value.focus()
      return
    }
  }
  if (form.value.newPassword !== form.value.checkPassword) {
    ElNotification.error({ title: '新密码以及确认密码不一致，请重新输入' })
    checkPasswordRef.value.focus()
    return
  }
  ElMessageBox.confirm('是否确定要修改当前用户密码?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    newPassword.value = form.value.newPassword
    emit('updatePassword')
    show.value = false
  })
}
</script>

<style scoped lang="less"></style>
