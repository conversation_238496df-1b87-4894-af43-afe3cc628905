<template>
  <div v-loading="formContent.loading" class="detail">
    <!-- 标题-->
    <div class="navigation">
      <CommonTitle is-back>
        <template #default>
          <div class="opearte">
            <el-button type="primary" @click="submit">提交</el-button>
          </div>
        </template>
      </CommonTitle>
    </div>

    <div class="paint">
      <div class="card">
        <el-row class="flex-row">
          <el-col :span="18" class="flex-col">
            <div class="map-area">
              <EditMap v-model:map-data="mapData" :type="MapType.PLANE" />
            </div>
          </el-col>
          <el-col :span="6" class="flex-col">
            <div class="piece">
              <el-tabs v-model="airSpaceEditTab" class="demo-tabs" @tab-click="airSpaceEditTabShift">
                <el-tab-pane label="基本信息" name="1">
                  <div class="scroll">
                    <el-form ref="formRef" :model="formContent.param" :rules="formRules" label-position="top">
                      <el-form-item label="空域名称" prop="airspaceName">
                        <el-input v-model="formContent.param.airspaceName" placeholder="请输入1~16位字数" />
                      </el-form-item>

                      <el-form-item label="高度范围">
                        <AirSpaceType
                          v-model:bot="formContent.param.bot"
                          v-model:top="formContent.param.top"
                          v-model:type="formContent.param.airspaceType"
                        />
                      </el-form-item>

                      <el-form-item label="来源" prop="source">
                        <el-select v-model="formContent.param.source" placeholder="请选择来源" style="width: 100%">
                          <el-option label="空域划设" value="DELIMITATION" />
                          <el-option label="用户空域" value="USER" />
                          <el-option label="常用空域" value="COMMON" />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="空域标签" prop="airspaceTags">
                        <!-- <TagSelect
                          v-model="formContent.param.airspaceTags"
                          :show-match-mode="false"
                          :use-json-format="true"
                        /> -->
                        <TypeTags v-model="formContent.param.airspaceTags" :options="defaultTags" />
                      </el-form-item>

                      <!-- <el-form-item label="基准规则" prop="rule_id">
                        <el-select v-model="formContent.param.rule_id" placeholder="请选择" style="width: 100%">
                          <el-option
                            v-for="item in baseRestrict.g"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item> -->

                      <el-form-item label="有效期开始" prop="startTime">
                        <el-date-picker
                          v-model="formContent.param.startTime"
                          type="datetime"
                          placeholder="请选择开始时间"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="x"
                          style="width: 100%"
                        />
                      </el-form-item>

                      <el-form-item label="有效期结束" prop="endTime">
                        <el-date-picker
                          v-model="formContent.param.endTime"
                          type="datetime"
                          placeholder="请选择结束时间"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="x"
                          style="width: 100%"
                        />
                      </el-form-item>

                      <el-form-item label="属地代码列表" prop="regionCodes">
                        <el-input v-model="formContent.param.regionCodes" placeholder="请输入属地代码" />
                      </el-form-item>

                      <el-form-item label="区域类型" prop="regionType">
                        <el-select
                          v-model="formContent.param.regionType"
                          placeholder="请选择区域类型"
                          style="width: 100%"
                        >
                          <el-option label="省" value="PROVINCE" />
                          <el-option label="跨省" value="CROSS_PROVINCE" />
                          <el-option label="市" value="CITY" />
                          <el-option label="跨市" value="CORSS_CITY" />
                          <el-option label="区" value="DISTRICT" />
                          <el-option label="跨区" value="CORSS_DISTRICT" />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="状态" prop="status">
                        <el-select v-model="formContent.param.status" placeholder="请选择状态" style="width: 100%">
                          <el-option label="启用" value="1" />
                          <el-option label="停用" value="2" />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="运行状态" prop="runningStatus">
                        <el-select
                          v-model="formContent.param.runningStatus"
                          placeholder="请选择运行状态"
                          style="width: 100%"
                        >
                          <el-option label="运行中" value="RUNNING" />
                          <el-option label="规划中" value="PLANNING" />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="空域简介" prop="description">
                        <el-input
                          v-model="formContent.param.description"
                          type="textarea"
                          placeholder="请输入"
                          :rows="3"
                        />
                      </el-form-item>

                      <el-form-item label="模型信息">
                        <ModelDetails v-model="mapData" />
                      </el-form-item>
                    </el-form>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="GeoJson" disabled name="2">
                  <div>
                    <el-input v-model="exportIn.json" type="textarea" :rows="26" />
                  </div>
                  <div class="subIn">
                    <div class="btn y" @click="subIn">导入</div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import * as api from '@/request/api/index.js'

// 组件导入
import EditMap from '@/components/map/EditMap.vue'
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import AirSpaceType from '@/components/common/AirSpaceType.vue'
import TypeTags from '@/components/common/typeTags/TypeTags.vue'
import ModelDetails from '@/components/common/modelDetails/ModelDetails.vue'
import { airspaceCategoryOptions } from './config.js'

import useOrg from '@/hooks/useOrg.js'
import { MapType } from '@/data/MapConstant.js'
const { orgId } = useOrg()

// 滚动高度
const { size } = defineProps(['size'])
const formHeight = computed(() => size.height - 132 + 'px')

const defaultTags = [
  { value: 'hospital', label: '医院' },
  { value: 'school', label: '学校' },
  { value: 'park', label: '公园' },
  { value: 'shopping', label: '商场' },
  { value: 'restaurant', label: '餐厅' },
]

const mapData = reactive({
  displayStyle: {},
  geometries: [],
  abnormalWidth: 0,
  geoJson: [],
})

watch(
  mapData,
  () => {
    console.log('mapData', mapData)
  },
  { deep: true },
)
// 跳转
const { getParam, setPathBack } = useComponent()

// 表单引用
const formRef = ref()

// 编辑切换
const airSpaceEditTab = ref('1')
const airSpaceEditTabShift = () => {}

/** 表单 **/
const formContent = reactive({
  param: {
    // 基本信息
    orgId,
    airspaceName: '',
    description: '',
    airspaceCategory: '',
    flightLevelIds: [],
    bot: 0,
    botInclude: 1,
    top: 0,
    topInclude: 1,
    source: '',
    airspaceType: '',
    startTime: null,
    endTime: null,
    regionCodes: '',
    regionType: '',
    runningStatus: '',
    status: '',
    displayStyle: JSON.stringify({ color: '#f7712f' }),
    airspaceTags: '[]',
    geometries: '[]',
    geoJson: '{}',
    // 规则相关
    rule_id: '',
  },
  bot: 0,
  top: 0,
  airspaceType: '',
  heightDisabled: true,
  loading: false,
  editType: 'add',
})

// 表单验证规则
const formRules = computed(() => ({
  // 必填字段验证
  airspaceName: [
    { required: true, message: '请输入空域名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  bot: [
    { required: true, message: '请输入底高', trigger: 'blur' },
    { type: 'number', message: '底高必须为数字', trigger: 'blur' },
  ],
  botInclude: [{ required: true, message: '请选择是否包括底高', trigger: 'change' }],
  top: [
    { required: true, message: '请输入顶高', trigger: 'blur' },
    { type: 'number', message: '顶高必须为数字', trigger: 'blur' },
  ],
  topInclude: [{ required: true, message: '请选择是否包括顶高', trigger: 'change' }],
  source: [{ required: true, message: '请选择来源', trigger: 'change' }],
  airspaceType: [{ required: true, message: '请选择空域类型', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择有效期开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择有效期结束时间', trigger: 'change' }],
  regionCodes: [{ required: true, message: '请输入属地代码列表', trigger: 'blur' }],
  regionType: [{ required: true, message: '请选择区域类型', trigger: 'change' }],
  runningStatus: [{ required: true, message: '请选择运行状态', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  // 非必填字段验证
  description: [{ max: 2000, message: '描述不能超过2000个字符', trigger: 'blur' }],
}))
const layerKey = ref(null)
// 初始化
initData()
function initData() {
  formContent.param.bot = getParam().data.bottom || 0
  formContent.param.top = getParam().data.top || 0
  layerKey.value = getParam().data.layerKey
  formContent.param.airspaceCategory = getParam().data.airspaceCategory
  if (getParam().data.id) {
    formContent.editType = 'edit'
    searchAirspace(getParam().data.id)
  } else {
    formContent.editType = 'add'
    formContent.param.flightLevelIds = [getParam().data.flightLevelId]
  }
}

function searchAirspace(id) {
  let p = {
    id: id,
    offset: 0,
    limit: 10,
  }
  api.airspace_aggregate_list(p).then(response => {
    const airspaceData = response.list
    if (airspaceData.length > 0) {
      const airspace = airspaceData[0]
      formContent.param.id = airspace.id
      formContent.param.airspaceName = airspace.airspaceName
      formContent.param.airspaceCategory = airspace.airspaceCategory
      formContent.param.bot = airspace.bot
      formContent.param.top = airspace.top
      formContent.param.botInclude = airspace.botInclude
      formContent.param.topInclude = airspace.topInclude
      formContent.param.source = airspace.source
      formContent.param.airspaceType = airspace.airspaceType
      formContent.param.startTime = airspace.startTime
      formContent.param.endTime = airspace.endTime
      formContent.param.regionCodes = airspace.regionCodes
      formContent.param.regionType = airspace.regionType
      formContent.param.runningStatus = airspace.runningStatus
      formContent.param.status = airspace.status
      formContent.param.description = airspace.description
      formContent.param.flightLevelIds = airspace.flightLevelIds
      formContent.param.airspaceTags = airspace.airspaceTags || '[]'
      if (airspace.ruleList && airspace.ruleList.length > 0) {
        formContent.param.rule_id = airspace.ruleList[0].id
      }
      if (airspace.geoJson) {
        mapData.geoJson = [airspace.geoJson]
      }
      if (airspace.geometries) {
        mapData.geometries = airspace.geometries
      }
    }
  })
}

// 监听地图数据变化，更新表单
watch(
  () => mapData.geometries,
  geometries => {
    if (geometries && geometries.length > 0) {
      formContent.param.geometries = JSON.stringify(geometries)
      formContent.param.geoJson = JSON.stringify(mapData.geoJson[0])
    }
  },
  { deep: true },
)

/** 编辑 **/
const baseRestrict = reactive({
  g: [
    {
      label: '适飞',
      value: 'QkYpHGsPg1lhQ_eEX8k3p',
    },
    {
      label: '管制',
      value: 'F6jRSCrVPdnGCGw_Phli9',
    },
    {
      label: '禁飞',
      value: 'iMfHwsO6sNYDGdLWzWZHH',
    },
  ],
  typeValue: '',
})

/** GeoJson - 导入 **/
const exportIn = reactive({
  json: '',
})

const subIn = () => {
  formContent.exportIn = true
  const parsedData = JSON.parse(exportIn.json)
  mapData.geometries = parsedData
}

// 提交
const submit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    let url = formContent.editType === 'add' ? 'airspace_aggregate_add' : 'airspace_aggregate_update'
    formContent.loading = true
    api[url]({ ...formContent.param, flightLevelIds: JSON.stringify(formContent.param.flightLevelIds) })
      .then(() => {
        ElMessage({ message: '保存成功', type: 'success' })
        setTimeout(() => {
          formContent.loading = false
          setPathBack()
        }, 1500)
      })
      .catch(error => {
        formContent.loading = false
      })
  } catch (error) {}
}
</script>

<style scoped lang="scss">
.detail {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  .navigation {
    flex: 0 0 auto;
  }
  .paint {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    .card {
      flex: 1 1 0;
      display: flex;
      flex-direction: row;
      height: 100%;
      .flex-row {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }
      .flex-col {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      .map-area {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }
      .piece {
        flex: 1 1 0;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px;
        padding-top: 0;
        background-color: #ffffff; /* 白色背景 */
        border-radius: 0 16px 16px 0; /* 头部圆角 */
        .scroll {
          flex: 1 1 0;
          overflow-y: auto;
          height: v-bind(formHeight);
          /* 隐藏滚动条但保持滚动功能 */
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */
          // 表单样式
          :deep(.el-form) {
            .el-form-item {
              margin-bottom: 20px;

              .el-form-item__label {
                font-weight: 500;
                color: #333;
                margin-bottom: 8px;
              }

              .el-input,
              .el-select,
              .el-textarea {
                width: 100%;
              }
            }
          }
        }
        .mbg {
          margin-bottom: 12px;
        }
        .pdr {
          padding-right: 12px;
        }
        .line {
          font-size: 16px;
          color: var(--main-font-color9);
          .title {
            position: relative;
            margin-bottom: 12px;
          }
          .title.w:after {
            position: absolute;
            content: '*';
            color: var(--el-color-danger);
            top: 2px;
            left: -12px;
          }
          .in {
            padding: 0 1px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .col4 {
              width: 40%;
            }
            .col6 {
              width: 60%;
            }
          }
          .note {
            height: 30px;
            line-height: 30px;
          }
        }
        // 模型信息表格样式
        .tb {
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          overflow: hidden;

          .th {
            height: 48px;
            background: #f5f7fa;
            box-sizing: border-box;
            color: #606266;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e4e7ed;

            .name {
              width: 50%;
              text-align: center;
              font-weight: 500;
            }
          }

          .td {
            height: 48px;
            text-align: center;
            line-height: 48px;
            color: #606266;
            border-bottom: 1px solid #e4e7ed;

            &:last-child {
              border-bottom: none;
            }
          }
        }

        // 地图信息样式
        .map-info {
          .map-info-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .map-info-label {
              font-weight: 500;
              color: #606266;
              min-width: 80px;
            }

            .map-info-value {
              color: #303133;
              flex: 1;
            }
          }
        }
      }
    }
  }
}
</style>
