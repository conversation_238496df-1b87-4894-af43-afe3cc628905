<template>
  <div v-loading="formContent.loading" class="detail">
    <!-- 标题-->
    <div class="navigation">
      <CommonTitle is-back>
        <template #default>
          <div class="opearte">
            <el-button type="primary" @click="submit">提交</el-button>
          </div>
        </template>
      </CommonTitle>
    </div>

    <div class="paint">
      <div class="card">
        <el-row class="flex-row">
          <el-col :span="18" class="flex-col">
            <div class="map-area">
              <EditMap v-model:map-data="mapData" :type="MapType.PLANE" />
            </div>
          </el-col>
          <el-col :span="6" class="flex-col">
            <div class="form-panel">
              <el-tabs v-model="airSpaceEditTab" class="demo-tabs" @tab-click="airSpaceEditTabShift">
                <el-tab-pane label="基本信息" name="1">
                  <div class="scroll">
                    <el-form ref="formRef" :model="formContent.param" :rules="formRules" label-position="top">
                      <el-form-item label="空域名称" prop="airspaceName">
                        <el-input v-model="formContent.param.airspaceName" placeholder="请输入1~16位字数" />
                      </el-form-item>

                      <el-form-item label="高度范围">
                        <AirSpaceType
                          v-model:bot="formContent.param.bot"
                          v-model:top="formContent.param.top"
                          v-model:type="formContent.param.airspaceType"
                          v-model:flight-level-ids="formContent.param.flightLevelIds"
                        />
                      </el-form-item>

                      <el-form-item label="来源" prop="source">
                        <el-select v-model="formContent.param.source" placeholder="请选择来源" style="width: 100%">
                          <el-option label="空域划设" value="DELIMITATION" />
                          <el-option label="常用空域" value="COMMON" />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="空域标签" prop="airspaceTags">
                        <!-- <TagSelect
                          v-model="formContent.param.airspaceTags"
                          :show-match-mode="false"
                          :use-json-format="true"
                        /> -->
                        <TypeTags v-model="formContent.param.airspaceTags" dic-type="空域标签" />
                      </el-form-item>
                      <el-switch
                        :model-value="formContent.param.startTime === '-1'"
                        active-text="长期有效"
                        inactive-text="指定时间"
                        style="margin-bottom: 12px"
                        @change="handleTermChange"
                      />
                      <el-form-item label="有效期开始" prop="startTime">
                        <el-date-picker
                          v-if="formContent.param.startTime !== '-1'"
                          v-model="formContent.param.startTime"
                          type="datetime"
                          placeholder="请选择开始时间"
                          format="YYYY-MM-DD HH:mm"
                          value-format="YYYYMMDDHHmm"
                          style="width: 100%"
                        />
                        <span v-else style="color: #909399; font-size: 14px">长期有效</span>
                      </el-form-item>

                      <el-form-item label="有效期结束" prop="endTime">
                        <el-date-picker
                          v-if="formContent.param.startTime !== '-1'"
                          v-model="formContent.param.endTime"
                          type="datetime"
                          placeholder="请选择结束时间"
                          format="YYYY-MM-DD HH:mm"
                          value-format="YYYYMMDDHHmm"
                          style="width: 100%"
                        />
                        <span v-else style="color: #909399; font-size: 14px">长期有效</span>
                      </el-form-item>

                      <el-form-item label="所属地区" prop="regionCodes">
                        <el-select
                          v-model="formContent.param.regionCodes"
                          placeholder="请选择所属地区"
                          style="width: 100%"
                          @change="handleRegionCodesChange"
                        >
                          <el-option
                            v-for="item in areaOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                            :disabled="isAreaOptionDisabled(item)"
                          />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="空域种类" prop="airspaceCategory">
                        <el-select
                          v-model="formContent.param.airspaceCategory"
                          placeholder="请选择空域种类"
                          style="width: 100%"
                          @change="handleAirspaceCategoryChange"
                        >
                          <el-option
                            v-for="item in airspaceCategoryOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="区域类型" prop="regionType">
                        <el-select
                          v-model="formContent.param.regionType"
                          placeholder="请选择区域类型"
                          style="width: 100%"
                        >
                          <el-option
                            v-for="item in regionTypeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="状态" prop="statusInfo">
                        <StatusSelect v-model="formContent.param.statusInfo" />
                      </el-form-item>

                      <el-form-item label="样式配置" prop="displayStyle">
                        <StyleConfigEditor v-model="formContent.param.displayStyle" />
                      </el-form-item>
                      <el-form-item label="规则" prop="rule_id">
                        <el-select
                          v-model="formContent.param.rule_id"
                          placeholder="请选择"
                          style="width: 100%"
                          clearable
                          @clear="formContent.param.rule_id = ''"
                        >
                          <el-option
                            v-for="item in ruleList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="空域简介" prop="description">
                        <el-input
                          v-model="formContent.param.description"
                          type="textarea"
                          placeholder="请输入"
                          :rows="3"
                        />
                      </el-form-item>

                      <el-form-item label="模型信息">
                        <ModelDetails ref="modelDetailsRef" v-model="mapData" @update:model-value="onMapDataUpdate" />
                      </el-form-item>
                    </el-form>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="GeoJson" disabled name="2">
                  <div>
                    <el-input v-model="exportIn.json" type="textarea" :rows="26" />
                  </div>
                  <div class="subIn">
                    <div class="btn y" @click="subIn">导入</div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import { useDictionary } from '@/hooks/useDictionary.js'
import * as api from '@/request/api/index.js'

// 组件导入
import EditMap from '@/components/map/EditMap.vue'
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import AirSpaceType from '@/components/common/AirSpaceType.vue'
import TypeTags from '@/components/common/typeTags/TypeTags.vue'
import ModelDetails from '@/components/common/modelDetails/ModelDetails.vue'
import StyleConfigEditor from '@/components/common/styleConfigEditor/StyleConfigEditor.vue'
import { airspaceCategoryOptions } from './config.js'
import StatusSelect from '@/components/common/statusSelect/StatusSelect.vue'

import useOrg from '@/hooks/useOrg.js'
import { useRule } from '@/hooks/useRule.js'
import { MapType } from '@/data/MapConstant.js'

const { orgId, cityName } = useOrg()
const areaOptions = useDictionary('所属地区')
const { ruleList } = useRule()

// 滚动高度
const { size } = defineProps(['size'])
const formHeight = computed(() => size.height - 132 + 'px')

const { setPath, getParam } = useComponent()
const activeLevelIndex = ref(null)

/** 表单 **/
const formContent = reactive({
  param: {
    // 基本信息
    orgId,
    airspaceName: '',
    description: '',
    airspaceCategory: '',
    flightLevelIds: [],
    bot: 0,
    botInclude: 1,
    top: 0,
    topInclude: 1,
    source: '',
    airspaceType: '',
    startTime: null,
    endTime: null,
    regionCodes: '',
    regionType: '',
    statusInfo: { status: 1, runningStatus: 'RUNNING' },
    displayStyle: JSON.stringify({ color: '#f7712f' }),
    airspaceTags: '[]',
    geometries: '[]',
    geoJson: '{}',
    // 规则相关
    rule_id: '',
  },
  bot: 0,
  top: 0,
  airspaceType: '',
  heightDisabled: true,
  loading: false,
  editType: 'add',
})

// 表单验证规则
const formRules = computed(() => ({
  // 必填字段验证
  airspaceName: [{ required: true, message: '请输入空域名称', trigger: 'blur' }],
  airspaceCategory: [{ required: true, message: '请选择空域种类', trigger: 'change' }],
  bot: [
    { required: true, message: '请输入底高', trigger: 'blur' },
    { type: 'number', message: '底高必须为数字', trigger: 'blur' },
  ],
  botInclude: [{ required: true, message: '请选择是否包括底高', trigger: 'change' }],
  top: [
    { required: true, message: '请输入顶高', trigger: 'blur' },
    { type: 'number', message: '顶高必须为数字', trigger: 'blur' },
  ],
  topInclude: [{ required: true, message: '请选择是否包括顶高', trigger: 'change' }],
  source: [{ required: true, message: '请选择来源', trigger: 'change' }],
  airspaceType: [{ required: true, message: '请选择空域类型', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择有效期开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择有效期结束时间', trigger: 'change' }],
  regionCodes: [{ required: true, message: '请选择所属地区', trigger: 'change' }],
  regionType: [{ required: true, message: '请选择区域类型', trigger: 'change' }],
  statusInfo: [{ required: true, message: '请选择状态', trigger: 'change' }],
  // 非必填字段验证
  description: [{ max: 2000, message: '描述不能超过2000个字符', trigger: 'blur' }],
}))

const mapData = reactive({
  displayStyle: { color: '#f7712f' },
  geometries: [],
  extra: {},
  geoJson: [],
  id: '',
})

// 区域类型选项 - 根据orgId动态控制
const regionTypeOptions = computed(() => {
  // 绍兴市，只能选择市和跨市
  if (formContent.param.regionCodes === '330600') {
    return [
      { label: '市', value: 'CITY' },
      { label: '跨市', value: 'CORSS_CITY' },
    ]
  } else {
    // 其他区域只能选择区和跨区
    return [
      { label: '区', value: 'DISTRICT' },
      { label: '跨区', value: 'CORSS_DISTRICT' },
    ]
  }
})

// 判断区域选项是否禁用
const isAreaOptionDisabled = option => {
  // 绍兴市可以选择所有选项
  if (orgId.value === 'cb8upAzuM07uQyNpzuj05') {
    return false
  }
  // 其他机构只能选择与cityName匹配的选项
  return option.label !== cityName.value
}

// 监听areaOptions变化，自动选中匹配的地区
watch([areaOptions, cityName], ([options, currentCityName]) => {
  //如果机构变化清空区域
  formContent.param.regionCodes = ''
  if (options.length > 0 && currentCityName && !formContent.param.regionCodes) {
    // 查找匹配的选项
    const matchedOption = options.find(option => option.label === currentCityName)
    if (matchedOption) {
      formContent.param.regionCodes = matchedOption.value
    }
  }
})

watch(
  mapData,
  () => {
    console.log('mapData', mapData)
  },
  { deep: true },
)

watch(
  () => formContent.param.displayStyle,
  newVal => {
    if (newVal) {
      mapData.displayStyle = JSON.parse(newVal)
    } else {
      mapData.displayStyle = {}
    }
  },
  { deep: true },
)

// 表单引用
const formRef = ref()
const modelDetailsRef = ref()

// 编辑切换
const airSpaceEditTab = ref('1')
const airSpaceEditTabShift = () => {}

const layerKey = ref(null)

// 初始化
initData()
function initData() {
  const params = getParam()
  activeLevelIndex.value = params.data.activeLevelIndex
  formContent.param.bot = params.data.bottom || 0
  formContent.param.top = params.data.top || 0
  layerKey.value = params.data.layerKey
  if (params.data.id) {
    formContent.editType = 'edit'
    searchAirspace(params.data.id)
  } else {
    formContent.editType = 'add'
  }
}

function searchAirspace(id) {
  let p = {
    id: id,
    offset: 0,
    limit: 10,
  }
  api.airspace_aggregate_list(p).then(response => {
    const airspaceData = response.list
    if (airspaceData.length > 0) {
      const airspace = airspaceData[0]
      formContent.param.id = airspace.id
      formContent.param.airspaceName = airspace.airspaceName
      formContent.param.airspaceCategory = airspace.airspaceCategory
      formContent.param.bot = airspace.bot
      formContent.param.top = airspace.top
      formContent.param.botInclude = airspace.botInclude
      formContent.param.topInclude = airspace.topInclude
      formContent.param.source = airspace.source
      formContent.param.airspaceType = airspace.airspaceType
      formContent.param.startTime = String(airspace.startTime)
      formContent.param.endTime = String(airspace.endTime)
      formContent.param.regionCodes = String(...airspace.regionCodes) || ''
      formContent.param.regionType = airspace.regionType
      formContent.param.statusInfo = { status: airspace.status, runningStatus: airspace.runningStatus }
      formContent.param.description = airspace.description
      formContent.param.flightLevelIds = airspace.flightLevelIds
      formContent.param.airspaceTags = JSON.stringify(airspace.airspaceTags) || '[]'
      formContent.param.displayStyle = JSON.stringify(airspace.displayStyle)
      formContent.param.rule_id = airspace.ruleList?.[0]?.id || ''
      console.log('airspace.statusInfo', formContent.param.statusInfo)

      if (airspace.geoJson) {
        mapData.geoJson = [airspace.geoJson]
      }
      if (airspace.geometries) {
        mapData.geometries = airspace.geometries
      }
      if (airspace.displayStyle) {
        mapData.displayStyle = airspace.displayStyle
      }
      mapData.id = airspace.id
    }
  })
}

// 监听地图数据变化，更新表单
watch(
  () => mapData.geometries,
  geometries => {
    if (geometries && geometries.length > 0) {
      formContent.param.geometries = JSON.stringify(geometries)
      formContent.param.geoJson = JSON.stringify(mapData.geoJson[0])
    } else {
      formContent.param.geometries = '[]'
      formContent.param.geoJson = '[]'
    }
  },
  { deep: true },
)

/** GeoJson - 导入 **/
const exportIn = reactive({
  json: '',
})

const subIn = () => {
  formContent.exportIn = true
  const parsedData = JSON.parse(exportIn.json)
  mapData.geometries = parsedData
}

// 提交
const submit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    if (!orgId.value) {
      ElMessage.error('请选择机构')
      return
    }

    // 检查几何体数据
    if (mapData.geometries.length === 0) {
      ElMessage.error('请选择地图模型')
      return
    }

    // 模型验证
    const result = await modelDetailsRef.value.validate()
    if (!result) {
      ElMessage.error('地图模型数据格式错误')
      return
    }

    //如果不是长期有效，判断开始时间是否大于结束时间
    if (formContent.param.startTime !== '-1' && formContent.param.endTime !== '-1') {
      if (formContent.param.startTime > formContent.param.endTime) {
        ElMessage.error('开始时间不能大于结束时间')
        return
      }
    }

    let url = formContent.editType === 'add' ? 'airspace_aggregate_add' : 'airspace_aggregate_update'
    formContent.loading = true
    api[url]({
      ...formContent.param,
      regionCodes: JSON.stringify([formContent.param.regionCodes]),
      flightLevelIds: JSON.stringify(formContent.param.flightLevelIds),
      status: formContent.param.statusInfo.status,
      runningStatus: formContent.param.statusInfo.runningStatus,
    })
      .then(() => {
        ElMessage({ message: '保存成功', type: 'success' })
        setTimeout(() => {
          formContent.loading = false
          setPath(getParam().from, { activeLevelIndex: activeLevelIndex.value }, { type: PathType.successBack })
        }, 1500)
      })
      .catch(err => {
        formContent.loading = false
      })
  } catch (error) {}
}

const onMapDataUpdate = newMapData => {
  Object.assign(mapData, newMapData)
}

// 处理有效期开始时间长期有效开关
const handleTermChange = value => {
  if (value) {
    formContent.param.startTime = '-1'
    formContent.param.endTime = '-1'
  } else {
    formContent.param.startTime = null
    formContent.param.endTime = null
  }
}

// 监听空域种类，联动更新规则
const handleAirspaceCategoryChange = category => {
  if (!category) return

  let ruleName = ''
  if (category === 'SUITABLE') {
    ruleName = '适飞规则'
  } else if (category === 'CONTROLLED') {
    ruleName = '管制规则'
  } else if (category === 'NO_FLY') {
    ruleName = '禁飞规则'
  }

  if (ruleName) {
    const targetRule = ruleList.find(rule => rule.label.includes(ruleName))
    if (targetRule) {
      formContent.param.rule_id = targetRule.value
    } else {
      formContent.param.rule_id = ''
    }
  } else {
    formContent.param.rule_id = ''
  }
}

const handleRegionCodesChange = value => {
  formContent.param.regionType = ''
}
watch(
  () => orgId.value,
  () => {
    formContent.param.regionType = ''
  },
)
</script>

<style scoped lang="less">
.detail {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  .navigation {
    flex: 0 0 auto;
  }
  .paint {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    .card {
      flex: 1 1 0;
      display: flex;
      flex-direction: row;
      height: 100%;
      .flex-row {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }
      .flex-col {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      .map-area {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }
      .form-panel {
        flex: 1 1 0;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px;
        padding-top: 0;
        background-color: #ffffff; /* 白色背景 */
        border-radius: 0 16px 16px 0; /* 头部圆角 */
        .scroll {
          flex: 1 1 0;
          overflow-y: auto;
          height: v-bind(formHeight);
          /* 隐藏滚动条但保持滚动功能 */
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */
          // 表单样式
          :deep(.el-form) {
            .el-form-item {
              margin-bottom: 20px;

              .el-form-item__label {
                font-weight: 500;
                color: #333;
                margin-bottom: 8px;
              }

              .el-input,
              .el-select,
              .el-textarea {
                width: 100%;
              }
            }
          }
        }
        .mbg {
          margin-bottom: 12px;
        }
        .pdr {
          padding-right: 12px;
        }
        .line {
          font-size: 16px;
          color: var(--main-font-color9);
          .title {
            position: relative;
            margin-bottom: 12px;
          }
          .title.w:after {
            position: absolute;
            content: '*';
            color: var(--el-color-danger);
            top: 2px;
            left: -12px;
          }
          .in {
            padding: 0 1px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .col4 {
              width: 40%;
            }
            .col6 {
              width: 60%;
            }
          }
          .note {
            height: 30px;
            line-height: 30px;
          }
        }
        // 模型信息表格样式
        .tb {
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          overflow: hidden;

          .th {
            height: 48px;
            background: #f5f7fa;
            box-sizing: border-box;
            color: #606266;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e4e7ed;

            .name {
              width: 50%;
              text-align: center;
              font-weight: 500;
            }
          }

          .td {
            height: 48px;
            text-align: center;
            line-height: 48px;
            color: #606266;
            border-bottom: 1px solid #e4e7ed;

            &:last-child {
              border-bottom: none;
            }
          }
        }

        // 地图信息样式
        .map-info {
          .map-info-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .map-info-label {
              font-weight: 500;
              color: #606266;
              min-width: 80px;
            }

            .map-info-value {
              color: #303133;
              flex: 1;
            }
          }
        }
      }
    }
  }
}
</style>
