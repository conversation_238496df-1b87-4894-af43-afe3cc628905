<template>
  <div class="airline-design">
    <CommonTitle />
    <CommonForm
      v-model="searchForm"
      :form-items="formItems"
      :default-show-count="3"
      @query="handleSearch"
      @reset="handleReset"
    >
      <!-- 状态插槽 -->
      <template #status>
        <StatusSelect v-model="searchForm.statusInfo" />
      </template>
      <!-- 标签筛选插槽 -->
      <template #tagSelection>
        <TagSelect ref="tagSelectRef" v-model="tagSelection" dic-type="航线标签" />
      </template>
    </CommonForm>
    <main class="main-content">
      <div class="airline-header">
        <header class="panel-header">
          <el-button type="primary" @click="addAirline">新增</el-button>
        </header>
      </div>
      <div class="box">
        <div class="map-section">
          <ShowMap v-model:map-data="mapData" />
        </div>
        <div v-loading="state.loading" class="control-panel">
          <div class="panel-content">
            <div class="airline-list">
              <div
                v-for="airline in state.airlines"
                :key="airline.id"
                class="airline-item"
                :class="{ selected: airline.select }"
                @click="selectAirline(airline)"
              >
                <div class="item-content">
                  <div class="item-icon">
                    <img :src="getImg('areaIcon.png')" alt="航线图标" />
                  </div>
                  <div class="item-info">
                    <h4 class="item-title">{{ airline.airlineTitle }}</h4>
                    <!-- <p class="item-desc">{{ airline.memo || '无备注' }}</p> -->
                  </div>
                </div>
                <div class="item-actions">
                  <el-dropdown>
                    <button class="action-btn">
                      <el-icon><MoreFilled /></el-icon>
                    </button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click.stop="editAirline(airline)">编辑</el-dropdown-item>
                        <el-dropdown-item @click.stop="deleteAirline(airline)">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { reactive, ref, computed } from 'vue'
import * as api from '@/request/api/index'
import { getImg } from '@/utils/getAsset.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import CommonForm from '@/components/common/CommonForm/CommonForm.vue'
import { defaultSearchForm, airlineFormItems } from './config.js'
import { MoreFilled } from '@element-plus/icons-vue'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import ShowMap from '@/components/map/ShowMap.vue'
import TagSelect from '@/components/common/TagSelect/TagSelect.vue'
import StatusSelect from '../common/statusSelect/StatusSelect.vue'

const { size } = defineProps(['size'])
const formHeight = computed(() => size.height - 132 + 'px')

const { setPath } = useComponent()

// 表单配置
const formItems = ref(airlineFormItems)

// 搜索表单
const searchForm = reactive({
  statusInfo: null,
})

const tagSelectRef = ref(null)
// 标签选择
const tagSelection = ref({
  matchAllTagList: [],
  matchAnyTagList: [],
})
const state = reactive({
  loading: false,
  airlines: [],
})

// 地图数据
const mapData = reactive({
  displayStyle: {},
  geometries: [],
  abnormalWidth: 0,
  geoJson: [],
  id: '',
})

// 获取航线列表
const getAirlineList = async () => {
  state.loading = true
  try {
    const { statusInfo, ...rest } = searchForm
    const params = {
      offset: 0,
      limit: 999,
      deleted: 0,
      ...rest,
      status: statusInfo?.status,
      runningStatus: statusInfo?.runningStatus,
      matchAllAirlineTagList: JSON.stringify(tagSelection.value.matchAllTagList),
      matchAnyAirlineTagList: JSON.stringify(tagSelection.value.matchAnyTagList),
    }
    const data = await api.airline_aggregate_list(params)
    state.airlines = data.list.map(airline => ({ ...airline, select: false }))
  } finally {
    state.loading = false
  }
}

// 选择航线
const selectAirline = selectedAirline => {
  state.airlines.forEach(airline => {
    airline.select = airline.id === selectedAirline.id
  })
  if (selectedAirline) {
    mapData.geoJson = [selectedAirline.geoJson]
    mapData.geometries = selectedAirline.geometries
    mapData.id = selectedAirline.id
    mapData.displayStyle = selectedAirline.displayStyle
  } else {
    mapData.geoJson = []
    mapData.geometries = []
    mapData.id = ''
    mapData.displayStyle = {}
  }
}

// 新增航线
const addAirline = () => setPath('airSpaceManagement/AirLineEdit.vue', null, { name: '新增航线', type: PathType.add })

// 编辑航线
const editAirline = airline => {
  setPath('airSpaceManagement/AirLineEdit.vue', { id: airline.id }, { name: '编辑航线', type: PathType.edit })
}

// 删除航线
const deleteAirline = airline => {
  ElMessageBox.confirm('是否确定删除当前航线', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.airline_aggregate_delete({ idList: JSON.stringify([airline.id]) })
    ElMessage.success('删除成功')
    await getAirlineList()
    // 清空地图数据
    mapData.geoJson = []
    mapData.geometries = []
    mapData.id = ''
    mapData.displayStyle = {}
  })
}

const handleSearch = () => {
  getAirlineList()
}

const handleReset = () => {
  searchForm.statusInfo = null
  tagSelectRef.value?.clearSelection()
  getAirlineList()
}

// 初始化数据
getAirlineList()
</script>

<style scoped lang="less">
.airline-design {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
}

/* 主要内容区域 */
.main-content {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-top: 0;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  min-height: 0;

  /* 航线头部区域 */
  .airline-header {
    display: flex;
    justify-content: flex-end;
    border-bottom: 2px solid #e8e8e8;
    flex: 0 0 auto;
    background: #fff;
    margin-bottom: 16px;
  }
}

.box {
  display: flex;
  flex: 1 1 0;
  min-height: 0;
  max-height: 100%;
  gap: 16px;
  overflow: hidden;
}

.map-section {
  flex: 1 1 0;
  background: #f8f9fa;
  border-radius: 8px;
  position: relative;
}

/* 右侧控制面板 */
.control-panel {
  flex: 0 0 360px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.panel-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  flex: 0 0 auto;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  background: #fff;
}

.airline-tabs {
  flex: 0 0 auto;
  padding: 0 12px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.type-tabs {
  display: flex;
  gap: 4px;
}

.type-tab {
  padding: 8px 16px;
  border: none;
  background: #f5f5f5;
  color: #666;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    background: #e8e8e8;
  }

  &.active {
    background: var(--main-color);
    color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.airline-list {
  flex: 1 1 0;
  overflow-y: auto;
  height: v-bind(formHeight);
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.list-container {
  height: 100%;
}

.scroll-content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 12px;
  min-height: 0;
  max-height: 100%;
}

.airline-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 8px;
  background: #fff;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #f87130;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.selected {
    background: rgba(247, 113, 47, 0.08);
    border-color: #f87130;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.item-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1 1 0;
  min-width: 0;
}

.item-icon {
  flex: 0 0 auto;

  img {
    width: 40px;
    height: 40px;
    border-radius: 4px;
  }
}

.item-info {
  flex: 1 1 0;
  min-width: 0;
}

.item-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-desc {
  margin: 0;
  font-size: 12px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-actions {
  flex: 0 0 auto;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--main-color);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(247, 113, 47, 0.1);
  }
}

@media (max-width: 1200px) {
  .control-panel {
    flex: 0 0 280px;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .control-panel {
    flex: 0 0 300px;
  }
}
</style>
