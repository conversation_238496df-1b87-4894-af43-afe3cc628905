<template>
  <div class="airspace-design">
    <CommonTitle />
    <CommonForm
      v-model="searchForm"
      :form-items="formItems"
      :default-show-count="3"
      @query="handleSearch"
      @reset="handleReset"
    >
      <template #status>
        <StatusSelect v-model="searchForm.statusInfo" />
      </template>
      <template #timeRange>
        <DateRange
          v-model="searchForm.validity"
          :default-props="{
            format: 'YYYY-MM-DD HH:mm',
            valueFormat: 'YYYYMMDDHHmm',
            clearable: true,
            rangeSeparator: '至',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
          }"
          :show-radio-group="true"
        />
      </template>

      <template #tagSelection>
        <TagSelect ref="tagSelectRef" v-model="tagSelection" dic-type="空域标签" />
      </template>
    </CommonForm>

    <main class="main-content">
      <div class="height-tabs">
        <el-tabs v-model="state.activeLevelIndex" :lazy="false">
          <el-tab-pane
            v-for="level in state.levels"
            :key="level.index"
            :lazy="false"
            :label="level.label"
            :name="String(level.index)"
          />
        </el-tabs>
        <el-checkbox-group v-model="searchForm.airspaceCategoryList" size="large" @change="handleSearch">
          <el-checkbox v-for="item in commonAirspaceCategoryOptions" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="box">
        <div class="map-section">
          <ShowMap v-model:map-data="mapData" />
        </div>
        <div v-loading="state.loading" class="control-panel">
          <div v-if="currentLevel" class="panel-content">
            <header class="panel-header">
              <el-button
                type="primary"
                :disabled="!searchForm.airspaceCategoryList || searchForm.airspaceCategoryList.length === 0"
                @click="addAirspace"
                >新增</el-button
              >
            </header>
            <div class="airspace-list">
              <div
                v-for="airspace in currentLevel?.airspaces || []"
                :key="airspace.id"
                class="airspace-item"
                :class="{ selected: airspace.select }"
                @click="selectAirspace(airspace)"
              >
                <div class="item-content">
                  <div class="item-icon">
                    <img :src="getImg('areaIcon.png')" alt="区域图标" />
                  </div>
                  <div class="item-info">
                    <h4 class="item-title">{{ airspace.airspaceName }}</h4>
                    <p class="item-desc">{{ airspace.memo }}</p>
                  </div>
                </div>
                <div class="item-actions">
                  <el-dropdown>
                    <button class="action-btn">
                      <el-icon><MoreFilled /></el-icon>
                    </button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click.stop="editAirspace(airspace)">编辑</el-dropdown-item>
                        <el-dropdown-item @click.stop="deleteAirspace(airspace)">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { reactive, computed, watch, ref } from 'vue'
import * as api from '@/request/api/index'
import { ElMessageBox, ElMessage } from 'element-plus'
import { getImg } from '@/utils/getAsset.js'
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import CommonForm from '@/components/common/CommonForm/CommonForm.vue'
import TagSelect from '@/components/common/tagSelect/TagSelect.vue'
import DateRange from '@/components/common/dateRange/DateRange.vue'
import { defaultSearchForm, airspaceFormItems } from './config.js'
import { useEventBus } from '@vueuse/core'
import { MoreFilled } from '@element-plus/icons-vue'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import ShowMap from '@/components/map/ShowMap.vue'
import { commonAirspaceCategoryOptions } from './config.js'
import useFlightLevel from '@/hooks/useFlightLevel.js'
import StatusSelect from '../common/statusSelect/StatusSelect.vue'

const { size } = defineProps(['size'])
const formHeight = computed(() => size.height - 132 + 'px')

const { setPath, getParam } = useComponent()

const formItems = ref(airspaceFormItems)

const searchForm = reactive({
  airspaceCategoryList: ['SUITABLE'],
  validity: null,
  statusInfo: null,
})

const LAYER_NAMES = {
  0: 'lowAirSpace',
  1: 'middleAirSpace',
  2: 'highAirSpace',
}

const tagSelectRef = ref(null)
const tagSelection = ref({
  matchAllTagList: [],
  matchAnyTagList: [],
})

const layerRefreshBus = useEventBus('LayerRefresh')
const levels = useFlightLevel()

const state = reactive({
  levels: levels,
  activeLevelIndex: getParam()?.data?.activeLevelIndex || '0',
  loading: false,
})

const mapData = reactive({
  displayStyle: {},
  geometries: [],
  extra: {},
  geoJson: [],
  id: '',
})

const activeLevelIndexNumber = computed(() => parseInt(state.activeLevelIndex, 10))
const currentLevel = computed(() => {
  if (state.activeLevelIndex < 0 || !state.levels[activeLevelIndexNumber.value]) {
    return null
  }
  return state.levels[activeLevelIndexNumber.value]
})

const layerName = computed(() => LAYER_NAMES[activeLevelIndexNumber.value] || 'highAirSpace')

const updateMapDisplay = () => {
  const selectedAirspace = currentLevel.value?.airspaces.find(a => a.select)
  if (selectedAirspace) {
    mapData.geoJson = [selectedAirspace.geoJson]
    mapData.geometries = selectedAirspace.geometries
    mapData.id = selectedAirspace.id
    mapData.displayStyle = selectedAirspace.displayStyle || { color: '#f7712f' }
  } else {
    mapData.geoJson = []
    mapData.geometries = []
    mapData.id = ''
    mapData.displayStyle = { color: '#f7712f' }
  }
}

const selectAirspace = selectedAirspace => {
  if (!currentLevel.value) return
  currentLevel.value.airspaces.forEach(a => {
    a.select = false
  })
  selectedAirspace.select = true
  updateMapDisplay()
}

const loadAirspacesForLevel = async level => {
  if (!level) return
  if (searchForm.airspaceCategoryList.length === 0) {
    level.airspaces = []
    return
  }
  try {
    state.loading = true
    const airspaces = await fetchAggregateList(level.id)
    level.airspaces = airspaces.map(a => ({ ...a, select: false }))
    level.dataLoaded = true
  } finally {
    state.loading = false
  }
}

const fetchAggregateList = async flightLevelId => {
  const { statusInfo, ...rest } = searchForm
  const params = {
    ...rest,
    flightLevelId: flightLevelId,
    airspaceCategoryList: JSON.stringify(searchForm.airspaceCategoryList),
    matchAllAirspaceTagList: JSON.stringify(tagSelection.value.matchAllTagList),
    matchAnyAirspaceTagList: JSON.stringify(tagSelection.value.matchAnyTagList),
    source: 'COMMON',
    deleted: 0,
    offset: 0,
    limit: 999,
    status: statusInfo?.status,
    runningStatus: statusInfo?.runningStatus,
  }

  if (searchForm.validity && searchForm.validity.beginTime) {
    params.beginTime = searchForm.validity.beginTime
    params.endTime = searchForm.validity.endTime
  }

  const response = await api.airspace_aggregate_list(params)
  return response.list || []
}

const addAirspace = () => {
  if (!currentLevel.value) {
    ElMessage.error('请先选择一个高度层')
    return
  }
  setPath(
    'airSpaceManagement/CommonAirSpaceEdit.vue',
    {
      bottom: currentLevel.value.bottom,
      top: currentLevel.value.top,
      flightLevelId: currentLevel.value.id,
      activeLevelIndex: state.activeLevelIndex,
    },
    { name: '新增空域', type: PathType.add },
  )
}

const editAirspace = airspace => {
  setPath(
    'airSpaceManagement/CommonAirSpaceEdit.vue',
    {
      id: airspace.id,
      bottom: currentLevel.value.bottom,
      top: currentLevel.value.top,
      flightLevelId: currentLevel.value.id,
      activeLevelIndex: state.activeLevelIndex,
    },
    { name: '编辑空域', type: PathType.edit },
  )
}

const deleteAirspace = airspace => {
  ElMessageBox.confirm('是否确定删除当前空域', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.airspace_aggregate_delete({ idList: JSON.stringify([airspace.id]) })
    ElMessage.success('删除成功')
    // 清空地图数据
    mapData.geoJson = []
    mapData.geometries = []
    mapData.id = ''
    mapData.displayStyle = { color: '#f7712f' }
    if (currentLevel.value) {
      await loadAirspacesForLevel(currentLevel.value)
      updateMapDisplay()
    }
  })
}

const handleSearch = () => {
  state.levels.forEach(level => {
    level.dataLoaded = false
  })
  if (currentLevel.value) {
    loadAirspacesForLevel(currentLevel.value)
  }
}

const handleReset = () => {
  searchForm.validity = null
  searchForm.statusInfo = null
  tagSelectRef.value?.clearSelection()
  handleSearch()
}

watch(currentLevel, async newLevel => {
  if (newLevel && !newLevel.dataLoaded) {
    await loadAirspacesForLevel(newLevel)
  }
  updateMapDisplay()
})

watch(
  layerName,
  newLayerName => {
    layerRefreshBus.emit({
      key: newLayerName,
      data: null,
    })
  },
  { immediate: true },
)

loadAirspacesForLevel(currentLevel.value)
</script>

<style scoped lang="less">
.airspace-design {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
  :deep(.el-tabs__nav-wrap:after) {
    height: 0;
  }
}

.main-content {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-top: 0;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  min-height: 0;

  .height-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #e8e8e8;
    flex: 0 0 auto;
    background: #fff;
    margin-bottom: 16px;
  }
}

.box {
  display: flex;
  flex: 1 1 0;
  min-height: 0;
  max-height: 100%;
  gap: 16px;
  overflow: hidden;
}

.map-section {
  flex: 1 1 0;
  background: #f8f9fa;
  border-radius: 8px;
  position: relative;
}

/* 右侧控制面板 */
.control-panel {
  flex: 0 0 360px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.panel-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  flex: 0 0 auto;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  background: #fff;
}

.airspace-list {
  flex: 1 1 0;
  overflow-y: auto;
  height: v-bind(formHeight);
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.airspace-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 8px;
  background: #fff;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #f87130;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.selected {
    background: rgba(247, 113, 47, 0.08);
    border-color: #f87130;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.item-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1 1 0;
  min-width: 0;
}

.item-icon {
  flex: 0 0 auto;

  img {
    width: 40px;
    height: 40px;
    border-radius: 4px;
  }
}

.item-info {
  flex: 1 1 0;
  min-width: 0;
}

.item-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-desc {
  margin: 0;
  font-size: 12px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-actions {
  flex: 0 0 auto;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--main-color);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(247, 113, 47, 0.1);
  }
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: #e8e8e8;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__nav-scroll) {
  padding: 16px 0;
}

@media (max-width: 1200px) {
  .control-panel {
    flex: 0 0 280px;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .control-panel {
    flex: 0 0 300px;
  }
}
</style>
