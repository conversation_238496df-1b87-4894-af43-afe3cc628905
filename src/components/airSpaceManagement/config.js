// import { mapStatusToText } from '@/utils/helper.js'
// Point 搜索表单默认值
export const defaultSearchForm = {
  status: undefined, // 改为可选择
  runningStatus: undefined, // 新增运行状态
}

// 状态选项配置
export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '停用', value: 2 },
]

export const runningStatusOptions = [
  { label: '运行中', value: 'RUNNING' },
  { label: '规划中', value: 'PLANNING' },
]

export const airspaceCategoryOptions = [
  { label: '适飞区域', value: 'SUITABLE' },
  { label: '管制区域', value: 'CONTROLLED' },
  { label: '禁飞区域', value: 'NO_FLY' },
  // { label: '协调军民航单位划设区域', value: 'COORDINATED_DELIMITATION' },
  // { label: '按需划设区域', value: 'ON_DEMAND' },
  // { label: '活动区域', value: 'ACTIVITY' },
  // { label: '协调军民航单位申请使用区域', value: 'COORDINATED_USAGE' },
  // { label: '军航活动区域', value: 'MILITARY' },
  // { label: '直升机场区域', value: 'HELIPORT' },
]
export const commonAirspaceCategoryOptions = [{ label: '适飞区域', value: 'SUITABLE' }]

// Airspace 表单配置项
export const airspaceFormItems = [
  {
    type: 'input',
    prop: 'airspaceNameLike',
    label: '空域名称',
    placeholder: '请输入空域名称',
    span: 8,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'status',
    prop: 'statusInfo',
    label: '状态',
    placeholder: '请选择状态',
    span: 8,
  },
  {
    type: 'slot',
    slotName: 'cityCodeList',
    prop: 'cityCodeList',
    label: '所属地区',
    span: 8,
  },
  {
    type: 'slot',
    slotName: 'timeRange',
    label: '有效期',
    span: 8,
    prop: 'timeRange',
  },
  // {
  //   type: 'select',
  //   prop: 'source',
  //   label: '来源',
  //   placeholder: '请选择来源',
  //   span: 8,
  //   options: [
  //     { label: '空域划设', value: 'DELIMITATION' },
  //     { label: '常用空域', value: 'COMMON' },
  //   ],
  // },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 8,
  },
]

// Airline 表单配置项
export const airlineFormItems = [
  {
    type: 'input',
    prop: 'airlineNoLike',
    label: '航线编号',
    placeholder: '请输入航线编号',
    span: 8,
  },
  {
    type: 'input',
    prop: 'airlineTitleLike',
    label: '航线名称',
    placeholder: '请输入航线名称',
    span: 8,
  },
  {
    type: 'slot',
    slotName: 'cityCodeList',
    prop: 'cityCodeList',
    label: '所属地区',
    span: 8,
  },
  {
    type: 'slot',
    slotName: 'status',
    prop: 'statusInfo',
    label: '状态',
    placeholder: '请选择状态',
    span: 8,
  },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 8,
  },
]

// 航路表单配置项
export const airwayFormItems = [
  {
    type: 'input',
    prop: 'codeLike',
    label: '航路编号',
    placeholder: '请输入航路编号',
    span: 8,
    props: {
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'nameLike',
    label: '航路名称',
    placeholder: '请输入航路名称',
    span: 8,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'cityCodeList',
    prop: 'cityCodeList',
    label: '所属地区',
    span: 8,
  },
  {
    type: 'slot',
    slotName: 'status',
    prop: 'statusInfo',
    label: '状态',
    placeholder: '请选择状态',
    span: 8,
  },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 8,
  },
]

// 航路搜索表单默认值
export const airwaySearchForm = {
  codeLike: undefined,
  nameLike: undefined,
  status: undefined,
  runningStatus: undefined,
}
