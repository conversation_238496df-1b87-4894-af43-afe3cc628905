<template>
  <div class="airline-edit-container">
    <!-- 标题-->
    <div class="navigation">
      <CommonTitle is-back>
        <template #default>
          <div class="opearte">
            <el-button type="primary" @click="submit">提交</el-button>
          </div>
        </template>
      </CommonTitle>
    </div>

    <div class="main-content">
      <div class="content-card">
        <el-row class="flex-row">
          <el-col :span="18" class="flex-col">
            <div class="map-area">
              <EditMap v-model:map-data="mapData" :type="MapType.LINE" />
            </div>
          </el-col>
          <el-col :span="6" class="flex-col">
            <div class="form-panel">
              <el-tabs v-model="airSpaceEditTab" class="demo-tabs" @tab-click="airSpaceEditTabShift">
                <el-tab-pane label="基本信息" name="1">
                  <div class="scroll">
                    <el-form ref="formRef" :model="formData" label-position="top" :rules="rules">
                      <el-form-item label="航线名称" prop="airlineTitle">
                        <el-input v-model="formData.airlineTitle" placeholder="请输入1~16位字数"></el-input>
                      </el-form-item>
                      <el-form-item label="航线编号" prop="airlineNo">
                        <el-input v-model="formData.airlineNo" placeholder="请输入"></el-input>
                      </el-form-item>
                      <el-form-item label="航线种类" prop="airlineCategory">
                        <el-select v-model="formData.airlineCategory" placeholder="请选择" style="width: 100%">
                          <el-option label="顺丰" value="SF" />
                          <el-option label="一电" value="YD" />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="航线类型" prop="airlineType">
                        <el-select v-model="formData.airlineType" placeholder="请选择" style="width: 100%">
                          <el-option
                            v-for="item in uaAirlineTypeGroup"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="航线性质" prop="airlineKind">
                        <el-select v-model="formData.airlineKind" placeholder="请选择" style="width: 100%">
                          <el-option
                            v-for="item in uaAirlineKindGroup"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="巡航高度(米)" prop="airlineHeight">
                        <el-input v-model="formData.airlineHeight" placeholder="请输入" type="number"></el-input>
                      </el-form-item>
                      <el-form-item label="航线宽度(米)" prop="airlineWidth">
                        <el-input v-model="formData.airlineWidth" placeholder="请输入" type="number"></el-input>
                      </el-form-item>

                      <el-form-item label="起飞点" prop="startPoint">
                        <el-input v-model="formData.startPoint" placeholder="请输入"></el-input>
                      </el-form-item>
                      <el-form-item label="临时起降点" prop="stopoverPoint">
                        <el-input v-model="formData.stopoverPoint" placeholder="请输入"></el-input>
                      </el-form-item>
                      <el-form-item label="落地点" prop="endPoint">
                        <el-input v-model="formData.endPoint" placeholder="请输入"></el-input>
                      </el-form-item>
                      <el-form-item label="飞行距离(米)" prop="flightDistance">
                        <el-input v-model="formData.flightDistance" placeholder="请输入" type="number"></el-input>
                      </el-form-item>
                      <el-form-item label="所属地区" prop="regionCodes">
                        <el-input v-model="formData.regionCodes" placeholder="请输入所属地区" />
                      </el-form-item>

                      <el-form-item label="区域类型" prop="regionType">
                        <el-select v-model="formData.regionType" placeholder="请选择区域类型" style="width: 100%">
                          <el-option label="省" value="PROVINCE" />
                          <el-option label="跨省" value="CROSS_PROVINCE" />
                          <el-option label="市" value="CITY" />
                          <el-option label="跨市" value="CORSS_CITY" />
                          <el-option label="区" value="DISTRICT" />
                          <el-option label="跨区" value="CORSS_DISTRICT" />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="状态" prop="status">
                        <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
                          <el-option label="启动" value="1" />
                          <el-option label="停用" value="2" />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="运行状态" prop="runningStatus">
                        <el-select v-model="formData.runningStatus" placeholder="请选择运行状态" style="width: 100%">
                          <el-option label="运行中" value="RUNNING" />
                          <el-option label="规划中" value="PLANNING" />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="备注" prop="memo">
                        <el-input v-model="formData.memo" type="textarea" placeholder="请输入" :rows="3" />
                      </el-form-item>
                      <el-form-item label="航线坐标">
                        <ModelDetails v-model="mapData" />
                      </el-form-item>
                    </el-form>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="GeoJson" disabled name="2">
                  <div>
                    <el-input v-model="exportIn.json" type="textarea" :rows="26" />
                  </div>
                  <div class="subIn">
                    <el-button type="primary" @click="subIn">导入</el-button>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import * as api from '@/request/api/index.js'
import CommonTitle from '@/components/common/commonTitle/CommonTitle.vue'
import EditMap from '@/components/map/EditMap.vue'
import ModelDetails from '@/components/common/modelDetails/ModelDetails.vue'
import { isJSON } from '@/utils/helper.js'
import useOrg from '@/hooks/useOrg.js'
import { MapType } from '@/data/MapConstant.js'
const { orgId } = useOrg()

const { setPathBack, getParam } = useComponent()

// 滚动高度
const { size } = defineProps(['size'])
const formHeight = computed(() => size.height - 132 + 'px')

const formRef = ref(null)
const loading = ref(false)
const editType = ref('add')
const airSpaceEditTab = ref('1')

const formData = reactive({
  id: null,
  orgId,
  airlineTitle: '',
  airlineNo: '',
  airlineCategory: '',
  airlineType: '',
  airlineKind: '',
  airlineHeight: 0,
  airlineWidth: 0,
  memo: '',
  status: '',
  geometries: '[]',
  geoJson: '{}',
  rule_id: 'F6jRSCrVPdnGCGw_Phli9',
  startPoint: '',
  endPoint: '',
  stopoverPoint: '',
  flightDistance: '',
  runningStatus: '',
  regionCodes: '',
  regionType: '',
})

const mapData = reactive({
  displayStyle: {},
  geometries: [],
  abnormalWidth: 0,
  geoJson: [],
})

const rules = {
  airlineTitle: [
    { required: true, message: '请输入航线名称', trigger: 'blur' },
    { max: 16, message: '航线名称不能超过16个字符', trigger: 'blur' },
  ],
  airlineNo: [
    { required: true, message: '请输入航线编号', trigger: 'blur' },
    { pattern: /^[^\u4e00-\u9fa5]+$/, message: '请不要输入中文', trigger: 'change' },
  ],
  airlineCategory: [{ required: true, message: '请选择航线种类', trigger: 'change' }],
  airlineType: [{ required: true, message: '请选择航线类型', trigger: 'change' }],
  airlineKind: [{ required: true, message: '请选择航线性质', trigger: 'change' }],
  airlineHeight: [{ required: true, message: '请输入巡航高度', trigger: 'blur' }],
  airlineWidth: [{ required: true, message: '请输入航线宽度', trigger: 'blur' }],
  startPoint: [{ required: true, message: '请输入起飞点', trigger: 'blur' }],
  stopoverPoint: [{ required: true, message: '请输入临时起降点', trigger: 'blur' }],
  endPoint: [{ required: true, message: '请输入落地点', trigger: 'blur' }],
  flightDistance: [{ required: true, message: '请输入飞行距离', trigger: 'blur' }],
  regionCodes: [{ required: true, message: '请输入属地代码', trigger: 'blur' }],
  regionType: [{ required: true, message: '请选择区域类型', trigger: 'change' }],
  runningStatus: [{ required: true, message: '请选择运行状态', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
}

const uaAirlineTypeGroup = ref([
  { label: '固定航线', value: 0 },
  { label: '临时航线', value: 1 },
  { label: '自定义', value: 2 },
])
const uaAirlineKindGroup = ref([
  { label: '自由', value: 0 },
  { label: '物流', value: 1 },
  { label: '文旅', value: 2 },
  { label: '农林', value: 3 },
  { label: '客运', value: 4 },
])

const searchLine = async id => {
  loading.value = true
  try {
    const res = await api.airline_aggregate_list({ id, offset: 0, limit: 10 })
    if (res.list.length > 0) {
      const lineData = res.list[0]
      formData.id = lineData.id
      formData.airlineTitle = lineData.airlineTitle
      formData.airlineNo = lineData.airlineNo
      formData.airlineCategory = lineData.airlineCategory
      formData.airlineType = lineData.airlineType
      formData.airlineKind = lineData.airlineKind
      formData.airlineHeight = lineData.airlineHeight
      formData.airlineWidth = lineData.airlineWidth
      formData.memo = lineData.memo
      formData.status = lineData.status
      formData.startPoint = lineData.startPoint
      formData.endPoint = lineData.endPoint
      formData.stopoverPoint = lineData.stopoverPoint
      formData.flightDistance = lineData.flightDistance
      formData.runningStatus = lineData.runningStatus
      formData.regionCodes = lineData.regionCodes
      formData.regionType = lineData.regionType
      formData.status = lineData.status
      if (lineData.ruleList && lineData.ruleList.length > 0) {
        formData.rule_id = lineData.ruleList[0].id
      }

      // 回显几何数据到地图
      if (lineData.geometries) {
        mapData.geometries = lineData.geometries
      }
      if (lineData.geoJson) {
        mapData.geoJson = [lineData.geoJson]
      }
    }
  } finally {
    loading.value = false
  }
}

watch(
  () => mapData.geometries,
  geometries => {
    if (geometries && geometries.length > 0) {
      formData.geometries = JSON.stringify(geometries)
      formData.geoJson = JSON.stringify(mapData.geoJson[0])
    }
  },
  { deep: true },
)

const exportIn = reactive({ json: '' })
const subIn = () => {
  if (!isJSON(exportIn.json)) {
    return ElMessage.error('导入失败，请输入正确的JSON格式')
  }
  const data = JSON.parse(exportIn.json)
  mapData.geometries = data
  ElMessage.success('导入成功')
}

const submit = async () => {
  console.log('formData', formData)

  try {
    await formRef.value.validate()
  } catch (error) {
    return ElMessage.error('请检查表单是否填写完整')
  }

  loading.value = true
  try {
    const params = { ...formData }
    const url = editType.value === 'add' ? 'airline_aggregate_add' : 'airline_aggregate_update'
    await api[url](params)
    ElMessage.success('保存成功')
    setTimeout(() => setPathBack(), 1500)
  } finally {
    loading.value = false
  }
}

const init = () => {
  const paramData = getParam().data
  if (paramData && paramData.id) {
    editType.value = 'edit'
    searchLine(paramData.id)
  }
}

init()
</script>

<style scoped lang="scss">
.airline-edit-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;

  .navigation {
    flex: 0 0 auto;
  }

  .main-content {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;

    .content-card {
      flex: 1 1 0;
      display: flex;
      flex-direction: row;
      height: 100%;

      .flex-row {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }

      .flex-col {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .map-area {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }

      .form-panel {
        flex: 1 1 0;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px;
        padding-top: 0;
        background-color: #ffffff; /* 白色背景 */
        border-radius: 0 16px 16px 0; /* 头部圆角 */

        .scroll {
          flex: 1 1 0;
          overflow-y: auto;
          height: v-bind(formHeight);
          /* 隐藏滚动条但保持滚动功能 */
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */

          // 表单样式
          :deep(.el-form) {
            .el-form-item {
              margin-bottom: 20px;

              .el-form-item__label {
                font-weight: 500;
                color: #333;
                margin-bottom: 8px;
              }

              .el-input,
              .el-select,
              .el-textarea {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}
</style>
