<template>
  <div v-loading="loading" class="airline-edit-container">
    <!-- 标题-->
    <div class="navigation">
      <CommonTitle is-back>
        <template #default>
          <div class="opearte">
            <el-button type="primary" @click="submit">提交</el-button>
          </div>
        </template>
      </CommonTitle>
    </div>

    <div class="main-content">
      <div class="content-card">
        <el-row class="flex-row">
          <el-col :span="18" class="flex-col">
            <div class="map-area">
              <EditMap v-model:map-data="mapData" :type="MapType.LINE" />
            </div>
          </el-col>
          <el-col :span="6" class="flex-col">
            <div class="form-panel">
              <el-tabs v-model="airSpaceEditTab" class="demo-tabs">
                <el-tab-pane label="基本信息" name="1">
                  <div class="scroll">
                    <el-form ref="formRef" :model="formData" label-position="top" :rules="rules">
                      <el-form-item label="航线名称" prop="airlineTitle">
                        <el-input v-model="formData.airlineTitle" placeholder="请输入1~16位字数"></el-input>
                      </el-form-item>
                      <el-form-item label="航线编号" prop="airlineNo">
                        <el-input v-model="formData.airlineNo" placeholder="请输入"></el-input>
                      </el-form-item>
                      <el-form-item label="航线种类" prop="airlineCategory">
                        <el-select v-model="formData.airlineCategory" placeholder="请选择" style="width: 100%">
                          <el-option
                            v-for="item in airlineTypeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="航线类型" prop="airlineType">
                        <el-select v-model="formData.airlineType" placeholder="请选择" style="width: 100%">
                          <el-option
                            v-for="item in uaAirlineTypeGroup"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="航线性质" prop="airlineKind">
                        <el-select v-model="formData.airlineKind" placeholder="请选择" style="width: 100%">
                          <el-option
                            v-for="item in uaAirlineKindGroup"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="航线标签" prop="airlineTags">
                        <TypeTags v-model="formData.airlineTags" dic-type="航线标签"
                      /></el-form-item>
                      <el-form-item label="巡航高度(米)" prop="airlineHeight">
                        <el-input-number
                          v-model="formData.airlineHeight"
                          :controls="false"
                          style="width: 100%"
                          :precision="0"
                          :min="0"
                          placeholder="请输入"
                        ></el-input-number>
                      </el-form-item>
                      <el-form-item label="航线宽度(米)" prop="airlineWidth">
                        <el-input-number
                          v-model="formData.airlineWidth"
                          :controls="false"
                          style="width: 100%"
                          :precision="0"
                          :min="0"
                          placeholder="请输入"
                        ></el-input-number>
                      </el-form-item>

                      <el-form-item label="起飞点" prop="startPoint">
                        <el-input v-model="formData.startPoint" placeholder="请输入"></el-input>
                      </el-form-item>
                      <el-form-item label="临时起降点" prop="stopoverPoint">
                        <el-input v-model="formData.stopoverPoint" placeholder="请输入"></el-input>
                      </el-form-item>
                      <el-form-item label="落地点" prop="endPoint">
                        <el-input v-model="formData.endPoint" placeholder="请输入"></el-input>
                      </el-form-item>
                      <el-form-item label="飞行距离(米)" prop="flightDistance">
                        <el-input-number
                          v-model="formData.flightDistance"
                          :controls="false"
                          style="width: 100%"
                          :precision="0"
                          :min="0"
                          placeholder="请输入"
                        ></el-input-number>
                      </el-form-item>
                      <RegionSelect v-model="formData.regionInfo" prop-prefix="regionInfo" />
                      <el-form-item label="状态" prop="statusInfo">
                        <StatusSelect v-model="formData.statusInfo" />
                      </el-form-item>
                      <el-form-item label="样式配置" prop="displayStyle">
                        <StyleConfigEditor v-model="formData.displayStyle" />
                      </el-form-item>
                      <el-form-item v-if="editType === 'edit'" label="规则" prop="rule_idList">
                        <el-select
                          v-model="formData.rule_idList"
                          placeholder="请选择"
                          style="width: 100%"
                          multiple
                          collapse-tags
                          collapse-tags-tooltip
                          clearable
                        >
                          <el-option
                            v-for="item in ruleList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                            :disabled="item.value === defaultRuleId"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="附件" prop="fileIds">
                        <CommonUpload v-model="formData.fileIds" multiple accept=".pdf" />
                      </el-form-item>
                      <el-form-item label="备注" prop="memo">
                        <el-input v-model="formData.memo" type="textarea" placeholder="请输入" :rows="3" />
                      </el-form-item>
                      <el-form-item label="航线坐标">
                        <ModelDetails ref="modelDetailsRef" v-model="mapData" @update:model-value="onMapDataUpdate" />
                      </el-form-item>
                    </el-form>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="GeoJson" disabled name="2">
                  <div>
                    <el-input v-model="exportIn.json" type="textarea" :rows="26" />
                  </div>
                  <div class="subIn">
                    <el-button type="primary" @click="subIn">导入</el-button>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import * as api from '@/request/api/index.js'
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import EditMap from '@/components/map/EditMap.vue'
import ModelDetails from '@/components/common/modelDetails/ModelDetails.vue'
import TypeTags from '@/components/common/typeTags/TypeTags.vue'
import StyleConfigEditor from '@/components/common/styleConfigEditor/StyleConfigEditor.vue'
import StatusSelect from '@/components/common/statusSelect/StatusSelect.vue'
import CommonUpload from '@/components/common/commonUpload/CommonUpload.vue'
import RegionSelect from '@/components/common/regionSelect/RegionSelect.vue'
import { isJSON } from '@/utils/helper.js'
import useOrg from '@/hooks/useOrg.js'
import { MapType } from '@/data/MapConstant.js'
import { useDictionary } from '@/hooks/useDictionary.js'
import { useRule } from '@/hooks/useRule.js'

const { orgId } = useOrg()

const airlineTypeOptions = useDictionary('航线种类')

const { ruleList } = useRule()

const { setPath, getParam } = useComponent()

// 滚动高度
const { size } = defineProps(['size'])
const formHeight = computed(() => size.height - 132 + 'px')

const formRef = ref(null)
const loading = ref(false)
const editType = ref('add')
const airSpaceEditTab = ref('1')
const modelDetailsRef = ref(null)
const defaultRuleId = ref('G3icFGOPx_ubKgU_EulRY') // 航线默认规则ID

const formData = reactive({
  id: null,
  orgId,
  cityCode: '',
  airlineTitle: '',
  airlineNo: '',
  airlineCategory: '',
  airlineType: '',
  airlineKind: '',
  airlineHeight: 0,
  airlineWidth: 0,
  airlineTags: [],
  memo: '',
  geometries: '[]',
  geoJson: '{}',
  rule_idList: [defaultRuleId.value], // 默认包含硬编码的规则ID
  startPoint: '',
  endPoint: '',
  stopoverPoint: '',
  flightDistance: '',
  regionInfo: {
    regionCodes: '',
    regionType: '',
  },
  displayStyle: JSON.stringify({ color: '#f7712f' }),
  statusInfo: { status: 1, runningStatus: 'RUNNING' },
  fileIds: [],
})

const mapData = reactive({
  displayStyle: { color: '#f7712f' },
  geometries: [],
  extra: {},
  geoJson: [],
  id: '',
})

const rules = {
  'airlineTitle': [{ required: true, message: '请输入航线名称', trigger: 'blur' }],
  'airlineNo': [
    { required: true, message: '请输入航线编号', trigger: 'blur' },
    { pattern: /^[^\u4e00-\u9fa5]+$/, message: '请不要输入中文', trigger: 'change' },
  ],
  'airlineCategory': [{ required: true, message: '请选择航线种类', trigger: 'change' }],
  'airlineType': [{ required: true, message: '请选择航线类型', trigger: 'change' }],
  'airlineKind': [{ required: true, message: '请选择航线性质', trigger: 'change' }],
  'airlineHeight': [{ required: true, message: '请输入巡航高度', trigger: 'blur' }],
  'airlineWidth': [{ required: true, message: '请输入航线宽度', trigger: 'blur' }],
  'startPoint': [{ required: true, message: '请输入起飞点', trigger: 'blur' }],
  'endPoint': [{ required: true, message: '请输入落地点', trigger: 'blur' }],
  'flightDistance': [{ required: true, message: '请输入飞行距离', trigger: 'blur' }],
  'regionInfo.regionCodes': [{ required: true, message: '请选择所属地区', trigger: 'change' }],
  'regionInfo.regionType': [{ required: true, message: '请选择区域类型', trigger: 'change' }],
  'statusInfo': [{ required: true, message: '请选择状态', trigger: 'change' }],
}

const uaAirlineTypeGroup = ref([
  { label: '固定航线', value: 0 },
  { label: '临时航线', value: 1 },
  { label: '自定义', value: 2 },
])
const uaAirlineKindGroup = ref([
  { label: '自由', value: 0 },
  { label: '物流', value: 1 },
  { label: '文旅', value: 2 },
  { label: '农林', value: 3 },
  { label: '客运', value: 4 },
  { label: '政务', value: 5 },
])

const searchLine = async id => {
  loading.value = true
  try {
    const res = await api.airline_aggregate_list({ id, offset: 0, limit: 10 })
    if (res.list.length > 0) {
      const lineData = res.list[0]
      formData.id = lineData.id
      formData.cityCode = lineData.cityCode
      formData.airlineTitle = lineData.airlineTitle
      formData.airlineNo = lineData.airlineNo
      formData.airlineCategory = lineData.airlineCategory
      formData.airlineType = lineData.airlineType
      formData.airlineKind = lineData.airlineKind
      formData.airlineHeight = lineData.airlineHeight
      formData.airlineWidth = lineData.airlineWidth
      formData.airlineTags = lineData.airlineTags
      formData.memo = lineData.memo
      formData.statusInfo = { status: lineData.status, runningStatus: lineData.runningStatus }
      formData.displayStyle = JSON.stringify(lineData.displayStyle)
      formData.startPoint = lineData.startPoint
      formData.endPoint = lineData.endPoint
      formData.stopoverPoint = lineData.stopoverPoint
      formData.flightDistance = lineData.flightDistance
      formData.regionInfo = {
        regionCodes: String(...lineData.regionCodes) || '',
        regionType: lineData.regionType,
      }
      formData.rule_idList = lineData.ruleList?.map(rule => rule.id) || []
      formData.fileIds = lineData.fileIds || []
      // 确保默认规则始终存在
      if (!formData.rule_idList.includes(defaultRuleId.value)) {
        formData.rule_idList.push(defaultRuleId.value)
      }

      // 回显几何数据到地图
      if (lineData.geometries) {
        mapData.geometries = lineData.geometries
      }
      if (lineData.geoJson) {
        mapData.geoJson = [lineData.geoJson]
      }
      if (lineData.displayStyle) {
        mapData.displayStyle = lineData.displayStyle
      }
      mapData.id = lineData.id
    }
  } finally {
    loading.value = false
  }
}

watch(
  () => mapData.geometries,
  geometries => {
    if (geometries && geometries.length > 0) {
      formData.geometries = JSON.stringify(geometries)
      formData.geoJson = JSON.stringify(mapData.geoJson[0])
    } else {
      formData.geometries = '[]'
      formData.geoJson = '[]'
    }
  },
  { deep: true },
)

watch(
  () => formData.displayStyle,
  newVal => {
    if (newVal) {
      mapData.displayStyle = JSON.parse(newVal)
    } else {
      mapData.displayStyle = { color: '#f7712f' }
    }
  },
  { deep: true },
)

const exportIn = reactive({ json: '' })
const subIn = () => {
  if (!isJSON(exportIn.json)) {
    return ElMessage.error('导入失败，请输入正确的JSON格式')
  }
  const data = JSON.parse(exportIn.json)
  mapData.geometries = data
  ElMessage.success('导入成功')
}

const submit = async () => {
  try {
    await formRef.value.validate()
  } catch {
    return ElMessage.error('请检查表单是否填写完整')
  }

  if (!orgId.value) {
    ElMessage.error('请选择机构')
    return
  }

  // 检查几何体数据
  if (mapData.geometries.length === 0) {
    ElMessage.error('请选择地图模型')
    return
  }

  const result = await modelDetailsRef.value.validate()
  if (!result) {
    ElMessage.error('地图模型数据格式错误')
    return
  }

  const res = await ElMessageBox.confirm('确定提交吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
  if (res === 'cancel') {
    return
  }

  loading.value = true
  try {
    const params = {
      ...formData,
      status: formData.statusInfo.status,
      runningStatus: formData.statusInfo.runningStatus,
      regionCodes: JSON.stringify([formData.regionInfo.regionCodes]),
      regionType: formData.regionInfo.regionType,
      cityCode: formData.regionInfo.regionCodes,
      rule_idList: JSON.stringify(formData.rule_idList),
      fileIds: JSON.stringify(formData.fileIds),
    }
    const url = editType.value === 'add' ? 'airline_aggregate_add' : 'airline_aggregate_update'
    await api[url](params)
    ElMessage.success('保存成功')
    setTimeout(() => {
      loading.value = false
      setPath(getParam().from, null, { type: PathType.successBack })
    }, 1500)
  } catch (error) {
    loading.value = false
  }
}

const init = () => {
  const paramData = getParam().data
  if (paramData && paramData.id) {
    editType.value = 'edit'
    searchLine(paramData.id)
  }
}

const onMapDataUpdate = newMapData => {
  Object.assign(mapData, newMapData)
}

init()
</script>

<style scoped lang="less">
.airline-edit-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;

  .navigation {
    flex: 0 0 auto;
  }

  .main-content {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;

    .content-card {
      flex: 1 1 0;
      display: flex;
      flex-direction: row;
      height: 100%;

      .flex-row {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }

      .flex-col {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .map-area {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }

      .form-panel {
        flex: 1 1 0;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px;
        padding-top: 0;
        background-color: #ffffff; /* 白色背景 */
        border-radius: 0 16px 16px 0; /* 头部圆角 */

        .scroll {
          flex: 1 1 0;
          overflow-y: auto;
          height: v-bind(formHeight);
          /* 隐藏滚动条但保持滚动功能 */
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */

          // 表单样式
          :deep(.el-form) {
            .el-form-item {
              margin-bottom: 20px;

              .el-form-item__label {
                font-weight: 500;
                color: #333;
                margin-bottom: 8px;
              }

              .el-input,
              .el-select,
              .el-textarea {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
  :deep(.el-input-number .el-input__inner) {
    text-align: left;
  }
}
</style>
