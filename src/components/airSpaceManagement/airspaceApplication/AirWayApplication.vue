<template>
  <div class="airway-application-container">
    <CommonForm
      v-model="searchForm"
      :form-items="formItems"
      :default-show-count="3"
      @query="handleSearch"
      @reset="handleReset"
    >
      <!-- 标签筛选插槽 -->
      <template #tagSelection>
        <TagSelect ref="tagSelectRef" v-model="tagSelection" dic-type="航路标签" />
      </template>
    </CommonForm>

    <CommonTable
      ref="tableRef"
      v-model:page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :total="total"
      :auto-height="true"
      :table-props="tableConfig"
      @pagination-change="handlePaginationChange"
      @action-click="handleActionClick"
      @selection-change="handleSelectionChange"
    >
      <template #toolbar-right="{ selectedRows: selected }">
        <el-button type="primary" @click="handleAdd">新增申请</el-button>
      </template>
    </CommonTable>
  </div>
</template>

<script setup name="AirLineApplication">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as api from '@/request/api/airway'
import { airWayTableColumns, tableConfig, defaultAirWaySearchForm, airwayFormItems } from './config.js'
import CommonTable from '@/components/common/CommonTable/CommonTable.vue'
import CommonForm from '@/components/common/CommonForm/CommonForm.vue'
import TagSelect from '@/components/common/TagSelect/TagSelect.vue'
import useComponent, { PathType } from '@/hooks/useComponent.js'

const { setPath } = useComponent()

// 表格数据
const tableData = ref([])
const columns = ref(airWayTableColumns)
const loading = ref(false)
const total = ref(0)

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
})

const selectedRows = ref([])
const tableRef = ref(null)

// 表单配置
const formItems = ref(airwayFormItems)

// 搜索表单
const searchForm = reactive({
  ...defaultAirWaySearchForm,
})

const tagSelectRef = ref(null)
// 标签选择
const tagSelection = ref({
  matchAllTagList: [],
  matchAnyTagList: [],
})

// 加载表格数据
const loadTableData = () => {
  loading.value = true

  const params = {
    ...searchForm,
    uaAirline_matchAllTagList: JSON.stringify(tagSelection.value.matchAllTagList),
    uaAirline_matchAnyTagList: JSON.stringify(tagSelection.value.matchAnyTagList),
    limit: pagination.pageSize,
    offset: (pagination.page - 1) * pagination.pageSize,
  }

  api
    .airlineApprovalList(params)
    .then(res => {
      tableData.value = res.list || []
      total.value = res.total || 0
    })
    .catch(() => {
      tableData.value = []
      total.value = 0
    })
    .finally(() => {
      loading.value = false
    })
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, defaultAirWaySearchForm)
  tagSelectRef.value?.clearSelection()
  handleSearch()
}

// 分页变化
const handlePaginationChange = () => {
  loadTableData()
}

// 选择变化
const handleSelectionChange = selection => {
  selectedRows.value = selection
}

// 操作按钮点击
const handleActionClick = async ({ action, row }) => {
  if (action.code === 'edit') {
    // 编辑申请
    setPath(
      'airSpaceManagement/airspaceApplication/detail/AirLineApplicationEdit.vue',
      { type: 'AIRLINE', id: row.id, mode: 'edit' },
      { name: '编辑航线申请', type: PathType.edit },
    )
  } else if (action.code === 'commit') {
    // 提交审批
    await handleCommitApproval(row)
  } else if (action.code === 'cancel') {
    // 撤销申请
    await handleCancelApproval(row)
  } else if (action.code === 'delete') {
    // 删除申请
    // await handleDeleteApproval(row)
  }
}

// 提交审批
const handleCommitApproval = async row => {
  try {
    await ElMessageBox.confirm('确定要提交该申请进行审批吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    loading.value = true
    await api.airlineApprovalCommit({
      id: row.id,
    })

    ElMessage.success('提交成功')
    loadTableData()
  } finally {
    loading.value = false
  }
}

// 撤销申请
const handleCancelApproval = async row => {
  try {
    await ElMessageBox.confirm('确定要撤销该申请吗？撤销后无法重新提交审批。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    loading.value = true
    await api.airlineApprovalCancel({
      id: row.id,
    })

    ElMessage.success('撤销成功')
    loadTableData()
  } finally {
    loading.value = false
  }
}

// 新增申请
const handleAdd = () => {
  setPath(
    'airSpaceManagement/airspaceApplication/detail/AirLineApplicationEdit.vue',
    { type: 'AIRLINE', mode: 'add' },
    { name: '新增航线申请', type: PathType.add },
  )
}

// 组件挂载时加载数据
onMounted(() => {
  loadTableData()
})
</script>

<style scoped lang="less">
.airway-application-container {
  width: 100%;
  height: 100%;
}
</style>
