<template>
  <div v-loading="loading" class="airway-edit-container">
    <!-- 标题-->
    <div class="navigation">
      <CommonTitle is-back>
        <template v-if="mode !== 'view'" #default>
          <div class="opearte">
            <el-button type="primary" @click="submit">提交</el-button>
          </div>
        </template>
      </CommonTitle>
    </div>

    <div class="main-content">
      <div class="content-card">
        <el-row class="flex-row">
          <el-col :span="18" class="flex-col">
            <div class="map-area">
              <EditMap v-model:map-data="mapData" type="LINE" />
            </div>
          </el-col>
          <el-col :span="6" class="flex-col">
            <div class="form-panel">
              <el-tabs v-model="airSpaceEditTab" class="demo-tabs">
                <el-tab-pane label="基本信息" name="1">
                  <div class="scroll">
                    <el-form ref="formRef" :model="formData" label-position="top" :rules="rules">
                      <el-form-item label="审批机构" prop="approval_approvalOrgId">
                        <el-select
                          v-model="formData.approval_approvalOrgId"
                          placeholder="请选择审批机构"
                          style="width: 100%"
                        >
                          <el-option v-for="item in orgList" :key="item.id" :label="item.orgName" :value="item.id" />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="航路名称" prop="airway_name">
                        <el-input v-model="formData.airway_name" placeholder="请输入航路名称"></el-input>
                      </el-form-item>
                      <el-form-item label="航路编号" prop="airway_code">
                        <el-input v-model="formData.airway_code" placeholder="请输入航路编号"></el-input>
                      </el-form-item>
                      <el-form-item label="飞行区宽度(米)" prop="airway_flightAreaWidth">
                        <el-input-number
                          v-model="formData.airway_flightAreaWidth"
                          :controls="false"
                          style="width: 100%"
                          :precision="0"
                          :min="0"
                          :max="100"
                          placeholder="请输入"
                        ></el-input-number>
                      </el-form-item>
                      <el-form-item label="保护区宽度(米)" prop="airway_protectedAreaWidth">
                        <el-input-number
                          v-model="formData.airway_protectedAreaWidth"
                          :controls="false"
                          style="width: 100%"
                          :precision="0"
                          :min="0"
                          :max="500"
                          placeholder="请输入"
                        ></el-input-number>
                      </el-form-item>
                      <el-form-item label="航路标签" prop="airway_tags">
                        <TypeTags v-model="formData.airway_tags" dic-type="航路标签" />
                      </el-form-item>
                      <el-form-item label="起降场点" prop="airway_airportIds">
                        <el-select
                          v-model="formData.airway_airportIds"
                          placeholder="请选择起降场点"
                          style="width: 100%"
                          multiple
                          collapse-tags
                          collapse-tags-tooltip
                        >
                          <el-option
                            v-for="item in airportList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="备降场" prop="airway_emergencyAirportIds">
                        <el-select
                          v-model="formData.airway_emergencyAirportIds"
                          placeholder="请选择备降场"
                          style="width: 100%"
                          multiple
                          collapse-tags
                          collapse-tags-tooltip
                        >
                          <el-option
                            v-for="item in emergencyAirportList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                      <RegionSelect v-model="formData.regionInfo" prop-prefix="regionInfo" :show-region-type="false" />
                      <el-form-item label="状态" prop="statusInfo">
                        <StatusSelect v-model="formData.statusInfo" />
                      </el-form-item>
                      <el-form-item label="样式配置" prop="airway_displayStyle">
                        <StyleConfigEditor v-model="formData.airway_displayStyle" />
                      </el-form-item>
                      <el-form-item v-if="editType === 'edit'" label="规则" prop="rule_idList">
                        <el-select
                          v-model="formData.rule_idList"
                          placeholder="请选择"
                          style="width: 100%"
                          multiple
                          collapse-tags
                          collapse-tags-tooltip
                          clearable
                        >
                          <el-option
                            v-for="item in ruleList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                            :disabled="item.value === defaultRuleId"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="附件" prop="airway_fileIds">
                        <CommonUpload v-model="formData.airway_fileIds" multiple accept=".pdf" />
                      </el-form-item>
                      <el-form-item label="备注" prop="airway_remark">
                        <el-input v-model="formData.airway_remark" type="textarea" placeholder="请输入" :rows="3" />
                      </el-form-item>
                      <el-form-item label="航路坐标">
                        <ModelDetails
                          ref="modelDetailsRef"
                          v-model="mapData"
                          v-model:airway-points="formData.airway_airwayPoints"
                          :show-airway-fields="true"
                          @update:model-value="onMapDataUpdate"
                        />
                      </el-form-item>
                    </el-form>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="GeoJson" disabled name="2">
                  <div>
                    <el-input v-model="exportIn.json" type="textarea" :rows="26" />
                  </div>
                  <div class="subIn">
                    <el-button type="primary" @click="subIn">导入</el-button>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import * as api from '@/request/api/index.js'
import * as airwayApi from '@/request/api/airway.js'
import CommonTitle from '@/components/common/commonTitle/CommonTitle.vue'
import EditMap from '@/components/map/EditMap.vue'
import ModelDetails from '@/components/common/modelDetails/ModelDetails.vue'
import TypeTags from '@/components/common/typeTags/TypeTags.vue'
import StyleConfigEditor from '@/components/common/styleConfigEditor/StyleConfigEditor.vue'
import StatusSelect from '@/components/common/statusSelect/StatusSelect.vue'
import RegionSelect from '@/components/common/regionSelect/RegionSelect.vue'
import { isJSON } from '@/utils/helper.js'
import useOrg from '@/hooks/useOrg.js'
import { useRule } from '@/hooks/useRule.js'
import CommonUpload from '@/components/common/commonUpload/CommonUpload.vue'

const { orgId, orgList } = useOrg()

const { setPath, getParam } = useComponent()

const { ruleList } = useRule()

// 滚动高度
const { size } = defineProps({
  size: {
    type: Object,
    required: true,
  },
})
const formHeight = computed(() => size.height - 132 + 'px')

const airType = ref(getParam().data.type)
const mode = ref(getParam().data.mode)

const formRef = ref(null)
const loading = ref(false)
const editType = ref('add')
const airSpaceEditTab = ref('1')
const modelDetailsRef = ref(null)
const defaultRuleId = ref('gSJHvBW00O4HFDhXfLwa7') // 硬编码的默认规则ID

const formData = reactive({
  id: null,
  airway_orgId: orgId,
  approval_approvalOrgId: '',
  airway_code: '',
  cityCode: '',
  airway_name: '',
  airway_flightAreaWidth: 0,
  airway_protectedAreaWidth: 0,
  airway_airwayPoints: [],
  airway_airportIds: '[]',
  airway_emergencyAirportIds: '[]',
  statusInfo: { status: 1, runningStatus: 'RUNNING' },
  airway_displayStyle: JSON.stringify({ color: '#f7712f' }),
  airway_remark: '',
  airway_tags: [],
  rule_idList: [defaultRuleId.value],
  regionInfo: { regionCodes: '', regionType: '' },
  airway_fileIds: [],
})

const airportList = ref([])
const emergencyAirportList = ref([])

const mapData = reactive({
  displayStyle: { color: '#f7712f' },
  geometries: [],
  extra: {},
  geoJson: [],
  id: '',
})

const rules = {
  'approval_approvalOrgId': [{ required: true, message: '请选择审批机构', trigger: 'change' }],
  'airway_name': [{ required: true, message: '请输入航路名称', trigger: 'blur' }],
  'airway_code': [{ required: true, message: '请输入航路编号', trigger: 'blur' }],
  'airway_flightAreaWidth': [{ required: true, message: '请输入飞行区宽度', trigger: 'blur' }],
  'airway_protectedAreaWidth': [{ required: true, message: '请输入保护区宽度', trigger: 'blur' }],
  'airway_airportIds': [{ required: true, message: '请选择起降场点', trigger: 'change' }],
  'airway_emergencyAirportIds': [{ required: true, message: '请选择备降场', trigger: 'change' }],
  'statusInfo': [{ required: true, message: '请选择状态', trigger: 'change' }],
  'regionInfo.regionCodes': [{ required: true, message: '请选择所属地区', trigger: 'change' }],
}

//获取起降场点
const getAirportList = async () => {
  const res = await api.la_airport_list({
    offset: 0,
    limit: 999,
    siteTypes: JSON.stringify(['VERTISTOP', 'VERTIPORT']),
  })
  airportList.value = res.list.map(item => ({
    ...item,
    label: item.name,
    value: item.id,
  }))
}

getAirportList()

//获取备降场
const getEmergencyAirportList = async () => {
  const res = await api.la_airport_list({ offset: 0, limit: 999, forEmergency: 1 })
  emergencyAirportList.value = res.list.map(item => ({
    ...item,
    label: item.name,
    value: item.id,
  }))
}
getEmergencyAirportList()

const searchAirway = async id => {
  loading.value = true
  try {
    const res = await airwayApi.airwayApprovalList({ approval_id: id, offset: 0, limit: 10 })
    if (res.list.length > 0) {
      const airwayData = res.list[0].subject
      formData.approval_id = res.list[0].id
      formData.cityCode = airwayData.cityCode
      formData.approval_approvalOrgId = res.list[0].approvalOrgId
      formData.airway_code = airwayData.code
      formData.airway_name = airwayData.name
      formData.airway_flightAreaWidth = airwayData.flightAreaWidth
      formData.airway_protectedAreaWidth = airwayData.protectedAreaWidth
      formData.airway_tags = airwayData.tags
      formData.airway_airportIds = airwayData.airports?.map(item => String(item.id))
      formData.airway_emergencyAirportIds = airwayData.emergencyAirports?.map(item => String(item.id))
      formData.statusInfo = { status: airwayData.status, runningStatus: airwayData.runningStatus }
      formData.airway_remark = airwayData.remark
      formData.airway_displayStyle = JSON.stringify(airwayData.displayStyle)
      formData.rule_idList = airwayData.ruleList?.map(rule => rule.id) || []
      formData.airway_fileIds = airwayData.fileIds || []
      formData.regionInfo = {
        regionCodes: airwayData.cityCode || '',
        regionType: '',
      }
      // 确保默认规则始终存在
      if (!formData.rule_idList.includes(defaultRuleId.value)) {
        formData.rule_idList.push(defaultRuleId.value)
      }

      // 处理航路点数据
      if (airwayData.airwayPoints && airwayData.airwayPoints.length > 0) {
        formData.airway_airwayPoints = airwayData.airwayPoints
        // 将航路点转换为地图可识别的几何数据
        const coordinates = airwayData.airwayPoints.map(point => [point.longitude, point.latitude])

        mapData.geometries = [
          {
            type: 'LINE',
            coordinateList: coordinates,
          },
        ]
        mapData.extra.protectedAreaWidth = airwayData.protectedAreaWidth
        mapData.id = airwayData.id
      }
      if (airwayData.displayStyle) {
        mapData.displayStyle = airwayData.displayStyle
      }
    }
  } finally {
    loading.value = false
  }
}

watch(
  () => mapData.geometries,
  newGeometries => {
    if (newGeometries && newGeometries.length > 0) {
      const geometry = newGeometries[0]
      if (geometry.type === 'LINE' && geometry.coordinateList) {
        // 当坐标点数量与航路点数量不一致时，更新航路点
        if (geometry.coordinateList.length !== formData.airway_airwayPoints.length) {
          formData.airway_airwayPoints = geometry.coordinateList.map((coord, index) => {
            return (
              formData.airway_airwayPoints[index] || {
                code: '', // 航路点编号
                name: '', // 航路点名称
                longitude: coord[0], // 经度
                latitude: coord[1], // 纬度
                altitude: coord[2] || 0, // 海拔，默认为0
              }
            )
          })
        } else {
          // 更新现有航路点的坐标
          formData.airway_airwayPoints.forEach((point, index) => {
            point.longitude = geometry.coordinateList[index][0]
            point.latitude = geometry.coordinateList[index][1]
            point.altitude = geometry.coordinateList[index][2] || point.altitude
          })
        }
      }
    } else {
      formData.airway_airwayPoints = []
    }
  },
  { deep: true },
)

watch(
  () => formData.airway_displayStyle,
  newVal => {
    if (newVal) {
      mapData.displayStyle = JSON.parse(newVal)
    } else {
      mapData.displayStyle = { color: '#f7712f' }
    }
  },
  { deep: true },
)

watch(
  () => formData.airway_protectedAreaWidth,
  newVal => {
    mapData.extra.protectedAreaWidth = newVal
  },
)

watch(
  () => formData.airway_airwayPoints,
  newAirwayPoints => {
    if (mapData.geometries && mapData.geometries.length > 0) {
      const geometry = mapData.geometries[0]
      if (geometry.type === 'LINE') {
        const newCoordinates = newAirwayPoints.map(p => [p.longitude, p.latitude, p.altitude])
        // 避免不必要的更新和循环
        if (JSON.stringify(geometry.coordinateList) !== JSON.stringify(newCoordinates)) {
          geometry.coordinateList = newCoordinates
        }
      }
    }
  },
  { deep: true },
)

watch(
  () => formData.airway_airportIds,
  newAirportIds => {
    mapData.extra.airports = newAirportIds.map(id => airportList.value.find(item => item.value === id))
  },
)

watch(
  () => formData.airway_emergencyAirportIds,
  newAirportIds => {
    mapData.extra.emergencyAirports = newAirportIds.map(id =>
      emergencyAirportList.value.find(item => item.value === id),
    )
  },
)

const exportIn = reactive({ json: '' })
const subIn = () => {
  if (!isJSON(exportIn.json)) {
    return ElMessage.error('导入失败，请输入正确的JSON格式')
  }
  const data = JSON.parse(exportIn.json)
  mapData.geometries = data
  ElMessage.success('导入成功')
}

const submit = async () => {
  try {
    await formRef.value.validate()
  } catch {
    return ElMessage.error('请检查表单是否填写完整')
  }

  if (!orgId.value) {
    ElMessage.error('请选择机构')
    return
  }

  // 检查几何体数据
  if (mapData.geometries.length === 0) {
    ElMessage.error('请选择地图模型')
    return
  }

  const result = await modelDetailsRef.value.validate()
  if (!result) {
    ElMessage.error('地图模型数据格式错误')
    return
  }

  const res = await ElMessageBox.confirm('确定提交吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
  if (res === 'cancel') {
    return
  }

  loading.value = true
  try {
    const { regionInfo, ...restOfFormData } = formData
    const params = {
      ...restOfFormData,
      airway_airwayPoints: JSON.stringify(formData.airway_airwayPoints),
      airway_airportIds: JSON.stringify(formData.airway_airportIds),
      airway_emergencyAirportIds: JSON.stringify(formData.airway_emergencyAirportIds),
      airway_status: formData.statusInfo.status,
      airway_runningStatus: formData.statusInfo.runningStatus,
      rule_idList: JSON.stringify(formData.rule_idList),
      regionCodes: JSON.stringify([regionInfo.regionCodes]),
      airway_cityCode: regionInfo.regionCodes,
      airway_fileIds: JSON.stringify(formData.airway_fileIds),
    }
    const url = editType.value === 'add' ? 'airwayApprovalCreate' : 'airwayApprovalUpdate'
    await airwayApi[url](params)
    ElMessage.success('保存成功')
    setTimeout(() => {
      loading.value = false
      setPath(getParam().from, { tab: airType.value }, { type: PathType.successBack })
    }, 1500)
  } catch (error) {
    loading.value = false
  }
}

const init = () => {
  const paramData = getParam().data
  if (paramData && paramData.id) {
    editType.value = 'edit'
    searchAirway(paramData.id)
  }
}

const onMapDataUpdate = newMapData => {
  Object.assign(mapData, newMapData)
}

init()
</script>

<style scoped lang="less">
.airway-edit-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;

  .navigation {
    flex: 0 0 auto;
  }

  .main-content {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;

    .content-card {
      flex: 1 1 0;
      display: flex;
      flex-direction: row;
      height: 100%;

      .flex-row {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }

      .flex-col {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .map-area {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }

      .form-panel {
        flex: 1 1 0;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px;
        padding-top: 0;
        background-color: #ffffff; /* 白色背景 */
        border-radius: 0 16px 16px 0; /* 头部圆角 */

        .scroll {
          flex: 1 1 0;
          overflow-y: auto;
          height: v-bind(formHeight);
          /* 隐藏滚动条但保持滚动功能 */
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */

          // 表单样式
          :deep(.el-form) {
            .el-form-item {
              margin-bottom: 20px;

              .el-form-item__label {
                font-weight: 500;
                color: #333;
                margin-bottom: 8px;
              }

              .el-input,
              .el-select,
              .el-textarea {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
  :deep(.el-input-number .el-input__inner) {
    text-align: left;
  }
}
</style>
