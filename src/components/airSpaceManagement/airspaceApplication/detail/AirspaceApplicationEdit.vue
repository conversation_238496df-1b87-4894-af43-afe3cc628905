<template>
  <div v-loading="formContent.loading" class="detail">
    <!-- 标题-->
    <div class="navigation">
      <CommonTitle is-back>
        <template #default>
          <div class="opearte">
            <el-button type="primary" @click="submit">提交</el-button>
          </div>
        </template>
      </CommonTitle>
    </div>

    <div class="paint">
      <div class="card">
        <el-row class="flex-row">
          <el-col :span="18" class="flex-col">
            <div class="map-area">
              <EditMap v-model:map-data="mapData" :type="MapType.PLANE" />
            </div>
          </el-col>
          <el-col :span="6" class="flex-col">
            <div class="form-panel">
              <el-tabs v-model="airSpaceEditTab" class="demo-tabs" @tab-click="airSpaceEditTabShift">
                <el-tab-pane label="基本信息" name="1">
                  <div class="scroll">
                    <el-form ref="formRef" :model="formContent.param" :rules="formRules" label-position="top">
                      <el-form-item label="空域名称" prop="airspaceName">
                        <el-input v-model="formContent.param.airspaceName" placeholder="请输入1~16位字数" />
                      </el-form-item>

                      <el-form-item label="高度范围">
                        <AirSpaceType
                          v-model:bot="formContent.param.bot"
                          v-model:top="formContent.param.top"
                          v-model:type="formContent.param.airspaceType"
                          v-model:flight-level-ids="formContent.param.flightLevelIds"
                        />
                      </el-form-item>

                      <el-form-item label="来源" prop="source">
                        <el-select
                          v-model="formContent.param.source"
                          placeholder="请选择来源"
                          style="width: 100%"
                          disabled
                        >
                          <el-option label="空域划设" value="DELIMITATION" />
                          <el-option label="常用空域" value="COMMON" />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="空域标签" prop="airspaceTags">
                        <TypeTags v-model="formContent.param.airspaceTags" dic-type="空域标签" />
                      </el-form-item>
                      <el-switch
                        :model-value="formContent.param.startTime === '-1'"
                        active-text="长期有效"
                        inactive-text="指定时间"
                        style="margin-bottom: 12px"
                        @change="handleTermChange"
                      />
                      <el-form-item label="有效期开始" prop="startTime">
                        <el-date-picker
                          v-if="formContent.param.startTime !== '-1'"
                          v-model="formContent.param.startTime"
                          type="datetime"
                          placeholder="请选择开始时间"
                          format="YYYY-MM-DD HH:mm"
                          value-format="YYYYMMDDHHmm"
                          style="width: 100%"
                        />
                        <span v-else style="color: #909399; font-size: 14px">长期有效</span>
                      </el-form-item>

                      <el-form-item label="有效期结束" prop="endTime">
                        <el-date-picker
                          v-if="formContent.param.startTime !== '-1'"
                          v-model="formContent.param.endTime"
                          type="datetime"
                          placeholder="请选择结束时间"
                          format="YYYY-MM-DD HH:mm"
                          value-format="YYYYMMDDHHmm"
                          style="width: 100%"
                        />
                        <span v-else style="color: #909399; font-size: 14px">长期有效</span>
                      </el-form-item>

                      <RegionSelect v-model="formContent.param.regionInfo" prop-prefix="regionInfo" />

                      <el-form-item label="空域种类" prop="airspaceCategory">
                        <el-select
                          v-model="formContent.param.airspaceCategory"
                          placeholder="请选择空域种类"
                          style="width: 100%"
                          @change="handleAirspaceCategoryChange"
                        >
                          <el-option
                            v-for="item in airspaceCategoryOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="状态" prop="statusInfo">
                        <StatusSelect v-model="formContent.param.statusInfo" />
                      </el-form-item>

                      <el-form-item label="样式配置" prop="displayStyle">
                        <StyleConfigEditor v-model="formContent.param.displayStyle" />
                      </el-form-item>
                      <el-form-item v-if="formContent.editType === 'edit'" label="规则" prop="rule_idList">
                        <el-select
                          v-model="formContent.param.rule_idList"
                          placeholder="请选择规则"
                          style="width: 100%"
                          multiple
                          collapse-tags
                          collapse-tags-tooltip
                          clearable
                        >
                          <el-option
                            v-for="item in ruleList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                            :disabled="item.value === defaultRuleId"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item label="附件" prop="fileIds">
                        <CommonUpload v-model="formContent.param.fileIds" multiple accept=".pdf" />
                      </el-form-item>
                      <el-form-item label="空域简介" prop="description">
                        <el-input
                          v-model="formContent.param.description"
                          type="textarea"
                          placeholder="请输入"
                          :rows="3"
                        />
                      </el-form-item>

                      <el-form-item label="模型信息">
                        <ModelDetails ref="modelDetailsRef" v-model="mapData" @update:model-value="onMapDataUpdate" />
                      </el-form-item>
                    </el-form>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="GeoJson" disabled name="2">
                  <div>
                    <el-input v-model="exportIn.json" type="textarea" :rows="26" />
                  </div>
                  <div class="subIn">
                    <div class="btn y" @click="subIn">导入</div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import * as api from '@/request/api/airspace.js'

// 组件导入
import EditMap from '@/components/map/EditMap.vue'
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import AirSpaceType from '@/components/common/AirSpaceType.vue'
import TypeTags from '@/components/common/typeTags/TypeTags.vue'
import ModelDetails from '@/components/common/modelDetails/ModelDetails.vue'
import StyleConfigEditor from '@/components/common/styleConfigEditor/StyleConfigEditor.vue'
import { airspaceCategoryOptions } from '../config.js'
import StatusSelect from '@/components/common/statusSelect/StatusSelect.vue'
import RegionSelect from '@/components/common/regionSelect/RegionSelect.vue'
import CommonUpload from '@/components/common/commonUpload/CommonUpload.vue'

import useOrg from '@/hooks/useOrg.js'
import { useRule } from '@/hooks/useRule.js'
import { MapType } from '@/data/MapConstant.js'

const { orgId } = useOrg()
const { ruleList } = useRule()

// 滚动高度
const { size } = defineProps(['size'])
const formHeight = computed(() => size.height - 132 + 'px')

const { setPath, getParam } = useComponent()
const activeLevelIndex = ref(null)
const defaultRuleId = ref(null)

/** 表单 **/
const formContent = reactive({
  param: {
    // 基本信息
    approval_approvalOrgId: 'cb8upAzuM07uQyNpzuj05',
    orgId,
    airspaceName: '',
    description: '',
    airspaceCategory: '',
    flightLevelIds: [],
    bot: 0,
    botInclude: 1,
    top: 0,
    topInclude: 1,
    source: 'DELIMITATION',
    airspaceType: '',
    startTime: null,
    endTime: null,
    regionInfo: { regionCodes: '', regionType: '' },
    statusInfo: { status: 1, runningStatus: 'RUNNING' },
    displayStyle: JSON.stringify({ color: '#f7712f' }),
    airspaceTags: '[]',
    geometries: '[]',
    geoJson: '{}',
    // 规则相关
    rule_idList: [],
    fileIds: [],
  },
  loading: false,
  editType: 'add',
})

// 表单验证规则
const formRules = computed(() => ({
  'airspaceName': [{ required: true, message: '请输入空域名称', trigger: 'blur' }],
  'airspaceCategory': [{ required: true, message: '请选择空域种类', trigger: 'change' }],
  'source': [{ required: true, message: '请选择来源', trigger: 'change' }],
  'airspaceType': [{ required: true, message: '请选择空域类型', trigger: 'change' }],
  'startTime': [{ required: true, message: '请选择有效期开始时间', trigger: 'change' }],
  'endTime': [{ required: true, message: '请选择有效期结束时间', trigger: 'change' }],
  'statusInfo': [{ required: true, message: '请选择状态', trigger: 'change' }],
  'description': [{ max: 2000, message: '描述不能超过2000个字符', trigger: 'blur' }],
  'regionInfo.regionCodes': [{ required: true, message: '请选择所属地区', trigger: 'change' }],
  'regionInfo.regionType': [{ required: true, message: '请选择区域类型', trigger: 'change' }],
}))

const mapData = reactive({
  displayStyle: { color: '#f7712f' },
  geometries: [],
  extra: {},
  geoJson: [],
  id: '',
})

watch(
  mapData,
  () => {
    console.log('mapData', mapData)
  },
  { deep: true },
)

watch(
  () => formContent.param.displayStyle,
  newVal => {
    if (newVal) {
      mapData.displayStyle = JSON.parse(newVal)
    } else {
      mapData.displayStyle = { color: '#f7712f' }
    }
  },
  { deep: true },
)

// 表单引用
const formRef = ref()
const modelDetailsRef = ref()

// 编辑切换
const airSpaceEditTab = ref('1')
const airSpaceEditTabShift = () => {}

const layerKey = ref(null)

// 初始化
initData()
function initData() {
  const params = getParam()
  //   activeLevelIndex.value = params.data.activeLevelIndex
  formContent.param.bot = params.data.bottom || 0
  formContent.param.top = params.data.top || 0
  layerKey.value = params.data.layerKey
  if (params.data.id) {
    formContent.editType = 'edit'
    searchAirspace(params.data.id)
  } else {
    formContent.editType = 'add'
  }
}

function searchAirspace(id) {
  let p = {
    approval_id: id,
    offset: 0,
    limit: 10,
  }
  api.approvalList(p).then(response => {
    const airspace = response.list[0].subject
    if (airspace) {
      formContent.param.approval_id = response.list[0].id
      formContent.param.airspaceName = airspace.airspaceName
      formContent.param.airspaceCategory = airspace.airspaceCategory
      formContent.param.bot = airspace.bot
      formContent.param.top = airspace.top
      formContent.param.botInclude = airspace.botInclude
      formContent.param.topInclude = airspace.topInclude
      formContent.param.source = airspace.source
      formContent.param.airspaceType = airspace.airspaceType
      formContent.param.startTime = String(airspace.startTime)
      formContent.param.endTime = String(airspace.endTime)
      formContent.param.regionInfo = {
        regionCodes: String(...airspace.regionCodes) || '',
        regionType: airspace.regionType,
      }
      formContent.param.statusInfo = { status: airspace.status, runningStatus: airspace.runningStatus }
      formContent.param.description = airspace.description
      formContent.param.flightLevelIds = airspace.flightLevelIds
      formContent.param.airspaceTags = JSON.stringify(airspace.airspaceTags) || '[]'
      formContent.param.displayStyle = JSON.stringify(airspace.displayStyle)
      formContent.param.rule_idList = airspace.ruleList?.map(rule => rule.id) || []
      formContent.param.fileIds = airspace.fileIds || []
      handleAirspaceCategoryChange(airspace.airspaceCategory, true) // 更新默认规则ID

      if (airspace.geoJson) {
        mapData.geoJson = [airspace.geoJson]
      }
      if (airspace.geometries) {
        mapData.geometries = airspace.geometries
      }
      if (airspace.displayStyle) {
        mapData.displayStyle = airspace.displayStyle
      }
      mapData.id = airspace.id
    }
  })
}

// 监听地图数据变化，更新表单
watch(
  () => mapData.geometries,
  geometries => {
    if (geometries && geometries.length > 0) {
      formContent.param.geometries = JSON.stringify(geometries)
      formContent.param.geoJson = JSON.stringify(mapData.geoJson[0])
    } else {
      formContent.param.geometries = '[]'
      formContent.param.geoJson = '[]'
    }
  },
  { deep: true },
)

/** GeoJson - 导入 **/
const exportIn = reactive({
  json: '',
})

const subIn = () => {
  formContent.exportIn = true
  const parsedData = JSON.parse(exportIn.json)
  mapData.geometries = parsedData
}

// 提交
const submit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    if (!orgId.value) {
      ElMessage.error('请选择机构')
      return
    }

    if (formContent.param.airspaceType === '') {
      ElMessage.error('请选择空域类型')
      return
    }

    // 检查几何体数据
    if (mapData.geometries.length === 0) {
      ElMessage.error('请选择地图模型')
      return
    }

    // 模型验证
    const result = await modelDetailsRef.value.validate()
    if (!result) {
      ElMessage.error('地图模型数据格式错误')
      return
    }

    //如果不是长期有效，判断开始时间是否大于结束时间
    if (formContent.param.startTime !== '-1' && formContent.param.endTime !== '-1') {
      if (formContent.param.startTime > formContent.param.endTime) {
        ElMessage.error('开始时间不能大于结束时间')
        return
      }
    }

    const res = await ElMessageBox.confirm('确定提交吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    if (res === 'cancel') {
      return
    }

    let url = formContent.editType === 'add' ? 'approvalCreate' : 'approvalUpdate'
    formContent.loading = true
    const { statusInfo, regionInfo, ...restParams } = formContent.param
    api[url]({
      ...restParams,
      status: statusInfo.status,
      runningStatus: statusInfo.runningStatus,
      regionCodes: JSON.stringify([regionInfo.regionCodes]),
      regionType: regionInfo.regionType,
      cityCode: regionInfo.regionCodes,
      flightLevelIds: JSON.stringify(formContent.param.flightLevelIds),
      rule_idList: JSON.stringify(formContent.param.rule_idList),
      fileIds: JSON.stringify(formContent.param.fileIds),
    })
      .then(() => {
        ElMessage({ message: '保存成功', type: 'success' })
        setTimeout(() => {
          formContent.loading = false
          setPath(getParam().from, { activeLevelIndex: activeLevelIndex.value || 0 }, { type: PathType.successBack })
        }, 1500)
      })
      .catch(err => {
        formContent.loading = false
      })
  } catch (error) {}
}

const onMapDataUpdate = newMapData => {
  Object.assign(mapData, newMapData)
}

// 处理有效期开始时间长期有效开关
const handleTermChange = value => {
  if (value) {
    formContent.param.startTime = '-1'
    formContent.param.endTime = '-1'
  } else {
    formContent.param.startTime = null
    formContent.param.endTime = null
  }
}

// 监听空域种类，联动更新规则
const handleAirspaceCategoryChange = (category, isInitialLoad = false) => {
  if (!category) return

  let ruleName = ''
  if (category === 'SUITABLE') {
    ruleName = '适飞规则'
  } else if (category === 'CONTROLLED') {
    ruleName = '管制规则'
  } else if (category === 'NO_FLY') {
    ruleName = '禁飞规则'
  }

  if (ruleName) {
    const targetRule = ruleList.find(rule => rule.label.includes(ruleName))
    if (targetRule) {
      defaultRuleId.value = targetRule.value // 更新默认规则ID
      if (formContent.editType === 'add') {
        formContent.param.rule_idList = [targetRule.value]
      } else if (!isInitialLoad) {
        // 编辑模式下，如果默认规则没被选中，则添加进去
        if (!formContent.param.rule_idList.includes(targetRule.value)) {
          formContent.param.rule_idList.push(targetRule.value)
        }
      }
    } else {
      defaultRuleId.value = null
      if (formContent.editType === 'add') {
        formContent.param.rule_idList = []
      }
    }
  } else {
    defaultRuleId.value = null
    if (formContent.editType === 'add') {
      formContent.param.rule_idList = []
    }
  }
}
</script>

<style scoped lang="less">
.detail {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  .navigation {
    flex: 0 0 auto;
  }
  .paint {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    .card {
      flex: 1 1 0;
      display: flex;
      flex-direction: row;
      height: 100%;
      .flex-row {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }
      .flex-col {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      .map-area {
        flex: 1 1 0;
        height: 100%;
        display: flex;
      }
      .form-panel {
        flex: 1 1 0;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 20px;
        padding-top: 0;
        background-color: #ffffff; /* 白色背景 */
        border-radius: 0 16px 16px 0; /* 头部圆角 */
        .scroll {
          flex: 1 1 0;
          overflow-y: auto;
          height: v-bind(formHeight);
          /* 隐藏滚动条但保持滚动功能 */
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */
          // 表单样式
          :deep(.el-form) {
            .el-form-item {
              margin-bottom: 20px;

              .el-form-item__label {
                font-weight: 500;
                color: #333;
                margin-bottom: 8px;
              }

              .el-input,
              .el-select,
              .el-textarea {
                width: 100%;
              }
            }
          }
        }
        .mbg {
          margin-bottom: 12px;
        }
        .pdr {
          padding-right: 12px;
        }
        .line {
          font-size: 16px;
          color: var(--main-font-color9);
          .title {
            position: relative;
            margin-bottom: 12px;
          }
          .title.w:after {
            position: absolute;
            content: '*';
            color: var(--el-color-danger);
            top: 2px;
            left: -12px;
          }
          .in {
            padding: 0 1px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .col4 {
              width: 40%;
            }
            .col6 {
              width: 60%;
            }
          }
          .note {
            height: 30px;
            line-height: 30px;
          }
        }
        // 模型信息表格样式
        .tb {
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          overflow: hidden;

          .th {
            height: 48px;
            background: #f5f7fa;
            box-sizing: border-box;
            color: #606266;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e4e7ed;

            .name {
              width: 50%;
              text-align: center;
              font-weight: 500;
            }
          }

          .td {
            height: 48px;
            text-align: center;
            line-height: 48px;
            color: #606266;
            border-bottom: 1px solid #e4e7ed;

            &:last-child {
              border-bottom: none;
            }
          }
        }

        // 地图信息样式
        .map-info {
          .map-info-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .map-info-label {
              font-weight: 500;
              color: #606266;
              min-width: 80px;
            }

            .map-info-value {
              color: #303133;
              flex: 1;
            }
          }
        }
      }
    }
  }
}
</style>
