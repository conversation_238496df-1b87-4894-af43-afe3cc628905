<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="handleDialogClosed"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" label-position="right">
      <el-form-item label="审批结果" prop="approvalResult">
        <el-select
          v-model="formData.approvalResult"
          placeholder="请选择审批结果"
          style="width: 100%"
          :disabled="loading"
        >
          <el-option label="撤销" value="CANCELLED" />
          <el-option label="通过" value="AGREE" />
          <el-option label="不通过" value="DISAGREE" />
        </el-select>
      </el-form-item>

      <el-form-item label="审批意见" prop="approvalOpinion">
        <el-input
          v-model="formData.approvalOpinion"
          type="textarea"
          :rows="4"
          placeholder="请输入审批意见（必填）"
          :disabled="loading"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="loading" @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ loading ? '提交中...' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 组件属性定义
const props = defineProps({
  // 控制弹窗显示隐藏
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 申请记录ID
  recordId: {
    type: String,
    default: '',
  },
  // 申请记录信息（用于显示标题等）
  recordData: {
    type: Object,
    default: () => ({}),
  },
})

// 事件定义
const emit = defineEmits(['update:modelValue', 'success', 'cancel'])

// 响应式数据
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  approvalResult: '',
  approvalOpinion: '',
})

// 表单验证规则
const formRules = {
  approvalResult: [{ required: true, message: '请选择审批结果', trigger: 'change' }],
}

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

const dialogTitle = computed(() => {
  const recordName = props.recordData?.subject?.airspaceName || props.recordData?.airspaceName || '申请记录'
  return `审批 - ${recordName}`
})

// 监听弹窗显示，重置表单
watch(dialogVisible, newVal => {
  if (newVal) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  formData.approvalResult = ''
  formData.approvalOpinion = ''
  formRef.value?.clearValidate()
}

// 提交审批
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    if (!props.recordId) {
      ElMessage.error('缺少申请记录ID')
      return
    }

    loading.value = true

    // 构造提交参数
    const submitData = {
      id: props.recordId,
      approvalResult: formData.approvalResult,
      approvalOpinion: formData.approvalOpinion,
    }

    // 触发成功事件，让父组件处理API调用
    emit('success', submitData)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('表单验证失败，请检查输入内容')
    }
  }
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
  emit('cancel')
}

// 弹窗关闭后的处理
const handleDialogClosed = () => {
  resetForm()
  loading.value = false
}

// 暴露方法给父组件
const setLoading = isLoading => {
  loading.value = isLoading
}

const closeDialog = () => {
  dialogVisible.value = false
}

defineExpose({
  setLoading,
  closeDialog,
})
</script>

<style scoped lang="less">
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 20px 10px 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}

:deep(.el-select) {
  width: 100%;
}
</style>
