<template>
  <div class="airspace-application">
    <CommonTitle
      :title="`${activeTab === 'AIRSPACE' ? '空域申请' : activeTab === 'AIRLINE' ? '航线申请' : '航路申请'}`"
    >
      <template #default>
        <el-tabs v-model="activeTab" class="airspace-tabs" @tab-click="handleTabClick">
          <el-tab-pane label="空域" name="AIRSPACE" />
          <el-tab-pane label="航线" name="AIRLINE" />
          <el-tab-pane label="航路" name="AIRWAY" />
        </el-tabs>
      </template>
    </CommonTitle>

    <!-- 动态组件渲染区域 -->
    <div class="airspace-content">
      <component :is="currentComponent" :key="activeTab" v-bind="componentProps" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, markRaw } from 'vue'
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import AirSpace from './AirSpaceApplication.vue'
import AirLine from './AirLineApplication.vue'
import AirWay from './AirWayApplication.vue'
import useComponent from '@/hooks/useComponent.js'
const { getParam } = useComponent()

// 当前激活的标签页
const activeTab = ref('AIRSPACE')

// 组件映射
const componentMap = {
  AIRSPACE: markRaw(AirSpace),
  AIRLINE: markRaw(AirLine),
  AIRWAY: markRaw(AirWay),
}

// 当前渲染的组件
const currentComponent = computed(() => componentMap[activeTab.value] || AirSpace)

// 标签页切换事件处理
const handleTabClick = tab => {
  activeTab.value = tab.name
}

const componentProps = computed(() => {
  switch (activeTab.value) {
    case 'AIRSPACE':
      return { showTitle: false }
    case 'AIRLINE':
      return { showTitle: true }
    case 'AIRWAY':
      return { showTitle: true }
    default:
      return {}
  }
})

// 组件挂载时的初始化
onMounted(() => {
  activeTab.value = getParam()?.data?.tab || 'AIRSPACE'
})

// 暴露给父组件的方法
defineExpose({
  // 获取当前激活的标签页
  getCurrentTab: () => activeTab.value,

  // 刷新当前组件
  refreshCurrentComponent: () => {
    // 通过改变 key 来强制重新渲染组件
    const currentKey = activeTab.value
    activeTab.value = ''
    nextTick(() => {
      activeTab.value = currentKey
    })
  },
})
</script>

<style scoped lang="less">
.airspace-application {
  width: 100%;
  height: 100%;
}

// 自定义标签页样式
.airspace-tabs {
  // 移除默认的下边框
  :deep(.el-tabs__header) {
    margin: 0;
    border-bottom: none;
  }

  // 标签页样式
  :deep(.el-tabs__item) {
    width: 70px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-regular);
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    margin-right: 8px;
    background-color: var(--el-fill-color-blank);
    transition: all 0.3s ease;
    padding: 0;

    &:hover {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary-light-7);
      background-color: var(--el-color-primary-light-9);
    }

    &.is-active {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
      font-weight: 600;
    }
  }

  // 隐藏默认的激活指示器
  :deep(.el-tabs__active-bar) {
    display: none;
  }

  // 标签页导航容器
  :deep(.el-tabs__nav-wrap) {
    &::after {
      display: none;
    }
  }
}
</style>
