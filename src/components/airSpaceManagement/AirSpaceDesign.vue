<template>
  <div v-loading="state.loading" class="airspace-design">
    <CommonTitle />
    <CommonForm v-model="searchForm" :form-items="formItems" @query="handleSearch" @reset="handleReset" />
    <main class="main-content">
      <div class="height-tabs">
        <el-tabs v-model="state.activeLevelIndex" :lazy="false">
          <el-tab-pane
            v-for="level in state.levels"
            :key="level.index"
            :lazy="false"
            :label="level.label"
            :name="String(level.index)"
          />
        </el-tabs>
        <header class="panel-header">
          <el-button type="primary" @click="addAirspace">新增</el-button>
        </header>
      </div>
      <div class="box">
        <div class="map-section">
          <ShowMap />
        </div>
        <div class="control-panel">
          <div v-if="currentLevel" class="panel-content">
            <div class="airspace-list">
              <div
                v-for="airspace in currentLevel?.airspaces || []"
                :key="airspace.id"
                class="airspace-item"
                :class="{ selected: airspace.select }"
                @click="selectAirspace(airspace)"
              >
                <div class="item-content">
                  <div class="item-icon">
                    <img :src="getImg('areaIcon.png')" alt="区域图标" />
                  </div>
                  <div class="item-info">
                    <h4 class="item-title">{{ airspace.airspaceName }}</h4>
                    <p class="item-desc">{{ airspace.memo }}</p>
                  </div>
                </div>
                <div class="item-actions">
                  <el-dropdown>
                    <button class="action-btn">
                      <el-icon><MoreFilled /></el-icon>
                    </button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click.stop="editAirspace(airspace)">编辑</el-dropdown-item>
                        <el-dropdown-item @click.stop="deleteAirspace(airspace)">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { reactive, computed, watch, ref } from 'vue'
import * as api from '@/request/api/index'
import { ElMessageBox, ElMessage } from 'element-plus'
import { getImg } from '@/utils/getAsset.js'
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import CommonForm from '@/components/common/CommonForm/CommonForm.vue'
import { defaultSearchForm, pointFormItems } from './config.js'
import { useEventBus } from '@vueuse/core'
import { MoreFilled } from '@element-plus/icons-vue'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import ShowMap from '@/components/map/ShowMap.vue'

const { size } = defineProps(['size'])
const formHeight = computed(() => size.height - 132 + 'px')

const { setPath } = useComponent()

// 表单配置
const formItems = ref(pointFormItems)

// 搜索表单
const searchForm = reactive({
  ...defaultSearchForm,
})

const LAYER_NAMES = {
  0: 'lowAirSpace',
  1: 'middleAirSpace',
  2: 'LhighAirSpace',
}

const layerRefreshBus = useEventBus('LayerRefresh')

const state = reactive({
  levels: [],
  activeLevelIndex: '0',
  loading: false,
})

const mapData = reactive({
  ids: [],
  displayStyle: {},
})

const activeLevelIndexNumber = computed(() => parseInt(state.activeLevelIndex, 10))

const currentLevel = computed(() => {
  if (state.activeLevelIndex < 0 || !state.levels[activeLevelIndexNumber.value]) {
    return null
  }
  return state.levels[activeLevelIndexNumber.value]
})

const layerName = computed(() => LAYER_NAMES[activeLevelIndexNumber.value] || 'LhighAirSpace')

const updateMapDisplay = () => {
  const selectedAirspace = currentLevel.value?.airspaces.find(a => a.select)
  if (selectedAirspace) {
    mapData.ids = [selectedAirspace.airspaceModelId]
    mapData.displayStyle = selectedAirspace.displayStyle?.length ? selectedAirspace.displayStyle[0] : {}
  } else {
    mapData.ids = []
    mapData.displayStyle = {}
  }
}

const selectAirspace = selectedAirspace => {
  if (!currentLevel.value) return
  currentLevel.value.airspaces.forEach(a => {
    a.select = false
  })
  selectedAirspace.select = true
  updateMapDisplay()
}

const loadAirspacesForLevel = async level => {
  if (!level) return
  try {
    state.loading = true
    const airspaces = await fetchAggregateList(level.id)
    level.airspaces = airspaces.map(a => ({ ...a, select: false }))
    level.dataLoaded = true
  } finally {
    state.loading = false
  }
}

const loadData = async () => {
  try {
    state.loading = true
    const response = await api.flight_level_list({ offset: 0, limit: 10 })
    state.levels = response.list.map((level, index) => ({
      id: level.id,
      label: level.flightLevelName,
      bottom: level.bot,
      top: level.top,
      key: level.flightLevelNo,
      index: index,
      dataLoaded: false,
      airspaces: [],
    }))
    if (state.levels.length > 0) {
      state.activeLevelIndex = '0'
    }
  } finally {
    state.loading = false
  }
}

const fetchAggregateList = async flightLevelId => {
  const params = {
    flightLevelId: flightLevelId,
    ...searchForm,
    source: 'DELIMITATION',
    deleted: 0,
    offset: 0,
    limit: 99,
  }
  const response = await api.airspace_aggregate_list(params)
  return response.list || []
}

const addAirspace = () => {
  if (!currentLevel.value) {
    ElMessage.error('请先选择一个高度层')
    return
  }
  setPath(
    'airSpaceManagement/AirSpaceEdit.vue',
    {
      bottom: currentLevel.value.bottom,
      top: currentLevel.value.top,
      flightLevelId: currentLevel.value.id,
    },
    { name: '新增空域', type: PathType.add },
  )
}

const editAirspace = airspace => {
  setPath(
    'airSpaceManagement/AirSpaceEdit.vue',
    {
      id: airspace.id,
      bottom: currentLevel.value.bottom,
      top: currentLevel.value.top,
      flightLevelId: currentLevel.value.id,
    },
    { name: '编辑空域', type: PathType.edit },
  )
}

const deleteAirspace = airspace => {
  ElMessageBox.confirm('是否确定删除当前空域', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await api.airspace_aggregate_delete({ idList: JSON.stringify([airspace.id]) })
    ElMessage.success('删除成功')
    if (currentLevel.value) {
      await loadAirspacesForLevel(currentLevel.value)
      updateMapDisplay()
    }
  })
}

const handleSearch = () => {
  // 当执行搜索时，所有已加载的数据都可能失效，因此将它们标记为未加载
  state.levels.forEach(level => {
    level.dataLoaded = false
  })
  // 重新加载当前层的数据
  if (currentLevel.value) {
    loadAirspacesForLevel(currentLevel.value)
  }
}

const handleReset = () => {
  Object.assign(searchForm, defaultSearchForm)
  handleSearch()
}

// 监听当前激活的高度层变化，自动加载数据
watch(currentLevel, async newLevel => {
  if (newLevel && !newLevel.dataLoaded) {
    await loadAirspacesForLevel(newLevel)
  }
  updateMapDisplay()
})

watch(
  layerName,
  newLayerName => {
    layerRefreshBus.emit({
      key: newLayerName,
      data: null,
    })
  },
  { immediate: true },
)

loadData()
</script>

<style scoped lang="less">
.airspace-design {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
  :deep(.el-tabs__nav-wrap:after) {
    height: 0;
  }
}

.main-content {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-top: 0;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  min-height: 0;

  .height-tabs {
    display: flex;
    justify-content: space-between;
    border-bottom: 2px solid #e8e8e8;
    flex: 0 0 auto;
    background: #fff;
    margin-bottom: 16px;
  }
}

.box {
  display: flex;
  flex: 1 1 0;
  min-height: 0;
  max-height: 100%;
  gap: 16px;
  overflow: hidden;
}

.map-section {
  flex: 1 1 0;
  background: #f8f9fa;
  border-radius: 8px;
  position: relative;
}

/* 右侧控制面板 */
.control-panel {
  flex: 0 0 360px;
  background: #fff;
  display: flex;
  flex-direction: column;
  // border: 1px solid #e8e8e8;
  // border-radius: 8px;
}

.panel-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.panel-header {
  flex: 0 0 auto;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  // border-bottom: 1px solid #e8e8e8; /* 添加底部边框分隔 */
  background: #fff;
}

.type-tabs {
  display: flex;
  gap: 4px;
}

.type-tab {
  padding: 8px 16px;
  border: none;
  background: #f5f5f5;
  color: #666;
  border-radius: px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    background: #e8e8e8;
  }

  &.active {
    background: var(--main-color);
    color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.add-btn {
  padding: 8px 16px;
  border: none;
  background: var(--main-color);
  color: #fff;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background: var(--main-color-hover, #e67e22);
  }

  &::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 20px;
    background: #e8e8e8;
  }
}

.airspace-list {
  flex: 1 1 0;
  overflow-y: auto;
  height: v-bind(formHeight);
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.airspace-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 8px;
  background: #fff;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--main-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.selected {
    background: rgba(247, 113, 47, 0.08);
    border-color: var(--main-color);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.item-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1 1 0;
  min-width: 0;
}

.item-icon {
  flex: 0 0 auto;

  img {
    width: 40px;
    height: 40px;
    border-radius: 4px;
  }
}

.item-info {
  flex: 1 1 0;
  min-width: 0;
}

.item-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  // white-space: nowrap;
}

.item-desc {
  margin: 0;
  font-size: 12px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  // white-space: nowrap;
}

.item-actions {
  flex: 0 0 auto;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--main-color);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(247, 113, 47, 0.1);
  }
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: #e8e8e8;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__nav-scroll) {
  padding: 16px 0;
}

@media (max-width: 1200px) {
  .control-panel {
    flex: 0 0 280px;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .control-panel {
    flex: 0 0 300px;
  }
}
</style>
