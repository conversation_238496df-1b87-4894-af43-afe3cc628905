<template>
  <div class="airway-application-container">
    <CommonForm v-model="searchForm" :form-items="formItems" @query="handleSearch" @reset="handleReset">
      <!-- 标签筛选插槽 -->
      <template #tagSelection>
        <TagSelect ref="tagSelectRef" v-model="tagSelection" dic-type="航路标签" />
      </template>
      <template #cityCodeList>
        <AreaSelect v-model="searchForm.cityCodeList" />
      </template>
    </CommonForm>

    <CommonTable
      ref="tableRef"
      v-model:page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :total="total"
      :auto-height="true"
      :table-props="tableConfig"
      @pagination-change="handlePaginationChange"
      @action-click="handleActionClick"
      @selection-change="handleSelectionChange"
    >
    </CommonTable>
    <!-- 审批弹窗 -->
    <ApprovalDialog
      ref="approvalDialogRef"
      v-model="approvalDialogVisible"
      :record-id="currentRecord.id"
      :record-data="currentRecord"
      :approval-type="'航线审批'"
      @success="handleApprovalSubmit"
      @cancel="approvalDialogVisible = false"
    />
  </div>
</template>

<script setup name="AirWayApplication">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as api from '@/request/api/airway'
import { airWayTableColumns, tableConfig, defaultAirWaySearchForm, airwayFormItems } from './config.js'
import CommonTable from '@/components/common/CommonTable/CommonTable.vue'
import CommonForm from '@/components/common/CommonForm/CommonForm.vue'
import TagSelect from '@/components/common/TagSelect/TagSelect.vue'
import ApprovalDialog from './components/ApprovalDialog.vue'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import AreaSelect from '@/components/common/areaSelect/AreaSelect.vue'
import useOrg from '@/hooks/useOrg.js'

const { orgId } = useOrg()

const { setPath } = useComponent()

// 表格数据
const tableData = ref([])
const columns = ref(airWayTableColumns)
const loading = ref(false)
const total = ref(0)

const approvalDialogVisible = ref(false)
const currentRecord = ref({})
const approvalDialogRef = ref()

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
})

const selectedRows = ref([])
const tableRef = ref(null)

// 表单配置
const formItems = ref(airwayFormItems)

// 搜索表单
const searchForm = reactive({
  ...defaultAirWaySearchForm,
  cityCodeList: [],
})

const tagSelectRef = ref(null)
// 标签选择
const tagSelection = ref({
  matchAllTagList: [],
  matchAnyTagList: [],
})

// 加载表格数据
const loadTableData = () => {
  loading.value = true

  const params = {
    ...searchForm,
    approval_approvalOrgId: orgId.value,
    approval_cityCodeList: JSON.stringify(searchForm.cityCodeList),
    airway_matchAllTagList: JSON.stringify(tagSelection.value.matchAllTagList),
    airway_matchAnyTagList: JSON.stringify(tagSelection.value.matchAnyTagList),
    limit: pagination.pageSize,
    offset: (pagination.page - 1) * pagination.pageSize,
  }

  // 如果审批状态为空，添加默认状态列表
  if (!searchForm.approval_approvalStatus) {
    params.approval_approvalStatusList = JSON.stringify(['TO_APPROVE', 'APPROVING', 'FINISH'])
    delete params.approval_approvalStatus
  }

  api
    .airwayApprovalList(params)
    .then(res => {
      tableData.value = res.list || []
      total.value = res.total || 0
    })
    .catch(() => {
      tableData.value = []
      total.value = 0
    })
    .finally(() => {
      loading.value = false
    })
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, defaultAirWaySearchForm)
  tagSelectRef.value?.clearSelection()
  handleSearch()
}

// 分页变化
const handlePaginationChange = () => {
  loadTableData()
}

// 选择变化
const handleSelectionChange = selection => {
  selectedRows.value = selection
}

// 操作按钮点击
const handleActionClick = async ({ action, row }) => {
  if (action.code === 'view') {
    // 查看详情
    setPath(
      'airSpaceManagement/airspaceApplication/detail/AirWayApplicationEdit.vue',
      { type: 'AIRWAY', id: row.id, mode: 'view' },
      { name: '航路申请详情', type: PathType.normal },
    )
  } else if (action.code === 'receive') {
    // 接收
    await handleReceiveApproval(row)
  } else if (action.code === 'approve') {
    // 审批
    await handleApproveApproval(row)
  }
}

// 接收
const handleReceiveApproval = async row => {
  try {
    await ElMessageBox.confirm('确定要接收该申请吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    loading.value = true
    await api.airwayApprovalAccept({
      id: row.id,
    })

    ElMessage.success('接收成功')
  } finally {
    loading.value = false
    loadTableData()
  }
}

// 审批
const handleApproveApproval = async row => {
  approvalDialogVisible.value = true
  currentRecord.value = row
}

// 审批成功
const handleApprovalSubmit = submitData => {
  api.airwayApprovalApprove(submitData).then(() => {
    ElMessage.success('审批成功')
    loadTableData()
    approvalDialogVisible.value = false
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadTableData()
})
</script>

<style scoped lang="less">
.airway-application-container {
  width: 100%;
  height: 100%;
}
</style>
