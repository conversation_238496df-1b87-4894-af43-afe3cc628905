<template>
  <div class="airspace-application-container">
    <CommonForm
      v-model="searchForm"
      :form-items="formItems"
      :default-show-count="3"
      @query="handleSearch"
      @reset="handleReset"
    >
      <!-- 标签筛选插槽 -->
      <template #tagSelection>
        <TagSelect ref="tagSelectRef" v-model="tagSelection" dic-type="空域标签" />
      </template>
    </CommonForm>

    <CommonTable
      ref="tableRef"
      v-model:page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :total="total"
      :auto-height="true"
      :table-props="tableConfig"
      :selection-control="selectionControl"
      @pagination-change="handlePaginationChange"
      @action-click="handleActionClick"
      @selection-change="handleSelectionChange"
    >
    </CommonTable>
    <!-- 审批弹窗 -->
    <ApprovalDialog
      ref="approvalDialogRef"
      v-model="approvalDialogVisible"
      :record-id="currentRecord.id"
      :record-data="currentRecord"
      @success="handleApprovalSubmit"
      @cancel="approvalDialogVisible = false"
    />
  </div>
</template>

<script setup name="AirspaceApplication">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as api from '@/request/api/airspace'
import { tableColumns, tableConfig, defaultSearchForm, airspaceFormItems, selectionControl } from './config.js'
import CommonTable from '@/components/common/CommonTable/CommonTable.vue'
import CommonForm from '@/components/common/CommonForm/CommonForm.vue'
import TagSelect from '@/components/common/TagSelect/TagSelect.vue'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import ApprovalDialog from './components/ApprovalDialog.vue'

const { setPath } = useComponent()

// 表格数据
const tableData = ref([])
const columns = ref(tableColumns)
const loading = ref(false)
const total = ref(0)

const approvalDialogVisible = ref(false)
const currentRecord = ref({})
const approvalDialogRef = ref()

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
})

const selectedRows = ref([])
const tableRef = ref(null)

// 表单配置
const formItems = ref(airspaceFormItems)

// 搜索表单
const searchForm = reactive({
  ...defaultSearchForm,
})

const tagSelectRef = ref(null)
// 标签选择
const tagSelection = ref({
  matchAllTagList: [],
  matchAnyTagList: [],
})

// 加载表格数据
const loadTableData = () => {
  loading.value = true

  const params = {
    ...searchForm,
    airspace_matchAllTagList: JSON.stringify(tagSelection.value.matchAllTagList),
    airspace_matchAnyTagList: JSON.stringify(tagSelection.value.matchAnyTagList),
    limit: pagination.pageSize,
    offset: (pagination.page - 1) * pagination.pageSize,
  }

  api
    .approvalList(params)
    .then(res => {
      tableData.value = res.list || []
      total.value = res.total || 0
    })
    .catch(() => {
      tableData.value = []
      total.value = 0
    })
    .finally(() => {
      loading.value = false
    })
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, defaultSearchForm)
  tagSelectRef.value?.clearSelection()
  handleSearch()
}

// 分页变化
const handlePaginationChange = () => {
  loadTableData()
}

// 选择变化
const handleSelectionChange = selection => {
  selectedRows.value = selection
}

// 检查是否可以批量提交
const canBatchCommit = selected => {
  return selected.every(row => row.approvalStatus === 'TO_COMMIT')
}

// 检查是否可以批量删除
const canBatchDelete = selected => {
  return selected.every(row => ['TO_COMMIT', 'CANCELLED'].includes(row.approvalStatus))
}

// 操作按钮点击
const handleActionClick = async ({ action, row }) => {
  if (action.code === 'view') {
    // 查看详情
    setPath(
      'airSpaceManagement/airspaceApplication/detail/AirspaceApplicationEdit.vue',
      { id: row.id, mode: 'view' },
      { name: '查看空域申请详情', type: PathType.view },
    )
  } else if (action.code === 'edit') {
    // 编辑申请
    setPath(
      'airSpaceManagement/airspaceApplication/detail/AirspaceApplicationEdit.vue',
      { id: row.id, mode: 'edit' },
      { name: '编辑空域申请', type: PathType.edit },
    )
  } else if (action.code === 'commit') {
    // 提交审批
    await handleCommitApproval(row)
  } else if (action.code === 'cancel') {
    // 撤销申请
    await handleCancelApproval(row)
  } else if (action.code === 'receive') {
    // 接收
    await handleReceiveApproval(row)
  } else if (action.code === 'approve') {
    // 审批
    await handleApproveApproval(row)
  } else if (action.code === 'delete') {
    // 删除申请
    // await handleDeleteApproval(row)
  }
}

// 提交审批
const handleCommitApproval = async row => {
  try {
    await ElMessageBox.confirm('确定要提交该申请进行审批吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    loading.value = true
    await api.approvalCommit({
      id: row.id,
    })

    ElMessage.success('提交成功')
    loadTableData()
  } finally {
    loading.value = false
  }
}

// 接收
const handleReceiveApproval = async row => {
  try {
    await ElMessageBox.confirm('确定要通过该申请吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    loading.value = true
    await api.approvalAccept({
      id: row.id,
    })

    ElMessage.success('通过成功')
    loadTableData()
  } finally {
    loading.value = false
  }
}

// 审批
const handleApproveApproval = async row => {
  approvalDialogVisible.value = true
  currentRecord.value = row
}

// 审批成功
const handleApprovalSubmit = submitData => {
  api.approvalApprove(submitData).then(() => {
    ElMessage.success('审批成功')
    loadTableData()
    approvalDialogVisible.value = false
  })
}

// 撤销申请
const handleCancelApproval = async row => {
  try {
    await ElMessageBox.confirm('确定要撤销该申请吗？撤销后无法重新提交审批。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    loading.value = true
    await api.approvalApplicantCancel({
      id: row.id,
    })

    ElMessage.success('撤销成功')
    loadTableData()
  } finally {
    loading.value = false
  }
}

// 删除申请
// const handleDeleteApproval = async row => {
//   try {
//     await ElMessageBox.confirm('确定要删除该申请吗？删除后无法恢复。', '提示', {
//       confirmButtonText: '确定',
//       cancelButtonText: '取消',
//       type: 'warning',
//     })

//     loading.value = true
//     await api.airspace_approval_delete({
//       approval_id: row.id,
//     })

//     ElMessage.success('删除成功')
//     loadTableData()
//   } catch (error) {
//     if (error !== 'cancel') {
//       ElMessage.error('删除失败')
//     }
//   } finally {
//     loading.value = false
//   }
// }

// 批量操作
const handleBatchOperation = operation => {
  const idList = selectedRows.value.map(item => item.id)

  if (operation === 'commit') {
    ElMessageBox.confirm('确认提交选中的申请进行审批吗？', '批量提交确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      try {
        loading.value = true
        // 逐个提交，因为API不支持批量提交
        for (const id of idList) {
          await api.approvalCommit({ approval_id: id })
        }
        ElMessage.success('批量提交成功')
        loadTableData()
        tableRef.value?.clearSelection()
      } finally {
        loading.value = false
      }
    })
  } else if (operation === 'delete') {
    ElMessageBox.confirm('确认删除选中的申请吗？删除后无法恢复。', '批量删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      try {
        loading.value = true
        // 这里需要根据实际API调整批量删除逻辑
        await api.approvalBatchDelete({
          approval_idList: JSON.stringify(idList),
        })
        ElMessage.success('批量删除成功')
        loadTableData()
        tableRef.value?.clearSelection()
      } catch (error) {
        ElMessage.error('批量删除失败')
      } finally {
        loading.value = false
      }
    })
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadTableData()
})
</script>

<style scoped lang="less">
.airspace-application-container {
  width: 100%;
  height: 100%;
}
</style>
