import { mapStatusToText } from '@/utils/helper.js'
import useOrg from '@/hooks/useOrg.js'
const { orgId } = useOrg()

// 权限判断函数
const hasEditPermission = row => {
  // 如果是市级权限，可以编辑所有数据
  if (orgId.value === 'cb8upAzuM07uQyNpzuj05') {
    return true
  }
  // 区县级权限，只能编辑自己组织的数据
  return row.orgId === orgId.value
}

// 审批状态选项
export const approvalStatusOptions = [
  { label: '待申请人提交', value: 'TO_COMMIT' },
  { label: '待主管单位审批', value: 'TO_APPROVE' },
  { label: '申请人已撤销', value: 'CANCELLED' },
  { label: '主管单位审批中', value: 'APPROVING' },
  { label: '主管单位审批结束', value: 'FINISH' },
]

// 审批结果选项
export const approvalResultOptions = [
  { label: '撤销', value: 'CANCELLED' },
  { label: '通过', value: 'AGREE' },
  { label: '不通过', value: 'DISAGREE' },
]

// 主体类型选项
export const subjectTypeOptions = [
  { label: '空域', value: 'AIRSPACE' },
  { label: '无人机航线', value: 'UA_AIRLINE' },
  { label: '空域', value: 'LA_AIRWAY' },
]

export const airspaceCategoryOptions = [
  { label: '适飞区域', value: 'SUITABLE' },
  { label: '管制区域', value: 'CONTROLLED' },
  { label: '禁飞区域', value: 'NO_FLY' },
  // { label: '协调军民航单位划设区域', value: 'COORDINATED_DELIMITATION' },
  // { label: '按需划设区域', value: 'ON_DEMAND' },
  // { label: '活动区域', value: 'ACTIVITY' },
  // { label: '协调军民航单位申请使用区域', value: 'COORDINATED_USAGE' },
  // { label: '军航活动区域', value: 'MILITARY' },
  // { label: '直升机场区域', value: 'HELIPORT' },
]
export const commonAirspaceCategoryOptions = [{ label: '适飞区域', value: 'SUITABLE' }]

// 表格列配置
export const tableColumns = [
  {
    prop: 'subject.airspaceName',
    label: '空域名称',
    width: 200,
    minWidth: 150,
  },
  {
    prop: 'subjectType',
    label: '主体类型',
    minWidth: 100,
    type: 'tag',
    tagText: row => {
      const option = subjectTypeOptions.find(opt => opt.value === row.subjectType)
      return option ? option.label : row.subjectType
    },
    tagTypeMap: {
      AIRSPACE: 'primary',
      UA_AIRLINE: 'success',
      LA_AIRWAY: 'warning',
    },
  },
  {
    prop: 'applicantName',
    label: '申请人',
    minWidth: 120,
  },
  {
    prop: 'creatorName',
    label: '创建人',
    minWidth: 120,
  },
  {
    prop: 'approvalStatus',
    label: '审批状态',
    minWidth: 150,
    type: 'tag',
    tagText: row => {
      const option = approvalStatusOptions.find(opt => opt.value === row.approvalStatus)
      return option ? option.label : row.approvalStatus
    },
    tagTypeMap: {
      TO_COMMIT: 'info',
      TO_APPROVE: 'warning',
      CANCELLED: 'danger',
      APPROVING: 'primary',
      FINISH: 'success',
    },
  },
  {
    prop: 'approvalResult',
    label: '审批结果',
    minWidth: 100,
    type: 'tag',
    tagText: row => {
      if (!row.approvalResult) return '-'
      const option = approvalResultOptions.find(opt => opt.value === row.approvalResult)
      return option ? option.label : row.approvalResult
    },
    tagTypeMap: {
      CANCELLED: 'info',
      AGREE: 'success',
      DISAGREE: 'danger',
    },
  },
  {
    prop: 'approvalOpinion',
    label: '审批意见',
    minWidth: 100,
  },
  // {
  //   prop: 'createTime',
  //   label: '创建时间',
  //   minWidth: 160,
  //   formatter: row => {
  //     if (!row.createTime) return '-'
  //     // 将 yyyyMMddHHmmss 格式转换为可读格式
  //     const timeStr = row.createTime.toString()
  //     if (timeStr.length === 14) {
  //       return `${timeStr.substring(0, 4)}-${timeStr.substring(4, 6)}-${timeStr.substring(6, 8)} ${timeStr.substring(8, 10)}:${timeStr.substring(10, 12)}:${timeStr.substring(12, 14)}`
  //     }
  //     return timeStr
  //   },
  // },
  {
    fixed: 'right',
    label: '操作',
    type: 'action',
    width: 250,
    actions: [
      {
        label: '编辑',
        type: 'primary',
        size: 'small',
        code: 'edit',
        visible: row => row.approvalStatus === 'TO_COMMIT' && hasEditPermission(row),
      },
      {
        label: '提交审批',
        type: 'success',
        size: 'small',
        code: 'commit',
        visible: row => row.approvalStatus === 'TO_COMMIT' && hasEditPermission(row),
      },
      {
        label: '撤销申请',
        type: 'warning',
        size: 'small',
        code: 'cancel',
        visible: row => ['TO_APPROVE'].includes(row.approvalStatus) && hasEditPermission(row),
      },
      {
        label: '接收',
        type: 'success',
        size: 'small',
        code: 'receive',
        visible: row => row.approvalStatus === 'TO_APPROVE' && hasEditPermission(row),
      },
      {
        label: '审批',
        type: 'primary',
        size: 'small',
        code: 'approve',
        visible: row => row.approvalStatus === 'APPROVING' && hasEditPermission(row),
      },
      {
        label: '详情',
        type: 'primary',
        size: 'small',
        code: 'view',
        visible: row => ['FINISH', 'TO_APPROVE', 'APPROVING'].includes(row.approvalStatus),
      },
      // {
      //   label: '删除',
      //   type: 'danger',
      //   size: 'small',
      //   code: 'delete',
      //   visible: row => ['TO_COMMIT', 'CANCELLED'].includes(row.approvalStatus) && hasEditPermission(row),
      // },
    ],
  },
]

// 表格配置
export const tableConfig = {
  stripe: true,
  border: false,
  showHeader: true,
  highlightCurrentRow: true,
  emptyText: '暂无数据',
}

// 搜索表单默认值
export const defaultSearchForm = {
  airspace_nameLike: undefined,
  approval_approvalStatus: undefined,
  approval_approvalResult: undefined,
  creatorNameLike: undefined,
  deleted: 0,
}

// 表单配置项
export const airspaceFormItems = [
  {
    type: 'input',
    prop: 'airspace_nameLike',
    label: '空域名称',
    placeholder: '请输入空域名称模糊查询',
    span: 8,
    props: {
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'approval_approvalStatus',
    label: '审批状态',
    placeholder: '请选择审批状态',
    span: 8,
    options: approvalStatusOptions,
    props: {
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'approval_approvalResult',
    label: '审批结果',
    placeholder: '请选择审批结果',
    span: 8,
    options: approvalResultOptions,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 8,
  },
]

// 选择权限控制函数
export const selectionControl = (row, index) => {
  return hasEditPermission(row)
}
