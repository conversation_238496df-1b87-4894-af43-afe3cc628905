<template>
  <div class="detail" :style="{ height: store.mainHeight + 'px' }">
    <!-- 标题-->
    <div class="navigation">
      <div class="note">
        <i class="ri-arrow-left-line" @click="setPathBack"></i>
        <div class="title">{{ paintTitle }}</div>
      </div>
      <div class="opearte">
        <div class="btn y" @click="submit">提交</div>
      </div>
    </div>

    <div class="paint" :style="{ height: store.paintHeight + 'px' }">
      <div class="card">
        <el-row>
          <el-col :span="18">
            <div :style="{ height: store.paintHeight - 19 + 'px' }">
              <AirSpaceDesignMap v-model:map-data="mapData" :map-ids="mapIds" type="PLANE" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="piece">
              <el-tabs v-model="airSpaceEditTab" class="demo-tabs" @tab-click="airSpaceEditTabShift">
                <el-tab-pane label="基本信息" name="1">
                  <div class="scroll" :style="{ height: store.paintHeight - 122 + 'px' }">
                    <div class="line mbg">
                      <div class="title w">飞行区域名称</div>
                      <div class="in">
                        <el-input
                          v-model="formContent.param.airspace_airspaceName"
                          placeholder="请输入1~16位字数"
                          size="large"
                        ></el-input>
                      </div>
                    </div>
                    <!--                    <div class="line mbg" v-show="getParam().type === 'edit'">
                      <div class="title w">附加规则</div>
                      <div class="in mbg"></div>
                    </div>-->
                    <div class="line mbg">
                      <AirSpaceType
                        v-model:if-airport="ifAirport"
                        v-model:disabled="formContent.heightDisabled"
                        v-model:bot="formContent.bot"
                        v-model:top="formContent.top"
                        v-model:type="formContent.param.airspace_airspaceType"
                      ></AirSpaceType>
                    </div>
                    <!--                    <div class="line mbg">
                                          <div class="title w">空域来源</div>
                                          <div class="in mbg">
                                            <el-select
                                              v-model="formContent.param.airspace_source"
                                              placeholder="请选择"
                                              size="large"
                                              style="width: 100%"
                                            >
                                              <el-option
                                                v-for="item in airspaceSource.g"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                              />
                                            </el-select>
                                          </div>
                                        </div>-->
                    <div class="line mbg">
                      <div class="title">空域简介</div>
                      <div class="in">
                        <el-input
                          v-model="formContent.param.airspace_description"
                          type="textarea"
                          placeholder="请输入"
                          :rows="3"
                        />
                      </div>
                    </div>

                    <!--                    <div class="line mbg">-->
                    <!--                      <div class="title">异常区(米)</div>-->
                    <!--                      <div class="in">-->
                    <!--                        <el-input-->
                    <!--                          placeholder="请选择"-->
                    <!--                          type="number"-->
                    <!--                          size="large"-->
                    <!--                          v-model="formContent.param.rule_bufferWidth"-->
                    <!--                        ></el-input>-->
                    <!--                      </div>-->
                    <!--                    </div>-->
                    <div class="line mbg">
                      <div class="title w">模型信息-{{ formContent.mapData.typeName }}</div>
                      <div v-show="formContent.mapData.type === 'CIRCLE'" class="tb">
                        <div class="th">
                          <div class="name">圆心坐标</div>
                        </div>
                        <el-row v-if="formContent.mapData.centerCoordinate">
                          <el-col :span="12"
                            ><div class="td">{{ formContent.mapData.centerCoordinate[0].toFixed(5) }}</div></el-col
                          >
                          <el-col :span="12"
                            ><div class="td">{{ formContent.mapData.centerCoordinate[1].toFixed(5) }}</div></el-col
                          >
                        </el-row>
                        <div class="th">
                          <div class="name">半径(米)</div>
                        </div>
                        <el-row>
                          <el-col :span="12"
                            ><div class="td">{{ formContent.mapData.radius }}</div></el-col
                          >
                        </el-row>
                      </div>
                      <div v-show="formContent.mapData.type === 'POLYGON'" class="tb">
                        <div class="th">
                          <div class="name">途经点坐标</div>
                        </div>
                        <el-row v-for="(item, index) in formContent.mapData.shellCoordinateList" :key="index">
                          <el-col :span="12"
                            ><div class="td">{{ item[0].toFixed(5) }}</div></el-col
                          >
                          <el-col :span="12"
                            ><div class="td">{{ item[1].toFixed(5) }}</div></el-col
                          >
                        </el-row>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="GeoJson" name="2">
                  <div>
                    <el-input v-model="exportIn.json" type="textarea" :rows="26" />
                  </div>
                  <div class="subIn">
                    <div class="btn y" @click="subIn">导入</div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
// 组件
import AirSpaceDesignMap from '@/components/map/AirSpaceDesignMap.vue'
// 地图
const mapData = reactive([])
const mapIds = reactive([])

import AirSpaceType from '@/components/common/AirSpaceType.vue'
const ifAirport = ref(false)
// 尺寸
import systemStore from '@/store/data.js'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import { ElMessage } from 'element-plus'
import * as api from '@/api'
import * as common from '@/utils/common'
const store = systemStore()
// 跳转
const { setPathBack, getParam, setPath, pathData } = useComponent()
// 标题
const paintTitle = ref(pathData.value.name)

//编辑切换
const airSpaceEditTab = ref('1')
const airSpaceEditTabShift = () => {}
// 上一页切换的tab位置
let address = []
/** 表单 **/
const formContent = reactive({
  param: {
    airspace_airspaceName: '',
    airspace_description: '',
    airspace_source: 'COMMON',
    airspace_airspaceType: '',
    airspace_displayStyle: JSON.stringify({ color: '#000' }),
    airspace_startTime: -1,
    airspace_endTime: -1,
    airspace_status: 1,
    airspaceModel_airspaceModelNo: Math.random(),
    airspaceModel_flightLevelIds: '',
    airspaceModel_geometries: '',
    rule_id: 'QkYpHGsPg1lhQ_eEX8k3p',
  },
  bot: 0,
  top: 0,
  heightDisabled: true,
  lockRepeat: false,
  editType: 'add',
  mapData: {
    type: '',
    typeName: '',
    centerCoordinate: '',
    radius: '',
    geojson: '',
    shellCoordinateList: [],
  },
})
// 初始化
initData()
function initData() {
  address = getParam().data.address
  formContent.top = getParam().data.top
  formContent.bot = getParam().data.bot
  if (getParam().data.id) {
    formContent.editType = 'edit'
    searchAirspace(getParam().data.id)
  } else {
    formContent.editType = 'add'
    formContent.param.airspaceModel_flightLevelIds = JSON.stringify([getParam().data.heightId])
  }
}

function searchAirspace(id) {
  let p = {
    id: id,
    offset: 0,
    limit: 10,
  }
  api.airspace_aggregate_list(p).then(data => {
    if (data.code === '00000') {
      let d = data.data.list
      if (d.length > 0) {
        formContent.param.airspace_id = d[0].id
        formContent.param.airspace_airspaceName = d[0].airspaceName
        formContent.param.airspace_source = d[0].source
        formContent.param.airspace_description = d[0].description
        formContent.param.airspaceModel_id = d[0].airspaceModel.id
        formContent.param.airspaceModel_flightLevelIds = d[0].airspaceModel.flightLevelIds
        if (d[0].airspaceModel.geometries[0].type === 'CIRCLE') {
          formContent.mapData.typeName = '圆'
          formContent.mapData.centerCoordinate = d[0].airspaceModel.geometries[0].centerCoordinate
          formContent.mapData.radius = d[0].airspaceModel.geometries[0].radius
        }
        mapIds.push(d[0].airspaceModel.id)
        formContent.param.rule_id = d[0].ruleList[0].id
      }
    } else {
      ElMessage.error(data.msg)
    }
  })
}
/** 编辑 **/
// 地图交互
watch(mapData, o => {
  if (o.length > 0) {
    formContent.mapData.type = o[0].type
    /*formContent.geojson = o[0].geoJson[0]*/
    if (o[0].type === 'CIRCLE') {
      formContent.mapData.typeName = '圆'
      formContent.mapData.centerCoordinate = o[0].centerCoordinate
      formContent.mapData.radius = o[0].radius
    } else {
      formContent.mapData.typeName = '多边形'
      formContent.mapData.shellCoordinateList = o[0].shellCoordinateList
    }
  } else {
    formContent.mapData.type = ''
    formContent.mapData.typeName = ''
    formContent.mapData.centerCoordinate = []
    formContent.mapData.radius = ''
    /*formContent.geojson = []*/
  }
})

/** GeoJson - 导入 **/
const exportIn = reactive({
  json: '',
})

const subIn = () => {
  if (common.isJSON(exportIn.json)) {
    ElMessage({
      message: t('message.exportInSuccess'),
      type: 'success',
    })
  } else {
    ElMessage.error(t('message.exportInFalse'))
    return
  }
  formContent.exportIn = true
  let d = JSON.parse(exportIn.json)
  let x = []
  d.map(item => {
    console.log(item)
    if (item.type === 'POLYGON') {
      x.push({
        type: item.type,
        shellCoordinateList: item.shellCoordinateList,
      })
      formContent.mapData.type = 'POLYGON'
    } else {
      x.push({
        type: item.type,
        radius: item.radius,
        centerCoordinate: item.centerCoordinate,
      })
      formContent.mapData.type = 'CIRCLE'
    }
    console.log(x)
    formContent.param.airspaceModel_geometries = JSON.stringify(x)
  })
}

// 提交
const submit = () => {
  if (formContent.lockRepeat) {
    ElMessage.error(t('message.lockError'))
    return
  }
  if (formContent.param.airspace_airspaceName === '') {
    ElMessage.error(t('message.airspaceNameLack'))
  } else if (formContent.param.airspace_airspaceName.length > 16) {
    ElMessage.error(t('message.airspaceNameLongError'))
  } else if (formContent.mapData.type === '') {
    ElMessage.error(t('message.modelLack'))
  } else if (formContent.param.airspace_description.length > 200) {
    ElMessage.error(t('message.memoLongError'))
  } else {
    if (!formContent.exportIn) {
      if (formContent.mapData.type === 'CIRCLE') {
        formContent.param.airspaceModel_geometries = JSON.stringify([
          {
            centerCoordinate: formContent.mapData.centerCoordinate,
            radius: formContent.mapData.radius,
            type: 'CIRCLE',
          },
        ])
      } else {
        formContent.param.airspaceModel_geometries = JSON.stringify([
          { shellCoordinateList: formContent.mapData.shellCoordinateList, type: 'POLYGON' },
        ])
      }
    }
    let url = formContent.editType === 'add' ? 'airspace_aggregate_add' : 'airspace_aggregate_update'
    formContent.lockRepeat = true
    api[url](formContent.param).then(data => {
      if (data.code === '00000') {
        ElMessage({
          message: t('message.saveSuccess'),
          type: 'success',
        })
        setTimeout(() => {
          formContent.lockRepeat = false
          setPath('3-2', { address: address }, PathType.successBack)
        }, common.globalTime())
      } else {
        formContent.lockRepeat = false
        ElMessage.error(data.msg)
      }
    })
  }
}
</script>

<style scoped lang="scss">
.detail {
  .piece {
    .scroll {
      padding-left: 10px;
      overflow-y: scroll;
    }
    .mbg {
      margin-bottom: 12px;
    }
    .pdr {
      padding-right: 12px;
    }
    .line {
      font-size: 16px;
      color: var(--main-font-color9);
      .title {
        position: relative;
        margin-bottom: 12px;
      }
      .title.w:after {
        position: absolute;
        content: '*';
        color: var(--el-color-danger);
        top: 2px;
        left: -12px;
      }
      .in {
        padding: 0 1px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .col4 {
          width: 40%;
        }
        .col6 {
          width: 60%;
        }
      }
      .note {
        height: 30px;
        line-height: 30px;
      }
    }
    .tb {
      .th {
        height: 48px;
        background: var(--main-box-color4);
        box-sizing: border-box;
        color: var(--main-font-color4);
        display: flex;
        align-items: center;
        .name {
          width: 50%;
          text-align: center;
          font-weight: bold;
        }
      }
      .td {
        height: 48px;
        text-align: center;
        line-height: 48px;
      }
    }
  }
}
</style>
