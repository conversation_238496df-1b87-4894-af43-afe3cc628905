<template>
  <div class="container" :style="{ height: store.mainHeight + 'px' }">
    <div class="navigation">
      <div class="title">{{ paintTitle }}</div>
      <div class="tab">
        <div
          v-for="(item, index) in filter.g"
          :key="index"
          class="btn"
          :class="[item.select ? 'select' : '']"
          @click="shiftTab(item, index)"
        >
          {{ item.title }}
        </div>
      </div>
    </div>

    <div class="search">
      <div v-if="filter.index === 0" class="card">
        <el-row>
          <el-col :span="6">
            <div class="line">
              <div class="label">障碍物名称</div>
              <div class="in">
                <el-input
                  v-model="tableConfig1.search.nameLike"
                  placeholder="请输入障碍物名称模糊查询"
                  size="large"
                ></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">障碍物地址</div>
              <div class="in">
                <el-input
                  v-model="tableConfig1.search.placeAddrLike"
                  placeholder="请输入障碍物地址模糊查询"
                  size="large"
                ></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="operate">
              <div class="btn n" @click="search1">查询</div>
              <div class="btn d" @click="reset1">重置</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div v-if="filter.index === 1" class="card">
        <el-row>
          <el-col :span="6">
            <div class="line">
              <div class="label">障碍物名称</div>
              <div class="in">
                <el-input
                  v-model="tableConfig2.search.nameLike"
                  placeholder="请输入线路名称模糊查询"
                  size="large"
                ></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">障碍物地址</div>
              <div class="in">
                <el-input
                  v-model="tableConfig2.search.placeAddrLike"
                  placeholder="请输入线路地址模糊查询"
                  size="large"
                ></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="operate">
              <div class="btn n" @click="search2">查询</div>
              <div class="btn d" @click="reset2">重置</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div v-if="filter.index === 2" class="card">
        <el-row>
          <el-col :span="6">
            <div class="line">
              <div class="label">障碍物名称</div>
              <div class="in">
                <el-input
                  v-model="tableConfig3.search.nameLike"
                  placeholder="请输入区域名称模糊查询"
                  size="large"
                ></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">障碍物地址</div>
              <div class="in">
                <el-input
                  v-model="tableConfig3.search.placeAddrLike"
                  placeholder="请输入区域地址模糊查询"
                  size="large"
                ></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="operate">
              <div class="btn n" @click="search3">查询</div>
              <div class="btn d" @click="reset3">重置</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="content" :style="{ height: contentHeight + 'px' }">
      <div v-if="filter.index === 0" class="card">
        <div class="filter">
          <div class="operate">
            <div class="btn n" @click="add">新增</div>
            <div class="btn d" @click="del">删除</div>
          </div>
        </div>
        <Table v-model:config="tableConfig1" v-model:reload="reload1" @edit="edit" @select="selectTa"></Table>
      </div>
      <div v-if="filter.index === 1" class="card">
        <div class="filter">
          <div class="operate">
            <div class="btn n" @click="add">新增</div>
            <div class="btn d" @click="del">删除</div>
          </div>
        </div>
        <Table v-model:config="tableConfig2" v-model:reload="reload2" @edit="edit" @select="selectTb"></Table>
      </div>
      <div v-if="filter.index === 2" class="card">
        <div class="filter">
          <div class="operate">
            <div class="btn n" @click="add">新增</div>
            <div class="btn d" @click="del">删除</div>
          </div>
        </div>
        <Table v-model:config="tableConfig3" v-model:reload="reload3" @edit="edit" @select="selectTc"></Table>
      </div>
    </div>
  </div>
</template>

<script setup name="plan">
import { ElMessage } from 'element-plus'
const { proxy } = getCurrentInstance()
import * as api from '@/api/index'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
// 组件
import Table from '@/components/common/Table.vue'
// 尺寸
import systemStore from '@/store/data.js'
const store = systemStore()
// 数据块
const contentHeight = store.mainHeight - 236
// 标题
const { setPath, pathData, getParam } = useComponent()

const paintTitle = ref(pathData.value.name)
// tab
const filter = reactive({
  g: [
    {
      title: '地点',
      select: true,
    },
    {
      title: '线路',
      select: false,
    },
    {
      title: '区域',
      select: false,
    },
  ],
  index: 0,
  type: 'POINT',
})
// 初始化
initDate()
function initDate() {
  if (getParam().data && getParam().data.tabActive !== '') {
    filter.index = getParam().data.tabActive
    filter.g.map(item => {
      item.select = false
    })
    filter.g[getParam().data.tabActive].select = true
  }
  if (getParam().data && getParam().data.type !== '') {
    filter.type = getParam().data.type
  }
}
function getFilterType(val) {
  let n = ''
  switch (filter.index) {
    case 0:
      n = 'POINT'
      break
    case 1:
      n = 'LINE'
      break
    case 2:
      n = 'PLANE'
      break
  }
  return n
}
const shiftTab = (o, i) => {
  filter.g.map(item => {
    return (item.select = false)
  })
  o.select = true
  filter.index = i
  filter.type = getFilterType(i)
}
/** 表格初始化 **/
const tableConfig1 = reactive({
  height: contentHeight - 115,
  select: true,
  pagination: true,
  api: 'mark_aggregate_list',
  timeSearch: [],
  operate: 1, // 编辑
  search: {
    nameLike: undefined,
    placeAddrLike: undefined,
    placeKind: 'POINT',
    status: 1,
    deleted: 0,
    endCreateTime: undefined,
    limit: 10,
    offset: 0,
  },
  column: [
    {
      prop: 'placeName',
      label: '地点',
      width: 200,
      template: true,
    },
    {
      prop: 'placeAddr',
      label: '地址',
      template: true,
    },
    {
      prop: 'airspaceType',
      label: '空域类型',
      template: true,
    },
    {
      prop: 'abnormalWidth',
      label: '异常区(米)',
      template: true,
    },
    {
      prop: 'bot',
      label: '底高(米)',
      template: true,
    },
    {
      prop: 'top',
      label: '顶高(米)',
      template: true,
    },
  ],
})
const reload1 = ref(0)
const tableConfig2 = reactive({
  height: contentHeight - 115,
  select: true,
  pagination: true,
  api: 'mark_aggregate_list',
  timeSearch: [],
  operate: 1, // 编辑
  search: {
    nameLike: undefined,
    placeAddrLike: undefined,
    placeKind: 'LINE',
    status: 1,
    deleted: 0,
    endCreateTime: undefined,
    limit: 10,
    offset: 0,
  },
  column: [
    {
      prop: 'placeName',
      label: '地点',
      width: 200,
      template: true,
    },
    {
      prop: 'placeAddr',
      label: '地址',
      template: true,
    },
    {
      prop: 'airspaceType',
      label: '空域类型',
      template: true,
    },
    {
      prop: 'abnormalWidth',
      label: '异常区(米)',
      template: true,
    },
    {
      prop: 'bot',
      label: '底高(米)',
      template: true,
    },
    {
      prop: 'top',
      label: '顶高(米)',
      template: true,
    },
  ],
})
const reload2 = ref(0)
const tableConfig3 = reactive({
  height: contentHeight - 115,
  select: true,
  pagination: true,
  api: 'mark_aggregate_list',
  timeSearch: [],
  operate: 1, // 编辑
  search: {
    nameLike: undefined,
    placeAddrLike: undefined,
    placeKind: 'PLANE',
    status: 1,
    deleted: 0,
    endCreateTime: undefined,
    limit: 10,
    offset: 0,
  },
  column: [
    {
      prop: 'placeName',
      label: '地点',
      width: 200,
      template: true,
    },
    {
      prop: 'placeAddr',
      label: '地址',
      template: true,
    },
    {
      prop: 'airspaceType',
      label: '空域类型',
      template: true,
    },
    {
      prop: 'abnormalWidth',
      label: '异常区(米)',
      template: true,
    },
    {
      prop: 'bot',
      label: '底高(米)',
      template: true,
    },
    {
      prop: 'top',
      label: '顶高(米)',
      template: true,
    },
  ],
})
const reload3 = ref(0)

/** 查询 **/
const search1 = () => {
  reload1.value = Math.random()
}
const reset1 = () => {
  tableConfig1.search.nameLike = undefined
  tableConfig1.search.placeAddrLike = undefined
}
const search2 = () => {
  reload2.value = Math.random()
}
const reset2 = () => {
  tableConfig2.search.nameLike = undefined
  tableConfig2.search.placeAddrLike = undefined
}
const search3 = () => {
  reload3.value = Math.random()
}
const reset3 = () => {
  tableConfig3.search.nameLike = undefined
  tableConfig3.search.placeAddrLike = undefined
}
/** 操作 **/
import useComponent, { PathType } from '@/hooks/useComponent.js'
// 新增
const add = () => {
  setPath('3-1-1', { type: filter.type, tabActive: filter.index }, PathType.add)
}
// 编辑
const edit = o => {
  setPath('3-1-1', { type: filter.type, id: o.id, tabActive: filter.index }, PathType.edit)
}
// 删除
let selectData1 = []
let selectData2 = []
let selectData3 = []
const selectTa = o => {
  selectData1 = []
  o.map(item => {
    selectData1.push(item.id)
  })
}
const selectTb = o => {
  selectData2 = []
  o.map(item => {
    selectData2.push(item.id)
  })
}
const selectTc = o => {
  selectData3 = []
  o.map(item => {
    selectData3.push(item.id)
  })
}
const del = () => {
  let selectData = ''
  let reload = ''
  if (filter.type === 'POINT') {
    selectData = selectData1
    reload = reload1
  } else if (filter.type === 'LINE') {
    selectData = selectData2
    reload = reload2
  } else if (filter.type === 'PLANE') {
    selectData = selectData3
    reload = reload3
  }
  if (selectData.length === 0) {
    ElMessage.error(t('message.selectError'))
  } else {
    api.mark_aggregate_delete({ idList: JSON.stringify(selectData) }).then(data => {
      if (data.code === '00000') {
        ElMessage({
          message: t('message.deleteSuccess'),
          type: 'success',
        })
        setTimeout(() => {
          reload.value = Math.random()
        }, 1500)
      } else {
        ElMessage.error(data.msg)
      }
    })
  }
}
</script>

<style scoped lang="scss"></style>
