<template>
  <div class="area-container">
    <CommonForm v-model="searchForm" :form-items="formItems" @query="handleSearch" @reset="handleReset">
      <!-- 区县选择插槽 -->
      <template #cityCodeList="{ formData }">
        <AreaSelect v-model="formData.cityCodeList" placeholder="请选择区县" />
      </template>

      <!-- 标签筛选插槽 -->
      <template #tagSelection>
        <TagSelect ref="tagSelectRef" v-model="tagSelection" />
      </template>
    </CommonForm>

    <CommonTable
      ref="tableRef"
      v-model:page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :total="total"
      :show-selection="true"
      :auto-height="true"
      :table-props="tableConfig"
      @pagination-change="handlePaginationChange"
      @action-click="handleActionClick"
      @selection-change="handleSelectionChange"
    >
      <template #toolbar-right="{ selectedRows: selected }">
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button type="danger" :disabled="selected.length === 0" @click="handleBatchOperation('delete')">
          删除{{ selected.length ? `(${selected.length})` : '' }}
        </el-button>
      </template>
    </CommonTable>
  </div>
</template>

<script setup name="area">
import { ref, reactive, onMounted, onActivated } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as api from '@/request/api/index'
import { areaTableColumns, tableConfig, defaultAreaSearchForm, areaFormItems } from './config.js'
import CommonTable from '@/components/common/CommonTable/CommonTable.vue'
import CommonForm from '@/components/common/CommonForm/CommonForm.vue'
import AreaSelect from '@/components/common/AreaSelect/AreaSelect.vue'
import TagSelect from '@/components/common/TagSelect/TagSelect.vue'
import useComponent, { PathType } from '@/hooks/useComponent.js'

const { setPath } = useComponent()

const placeType = 'PLANE'

// 表格数据
const tableData = ref([])
const columns = ref(areaTableColumns)
const loading = ref(false)
const total = ref(0)
// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
})
const selectedRows = ref([])
const tableRef = ref(null)

// 表单配置
const formItems = ref(areaFormItems)

// 搜索表单
const searchForm = reactive({
  ...defaultAreaSearchForm,
})

const tagSelectRef = ref(null)
// 标签选择
const tagSelection = ref({
  matchAllTagList: [],
  matchAnyTagList: [],
})
// 加载表格数据
const loadTableData = () => {
  loading.value = true

  const params = {
    ...searchForm,
    cityCodeList: JSON.stringify(searchForm.cityCodeList),
    matchAllTagList: JSON.stringify(tagSelection.value.matchAllTagList),
    matchAnyTagList: JSON.stringify(tagSelection.value.matchAnyTagList),
    limit: pagination.pageSize,
    offset: (pagination.page - 1) * pagination.pageSize,
  }

  api
    .obstacle_list(params)
    .then(res => {
      tableData.value = res.list || []
      total.value = res.total || 0
    })
    .catch(() => {
      tableData.value = []
      total.value = 0
    })
    .finally(() => {
      loading.value = false
    })
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, defaultAreaSearchForm)
  tagSelectRef.value?.clearSelection()
  handleSearch()
}

// 分页变化
const handlePaginationChange = () => {
  loadTableData()
}

// 选择变化
const handleSelectionChange = selection => {
  selectedRows.value = selection
}

// 操作按钮点击
const handleActionClick = async ({ action, row }) => {
  if (action.code === 'edit') {
    setPath(
      'mapManagement/detail/MarkDetail.vue',
      { type: placeType, id: row.id, source: 'obstacle' },
      { name: '编辑障碍物区域', type: PathType.edit },
    )
  } else if (action.code === 'enable') {
    await handleStatusChange(row, 1, '启用')
  } else if (action.code === 'disable') {
    await handleStatusChange(row, 2, '停用')
  }
}

// 状态变更
const handleStatusChange = async (row, status, statusText) => {
  try {
    await ElMessageBox.confirm(`确定要${statusText}该区域吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    loading.value = true
    await api.obstacle_update({
      obstacle_id: row.id,
      obstacle_status: status,
    })

    ElMessage.success(`${statusText}成功`)
    loadTableData()
  } finally {
    loading.value = false
  }
}

// 新增
const handleAdd = () => {
  setPath(
    'mapManagement/detail/MarkDetail.vue',
    { type: placeType, source: 'obstacle' },
    { name: '新增障碍物区域', type: PathType.add },
  )
}

// 批量操作
const handleBatchOperation = operation => {
  const idList = selectedRows.value.map(item => item.id)

  if (operation === 'delete') {
    ElMessageBox.confirm('确认删除选中的区域吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      api.obstacle_delete({ idList: JSON.stringify(idList) }).then(() => {
        ElMessage({
          message: '删除成功',
          type: 'success',
        })

        // 重新加载数据
        setTimeout(() => {
          loadTableData()
          tableRef.value?.clearSelection()
        }, 500)
      })
    })
  }
}

onActivated(() => {
  loadTableData()
})

// 组件挂载时加载数据
onMounted(() => {
  loadTableData()
})
</script>

<style scoped lang="less">
.area-container {
  width: 100%;
  height: 100%;
}
</style>
