import { mapStatusToText } from '@/utils/helper.js'
import useOrg from '@/hooks/useOrg.js'
const { orgId } = useOrg()

// 权限判断函数
const hasEditPermission = row => {
  // 如果是市级权限，可以编辑所有数据
  if (orgId.value === 'cb8upAzuM07uQyNpzuj05') {
    return true
  }
  // 区县级权限，只能编辑自己组织的数据
  return row.orgId === orgId.value
}

export const selectionControl = (row, index) => {
  return hasEditPermission(row)
}

const commonStatusColumn = {
  prop: 'status',
  label: '状态',
  minWidth: 100,
  type: 'tag',
  tagText: row => {
    if (row.status === 2) {
      return '停用'
    } else if (row.status === 1) {
      if (row.runningStatus === 'RUNNING') {
        return '在用'
      } else if (row.runningStatus === 'PLANNING') {
        return '规划中'
      }
    }
  },
  tagType: row => {
    if (row.status === 2) {
      return 'danger'
    } else if (row.status === 1) {
      if (row.runningStatus === 'RUNNING') {
        return 'success'
      } else if (row.runningStatus === 'PLANNING') {
        return 'warning'
      }
    }
  },
}

const commonActions = {
  fixed: 'right',
  label: '操作',
  type: 'action',
  width: 200,
  actions: [
    {
      label: '编辑',
      type: 'primary',
      size: 'small',
      code: 'edit',
      // visible: hasEditPermission,
    },
    {
      label: '恢复',
      type: 'success',
      size: 'small',
      code: 'restore',
      // visible: row => row.status === 2 && hasEditPermission(row),
      visible: row => row.status === 2,
    },
    {
      label: '停用',
      type: 'danger',
      size: 'small',
      code: 'deactivate',
      // visible: row => row.status === 1 && hasEditPermission(row),
      visible: row => row.status === 1,
    },
  ],
}

// Point 组件表格配置
export const pointTableColumns = [
  {
    prop: 'name',
    label: '地点',
    width: 200,
    minWidth: 120,
  },
  {
    prop: 'addr',
    label: '地址',
    minWidth: 150,
  },
  {
    prop: 'cityName',
    label: '所属地区',
    minWidth: 150,
  },
  {
    prop: 'categoryName',
    label: '类型',
    minWidth: 120,
  },
  commonStatusColumn,
  commonActions,
]

// Line 组件表格配置
export const lineTableColumns = [
  {
    prop: 'name',
    label: '线路名称',
    width: 200,
    minWidth: 120,
  },
  {
    prop: 'addr',
    label: '地址',
    minWidth: 150,
  },
  {
    prop: 'cityName',
    label: '所属地区',
    minWidth: 150,
  },
  {
    prop: 'categoryName',
    label: '类型',
    minWidth: 120,
  },
  commonStatusColumn,
  commonActions,
]

// Area 组件表格配置
export const areaTableColumns = [
  {
    prop: 'name',
    label: '区域名称',
    width: 200,
    minWidth: 120,
  },
  {
    prop: 'addr',
    label: '地址',
    minWidth: 150,
  },
  {
    prop: 'cityName',
    label: '所属地区',
    minWidth: 150,
  },
  {
    prop: 'categoryName',
    label: '类型',
    minWidth: 120,
  },
  commonStatusColumn,
  commonActions,
]

// 表格配置
export const tableConfig = {
  stripe: true,
  border: false,
  showHeader: true,
  highlightCurrentRow: true,
  emptyText: '暂无数据',
}

// Point 搜索表单默认值
export const defaultSearchForm = {
  nameLike: undefined,
  addrLike: undefined,
  status: undefined, // 改为可选择
  runningStatus: undefined, // 新增运行状态
  kind: 'POINT',
  deleted: 0,
}

// Point 表单配置项
export const pointFormItems = [
  {
    type: 'input',
    prop: 'nameLike',
    label: '地点名称',
    placeholder: '请输入地点名称模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'addrLike',
    label: '地址',
    placeholder: '请输入地址模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'cityCodeList',
    prop: 'cityCodeList',
    label: '所属地区',
    span: 6,
  },
  {
    type: 'slot',
    slotName: 'status',
    prop: 'statusInfo',
    label: '状态',
    placeholder: '请选择状态',
    span: 6,
  },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 6,
  },
]

// Line 搜索表单默认值
export const defaultLineSearchForm = {
  nameLike: undefined,
  addrLike: undefined,
  status: undefined,
  runningStatus: undefined,
  kind: 'LINE',
  deleted: 0,
}

// Line 表单配置项
export const lineFormItems = [
  {
    type: 'input',
    prop: 'nameLike',
    label: '线路名称',
    placeholder: '请输入线路名称模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'addrLike',
    label: '线路地址',
    placeholder: '请输入线路地址模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'cityCodeList',
    prop: 'cityCodeList',
    label: '所属地区',
    span: 6,
  },
  {
    type: 'slot',
    slotName: 'status',
    prop: 'statusInfo',
    label: '状态',
    placeholder: '请选择状态',
    span: 6,
  },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 6,
  },
]

// Area 搜索表单默认值
export const defaultAreaSearchForm = {
  nameLike: undefined,
  addrLike: undefined,
  status: undefined,
  runningStatus: undefined,
  kind: 'PLANE',
  deleted: 0,
}

// Area 表单配置项
export const areaFormItems = [
  {
    type: 'input',
    prop: 'nameLike',
    label: '区域名称',
    placeholder: '请输入区域名称模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'addrLike',
    label: '区域地址',
    placeholder: '请输入区域地址模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'cityCodeList',
    prop: 'cityCodeList',
    label: '所属地区',
    span: 6,
  },
  {
    type: 'slot',
    slotName: 'status',
    prop: 'statusInfo',
    label: '状态',
    placeholder: '请选择状态',
    span: 6,
  },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 6,
  },
]
