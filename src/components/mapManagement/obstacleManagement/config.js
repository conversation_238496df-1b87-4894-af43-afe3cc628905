import { mapStatusToText } from '@/utils/helper.js'
import useOrg from '@/hooks/useOrg.js'
const { orgId } = useOrg()

// 权限判断函数
const hasEditPermission = row => {
  // 如果是市级权限，可以编辑所有数据
  if (orgId.value === 'cb8upAzuM07uQyNpzuj05') {
    return true
  }
  // 区县级权限，只能编辑自己组织的数据
  return row.orgId === orgId.value
}

// Point 组件表格配置
export const pointTableColumns = [
  {
    prop: 'name',
    label: '地点',
    width: 200,
    minWidth: 120,
  },
  {
    prop: 'addr',
    label: '地址',
    minWidth: 150,
  },
  {
    prop: 'cityName',
    label: '所属区县',
    minWidth: 150,
  },
  {
    prop: 'airspaceType',
    label: '空域类型',
    minWidth: 120,
  },
  {
    prop: 'category',
    label: '类型',
    minWidth: 120,
  },
  {
    prop: 'bot',
    label: '底高(米)',
    minWidth: 100,
  },
  {
    prop: 'top',
    label: '顶高(米)',
    minWidth: 100,
  },
  {
    prop: 'status',
    label: '状态',
    minWidth: 80,
    type: 'tag',
    tagText: row => mapStatusToText(row.status, 'poi_status', '-'),
    tagTypeMap: { 1: 'success', 2: 'danger' },
  },
  {
    prop: 'runningStatus',
    label: '运行状态',
    minWidth: 100,
    type: 'tag',
    tagText: row => mapStatusToText(row.runningStatus, 'poi_runningStatus', '-'),
    tagTypeMap: { RUNNING: 'success', PLANNING: 'warning' },
  },
  {
    fixed: 'right',
    label: '操作',
    type: 'action',
    width: 200,
    actions: [
      {
        label: '编辑',
        type: 'primary',
        size: 'small',
        code: 'edit',
        visible: hasEditPermission,
      },
      {
        label: '启用',
        type: 'success',
        size: 'small',
        code: 'enable',
        visible: row => row.status === 2 && hasEditPermission(row),
      },
      {
        label: '停用',
        type: 'danger',
        size: 'small',
        code: 'disable',
        visible: row => row.status === 1 && hasEditPermission(row),
      },
    ],
  },
]

// Line 组件表格配置
export const lineTableColumns = [
  {
    prop: 'name',
    label: '线路名称',
    width: 200,
    minWidth: 120,
  },
  {
    prop: 'addr',
    label: '地址',
    minWidth: 150,
  },
  {
    prop: 'cityName',
    label: '所属区县',
    minWidth: 150,
  },
  {
    prop: 'airspaceType',
    label: '空域类型',
    minWidth: 120,
  },
  {
    prop: 'category',
    label: '类型',
    minWidth: 120,
  },
  {
    prop: 'bot',
    label: '底高(米)',
    minWidth: 100,
  },
  {
    prop: 'top',
    label: '顶高(米)',
    minWidth: 100,
  },
  {
    prop: 'status',
    label: '状态',
    minWidth: 80,
    type: 'tag',
    tagText: row => mapStatusToText(row.status, 'poi_status', '-'),
    tagTypeMap: { 1: 'success', 2: 'danger' },
  },
  {
    prop: 'runningStatus',
    label: '运行状态',
    minWidth: 100,
    type: 'tag',
    tagText: row => mapStatusToText(row.runningStatus, 'poi_runningStatus', '-'),
    tagTypeMap: { RUNNING: 'success', PLANNING: 'warning' },
  },
  {
    fixed: 'right',
    label: '操作',
    type: 'action',
    width: 200,
    actions: [
      {
        label: '编辑',
        type: 'primary',
        size: 'small',
        code: 'edit',
        visible: hasEditPermission,
      },
      {
        label: '启用',
        type: 'success',
        size: 'small',
        code: 'enable',
        visible: row => row.status === 2 && hasEditPermission(row),
      },
      {
        label: '停用',
        type: 'danger',
        size: 'small',
        code: 'disable',
        visible: row => row.status === 1 && hasEditPermission(row),
      },
    ],
  },
]

// Area 组件表格配置
export const areaTableColumns = [
  {
    prop: 'name',
    label: '区域名称',
    width: 200,
    minWidth: 120,
  },
  {
    prop: 'addr',
    label: '地址',
    minWidth: 150,
  },
  {
    prop: 'cityName',
    label: '所属区县',
    minWidth: 150,
  },
  {
    prop: 'airspaceType',
    label: '空域类型',
    minWidth: 120,
  },
  {
    prop: 'category',
    label: '类型',
    minWidth: 120,
  },
  {
    prop: 'bot',
    label: '底高(米)',
    minWidth: 100,
  },
  {
    prop: 'top',
    label: '顶高(米)',
    minWidth: 100,
  },
  {
    prop: 'status',
    label: '状态',
    minWidth: 80,
    type: 'tag',
    tagText: row => mapStatusToText(row.status, 'poi_status', '-'),
    tagTypeMap: { 1: 'success', 2: 'danger' },
  },
  {
    prop: 'runningStatus',
    label: '运行状态',
    minWidth: 100,
    type: 'tag',
    tagText: row => mapStatusToText(row.runningStatus, 'poi_runningStatus', '-'),
    tagTypeMap: { RUNNING: 'success', PLANNING: 'warning' },
  },
  {
    fixed: 'right',
    label: '操作',
    type: 'action',
    width: 200,
    actions: [
      {
        label: '编辑',
        type: 'primary',
        size: 'small',
        code: 'edit',
        visible: hasEditPermission,
      },
      {
        label: '启用',
        type: 'success',
        size: 'small',
        code: 'enable',
        visible: row => row.status === 2 && hasEditPermission(row),
      },
      {
        label: '停用',
        type: 'danger',
        size: 'small',
        code: 'disable',
        visible: row => row.status === 1 && hasEditPermission(row),
      },
    ],
  },
]

// 表格配置
export const tableConfig = {
  stripe: true,
  border: false,
  showHeader: true,
  highlightCurrentRow: true,
  emptyText: '暂无数据',
}

// Point 搜索表单默认值
export const defaultSearchForm = {
  nameLike: undefined,
  addrLike: undefined,
  cityCodeList: [],
  status: undefined, // 改为可选择
  runningStatus: undefined, // 新增运行状态
  kind: 'POINT',
  deleted: 0,
}

// 状态选项配置
export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '停用', value: 2 },
]

export const runningStatusOptions = [
  { label: '运行中', value: 'RUNNING' },
  { label: '规划中', value: 'PLANNING' },
]

// Point 表单配置项
export const pointFormItems = [
  {
    type: 'input',
    prop: 'nameLike',
    label: '地点名称',
    placeholder: '请输入地点名称模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'addrLike',
    label: '地址',
    placeholder: '请输入地址模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'cityCodeList',
    prop: 'cityCodeList',
    label: '所属区县',
    span: 6,
  },

  {
    type: 'select',
    prop: 'status',
    label: '状态',
    placeholder: '请选择状态',
    span: 6,
    options: statusOptions,
    props: {
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'runningStatus',
    label: '运行状态',
    placeholder: '请选择运行状态',
    span: 6,
    options: runningStatusOptions,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 6,
  },
]

// Line 搜索表单默认值
export const defaultLineSearchForm = {
  nameLike: undefined,
  addrLike: undefined,
  cityCodeList: [],
  status: undefined,
  runningStatus: undefined,
  kind: 'LINE',
  deleted: 0,
}

// Line 表单配置项
export const lineFormItems = [
  {
    type: 'input',
    prop: 'nameLike',
    label: '线路名称',
    placeholder: '请输入线路名称模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'addrLike',
    label: '线路地址',
    placeholder: '请输入线路地址模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'cityCodeList',
    prop: 'cityCodeList',
    label: '所属区县',
    span: 6,
  },
  {
    type: 'select',
    prop: 'status',
    label: '状态',
    placeholder: '请选择状态',
    span: 6,
    options: statusOptions,
    props: {
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'runningStatus',
    label: '运行状态',
    placeholder: '请选择运行状态',
    span: 6,
    options: runningStatusOptions,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 6,
  },
]

// Area 搜索表单默认值
export const defaultAreaSearchForm = {
  nameLike: undefined,
  addrLike: undefined,
  cityCodeList: [],
  status: undefined,
  runningStatus: undefined,
  kind: 'PLANE',
  deleted: 0,
}

// Area 表单配置项
export const areaFormItems = [
  {
    type: 'input',
    prop: 'nameLike',
    label: '区域名称',
    placeholder: '请输入区域名称模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'addrLike',
    label: '区域地址',
    placeholder: '请输入区域地址模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'cityCodeList',
    prop: 'cityCodeList',
    label: '所属区县',
    span: 6,
  },
  {
    type: 'select',
    prop: 'status',
    label: '状态',
    placeholder: '请选择状态',
    span: 6,
    options: statusOptions,
    props: {
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'runningStatus',
    label: '运行状态',
    placeholder: '请选择运行状态',
    span: 6,
    options: runningStatusOptions,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 6,
  },
]
