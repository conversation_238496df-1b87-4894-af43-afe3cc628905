<template>
  <div class="container" :style="{ height: store.mainHeight + 'px' }">
    <div class="navigation">
      <div class="note">
        <div class="title">{{ paintTitle }}</div>
      </div>
      <div class="opearte"></div>
    </div>
    <div class="content" :style="{ height: contentHeight + 'px' }">
      <div class="card">
        <div ref="pieceCom" class="piece">
          <div class="mapContainer" :style="{ width: mapWidth + 'px', height: mapHeight + 'px' }">
            <DesignMap v-model:map-data="mapData"></DesignMap>
          </div>
          <el-tabs v-model="space.active" class="demo-tabs" :lazy="false" @tab-click="spaceHeightTabShift">
            <el-tab-pane v-for="(item, index) in space.g" :lazy="false" :label="item.label" :name="item.index">
              <el-row>
                <el-col :span="18"> </el-col>
                <el-col :span="6">
                  <div class="paint">
                    <div class="title">
                      <div class="tab"></div>
                      <div class="operate" @click="add">+新增</div>
                    </div>
                    <div v-for="(itemp, indexp) in item.detail" :key="indexp" class="list">
                      <div v-show="item.active === indexp" class="scroll" :style="{ height: mapHeight - 40 + 'px' }">
                        <div
                          v-for="(itemc, indexc) in itemp.g"
                          :key="indexc"
                          class="line"
                          :class="[itemc.select ? 'select' : '']"
                          @click="airspaceCheck(itemc, indexc)"
                        >
                          <el-row style="width: 100%">
                            <el-col :span="22">
                              <el-row class="note">
                                <el-col :span="3">
                                  <img :src="getImg('areaIcon.png')" alt="" />
                                </el-col>
                                <el-col :span="21">
                                  <div class="wrap">
                                    <div class="title">
                                      <div class="name">{{ itemc.airspaceName }}</div>
                                      <!--                                        <div class="mark">{{ itemc.ruleList[0].bufferWidth }}米</div>-->
                                    </div>
                                    <div class="remarks">{{ itemc.memo }}</div>
                                  </div>
                                </el-col>
                              </el-row>
                            </el-col>
                            <el-col :span="2">
                              <div class="operate">
                                <el-dropdown>
                                  <span class="el-dropdown-link" style="font-size: 20px"
                                    ><i class="ri-more-fill"></i
                                  ></span>
                                  <template #dropdown>
                                    <el-dropdown-menu>
                                      <el-dropdown-item @click="edit(itemc)">编辑</el-dropdown-item>
                                      <el-dropdown-item @click="del(itemc)">删除</el-dropdown-item>
                                    </el-dropdown-menu>
                                  </template>
                                </el-dropdown>
                              </div>
                            </el-col>
                          </el-row>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import * as api from '@/api/index'
import * as common from '@/utils/common.js'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import { getImg } from '@/utils/getAsset.js'
// 组件
import DesignMap from '@/components/map/DesignMap.vue'
// 尺寸
import systemStore from '@/store/data.js'
const store = systemStore()
const mapHeight = store.mainHeight - 206
// 数据块
const contentHeight = store.mainHeight - 58
// 跳转
import useComponent, { PathType } from '@/hooks/useComponent.js'
const { setPath, pathData, getParam } = useComponent()
// 标题
const paintTitle = ref(pathData.value.name)
const pieceCom = ref(null)
const mapWidth = ref(0)
// 地图数据
const mapData = reactive({ ids: [] })
/** 地图组件初始化 **/
onMounted(() => {
  mapWidth.value = (pieceCom.value.clientWidth - 48) * 0.75 - 12
})

/** 空域查询 **/
// 查询
const space = reactive({
  g: [],
  active: 0,
})

// 空域高度切换
const spaceHeightTabShift = o => {
  space.active = o.index
  // 如果有选择的空域，就传输
  const selectSpace = space.g[space.active].detail[space.g[space.active].active].g.filter(item => item.select)
  mapData.ids = selectSpace.length > 0 ? [selectSpace[0].airspaceModelId] : []
}
getflightLevelList()

// 高度层查询
function getflightLevelList() {
  let p = {
    offset: 0,
    limit: 10,
  }
  api.flight_level_list(p).then(data => {
    if (data.code === '00000') {
      let d = data.data.list
      let o = common.groupBy(d, 'id')
      space.g = []
      o.map((itemp, index) => {
        space.g.push({
          id: itemp[0].id,
          label: itemp[0].flightLevelName,
          bot: itemp[0].bot,
          top: itemp[0].top,
          key: itemp[0].flightLevelNo,
          index: index,
          active: 0,
          select: false,
          detail: [{ g: [] }],
        })
        getAggregateList(itemp[0].id, index)
      })
      if (getParam().data && getParam().data.address.length > 0) {
        space.active = getParam().data.address[0]
        space.g[getParam().data.address[0]].select = true
      } else {
        space.g[0].select = true
      }
    }
  })
}

// 聚合查询
function getAggregateList(flightLevelId, index) {
  let p = {
    flightLevelId: flightLevelId,
    source: 'COMMON',
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 200,
  }
  api.airspace_aggregate_list(p).then(data => {
    if (data.code === '00000') {
      let d = data.data.list
      d.map(itemp => {
        if (itemp.ruleList && itemp.ruleList.length > 0 && itemp.ruleList[0]) {
          itemp.select = false
          space.g[index].detail[0].g.push(itemp)
        }
      })
      // 如果遍历的是上一页传输过来的高度层
      if (getParam().data && getParam().data.address.length > 0) {
        if (space.active === index) {
          space.g[space.active].active = getParam().data.address[1]
          space.g[space.active].detail.map(item => {
            item.select = false
          })
          space.g[space.active].detail[getParam().data.address[1]].select = true
        }
      }
    }
    console.log('--------------')
  })
}

/** 空域编辑 **/
// 新增
const add = () =>
  setPath(
    '3-2-1',
    {
      address: [space.active, space.g[space.active].active],
      top: space.g[space.active].top,
      bot: space.g[space.active].bot,
      heightId: space.g[space.active].id,
    },
    PathType.add,
  )
// 编辑
const edit = w => {
  setPath(
    '3-2-1',
    {
      id: w.id,
      top: space.g[space.active].top,
      bot: space.g[space.active].bot,
      address: [space.active, space.g[space.active].active],
    },
    PathType.edit,
  )
}
// 选择
const airspaceCheck = (item, index) => {
  mapData.ids = []
  space.g[space.active].detail[space.g[space.active].active].g.map(itemp => {
    itemp.select = false
  })
  item.select = true
  console.log(item)
  mapData.ids = [item.airspaceModelId]
}

// 删除
const del = o => {
  console.log(o)
  ElMessageBox.confirm('是否确定删除当前空域', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      let idList = JSON.stringify([o.id])
      api.airspace_aggregate_delete({ idList: idList }).then(data => {
        if (data.code === '00000') {
          ElMessage({
            message: t('message.deleteSuccess'),
            type: 'success',
          })
          setTimeout(() => {
            getflightLevelList()
          }, common.globalTime())
        } else {
          ElMessage.error(data.msg)
        }
      })
      console.log(o)
    })
    .catch(() => {})
}
</script>

<style scoped lang="scss">
.container {
  .piece {
    position: relative;
    padding: 24px;
    .paint {
      .title {
        padding: 0 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        .tab {
          display: flex;
          align-items: center;
          height: 40px;
          .name {
            height: 40px;
            width: 95px;
            text-align: center;
            line-height: 40px;
            color: var(--main-font-color6);
            font-size: 16px;
            cursor: pointer;
          }
          .name.select {
            background: var(--main-color);
            color: #fff;
            border-radius: 5px;
            box-shadow: var(--main-box-shadown);
          }
        }
        .operate {
          color: var(--main-color);
          font-size: 16px;
          position: relative;
          padding-left: 12px;
          cursor: pointer;
        }
      }
    }
    .mapContainer {
      position: absolute;
      top: 78px;
      box-sizing: border-box;
    }
    .list {
      .scroll {
        overflow: scroll;
      }
      .line {
        margin: 12px 0;
        height: 70px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .note {
          padding-left: 12px;
          display: flex;
          align-items: center;
          height: 60px;
          img {
            width: 48px;
            height: 48px;
          }
          .title {
            padding: 0px;
            width: 100%;
            display: flex;
            height: 30px;
            align-items: center;
            .name {
              width: 70%;
              height: 30px;
              line-height: 30px;
              text-align: left;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-size: 18px;
            }
            .mark {
              font-size: 14px;
              background: rgba(0, 176, 70, 0.1);
              border-radius: 6px;
              padding: 2px 8px;
              color: #00b046;
            }
          }
          .remarks {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.4);
          }
        }
        .operate {
          height: 60px;
          display: flex;
          align-items: center;
          padding-right: 12px;
          color: rgb(247, 113, 47);
          cursor: pointer;
        }
      }
      .line.select {
        border-radius: 12px;
        background: rgba(247, 113, 47, 0.12);
      }
    }
  }
}
</style>
