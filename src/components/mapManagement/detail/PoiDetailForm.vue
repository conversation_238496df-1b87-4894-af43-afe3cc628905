<template>
  <div class="poi-detail-form">
    <!-- POI基本信息 -->
    <el-form-item :label="`POI名称`" prop="poi_name">
      <el-input v-model="formData.poi_name" placeholder="请输入POI名称" @input="handleInput" />
    </el-form-item>

    <el-form-item :label="`POI地址`" prop="poi_addr">
      <el-input v-model="formData.poi_addr" placeholder="请输入POI地址" @input="handleInput" />
    </el-form-item>

    <el-form-item label="类型编码" prop="poi_typeCode">
      <el-input v-model="formData.poi_typeCode" placeholder="请输入类型编码" @input="handleInput" />
    </el-form-item>

    <el-form-item label="类型名称" prop="poi_typeName">
      <el-input v-model="formData.poi_typeName" placeholder="请输入类型名称" @input="handleInput" />
    </el-form-item>

    <el-form-item label="POI标签" prop="poi_tags">
      <!-- <TagSelect
        v-model="formData.poi_tags"
        :show-match-mode="false"
        :use-json-format="true"
        @update:model-value="handleInput"
      /> -->
      <TypeTags v-model="formData.poi_tags" @update:model-value="handleInput" />
    </el-form-item>

    <template v-if="showSharedFields">
      <el-form-item label="运行状态" prop="poi_runningStatus">
        <el-select
          v-model="formData.poi_runningStatus"
          placeholder="请选择运行状态"
          style="width: 100%"
          @change="handleInput"
        >
          <el-option label="运行中" value="RUNNING" />
          <el-option label="规划中" value="PLANNING" />
        </el-select>
      </el-form-item>

      <el-form-item label="样式配置" prop="poi_displayStyle">
        <StyleConfigEditor v-model="formData.poi_displayStyle" />
      </el-form-item>

      <el-form-item label="备注" prop="poi_remark">
        <el-input
          v-model="formData.poi_remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          @input="handleInput"
        />
      </el-form-item>
    </template>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
// import TagSelect from '@/components/common/tagSelect/TagSelect.vue'
import StyleConfigEditor from '@/components/common/styleConfigEditor/StyleConfigEditor.vue'
import TypeTags from '@/components/common/typeTags/TypeTags.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
  isAssociation: {
    type: Boolean,
    default: false,
  },
  showSharedFields: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue', 'validate'])

const formData = reactive({
  poi_id: '',
  poi_name: '',
  poi_addr: '',
  poi_typeCode: '',
  poi_typeName: '',
  // poi_layerType: '',
  poi_status: 1,
  poi_runningStatus: '',
  poi_tags: '[]',
  poi_displayStyle: JSON.stringify({ color: '#f7712f' }),
  poi_latitude: 0,
  poi_longitude: 0,
  poi_altitude: 0,
  poi_remark: '',
})

watch(
  () => props.modelValue,
  newValue => {
    if (newValue) {
      Object.keys(formData).forEach(key => {
        if (Object.prototype.hasOwnProperty.call(newValue, key)) {
          formData[key] = newValue[key]
        }
      })
    }
  },
  { immediate: true, deep: true },
)

watch(
  formData,
  newValue => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true },
)

const handleInput = () => {
  emit('validate')
}
</script>

<style scoped></style>
