<template>
  <div class="obstacle-detail-form">
    <!-- 障碍物基本信息 -->
    <el-form-item :label="`${title}名称`" prop="obstacle_name">
      <el-input v-model="formData.obstacle_name" placeholder="请输入障碍物名称" @input="handleInput" />
    </el-form-item>

    <el-form-item :label="`${title}地址`" prop="obstacle_addr">
      <el-input v-model="formData.obstacle_addr" placeholder="请输入障碍物地址" @input="handleInput" />
    </el-form-item>

    <!-- 高度范围 -->
    <el-form-item label="高度范围">
      <AirSpaceType
        v-model:if-airport="ifAirport"
        v-model:bot="formData.obstacle_bot"
        v-model:top="formData.obstacle_top"
        v-model:type="formData.obstacle_airspaceType"
      />
    </el-form-item>

    <el-form-item label="障碍物类型" prop="obstacle_category">
      <el-select
        v-model="formData.obstacle_category"
        placeholder="请输入障碍物类型"
        style="width: 100%"
        @input="handleInput"
      >
        <el-option v-for="item in obstacleCategoryOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>

    <el-form-item label="障碍物标签" prop="obstacle_tags">
      <TypeTags v-model="formData.obstacle_tags" dic-type="障碍物标签" @update:model-value="handleInput" />
    </el-form-item>

    <RegionSelect v-model="formData.obstacle_regionInfo" prop-prefix="obstacle_regionInfo" :show-region-type="false" />

    <template v-if="showSharedFields">
      <el-form-item label="状态" prop="obstacle_statusInfo">
        <StatusSelect v-model="formData.obstacle_statusInfo" />
      </el-form-item>
      <el-form-item label="样式配置" prop="obstacle_displayStyle">
        <StyleConfigEditor v-model="formData.obstacle_displayStyle" />
      </el-form-item>
      <el-form-item v-if="editType === 'edit'" label="规则" prop="obstacle_rule_idList">
        <el-select
          v-model="formData.obstacle_rule_idList"
          placeholder="请选择"
          style="width: 100%"
          multiple
          collapse-tags
          collapse-tags-tooltip
          clearable
        >
          <el-option
            v-for="item in ruleList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            :disabled="item.value === defaultRuleId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="obstacle_remark">
        <el-input
          v-model="formData.obstacle_remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          @input="handleInput"
        />
      </el-form-item>
    </template>
  </div>
</template>

<script setup>
import { reactive, watch, ref } from 'vue'
import TypeTags from '@/components/common/typeTags/TypeTags.vue'
import AirSpaceType from '@/components/common/AirSpaceType.vue'
import StyleConfigEditor from '@/components/common/styleConfigEditor/StyleConfigEditor.vue'
import RegionSelect from '@/components/common/regionSelect/RegionSelect.vue'
import { useDictionary } from '@/hooks/useDictionary.js'
import { useRule } from '@/hooks/useRule.js'
import StatusSelect from '@/components/common/statusSelect/StatusSelect.vue'

const obstacleCategoryOptions = useDictionary('障碍物类型')
const { ruleList } = useRule()

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
  title: {
    type: String,
    default: '障碍物',
  },
  editType: {
    type: String,
    default: 'add',
  },
  defaultRuleId: {
    type: String,
    default: null,
  },
  showSharedFields: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue', 'validate'])

// 机场标识
const ifAirport = ref(false)

// 表单数据
const formData = reactive({
  obstacle_id: '',
  obstacle_name: '',
  obstacle_addr: '',
  obstacle_kind: '',
  obstacle_airspaceType: '',
  obstacle_category: '',
  obstacle_tags: '[]',
  obstacle_geometries: '[]',
  obstacle_geoJson: '[]',
  obstacle_bot: 0,
  obstacle_top: 0,
  obstacle_displayStyle: JSON.stringify({ color: '#f7712f' }),
  obstacle_statusInfo: { status: 1, runningStatus: 'RUNNING' },
  obstacle_remark: '',
  obstacle_rule_idList: [],
  obstacle_regionInfo: { regionCodes: '', regionType: '' },
})

watch(
  () => props.modelValue,
  newValue => {
    if (newValue) {
      Object.keys(formData).forEach(key => {
        if (Object.prototype.hasOwnProperty.call(newValue, key)) {
          formData[key] = newValue[key]
        }
      })
    }
  },
  { immediate: true, deep: true },
)

watch(
  formData,
  newValue => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true },
)

const handleInput = () => {
  emit('validate')
}
</script>

<style scoped></style>
