<template>
  <div class="obstacle-detail-form">
    <!-- 障碍物基本信息 -->
    <el-form-item :label="`${title}名称`" prop="obstacle_name">
      <el-input v-model="formData.obstacle_name" placeholder="请输入障碍物名称" @input="handleInput" />
    </el-form-item>

    <el-form-item :label="`${title}地址`" prop="obstacle_addr">
      <el-input v-model="formData.obstacle_addr" placeholder="请输入障碍物地址" @input="handleInput" />
    </el-form-item>

    <!-- 高度范围 -->
    <el-form-item label="高度范围">
      <AirSpaceType
        v-model:if-airport="ifAirport"
        v-model:bot="formData.obstacle_bot"
        v-model:top="formData.obstacle_top"
        v-model:type="formData.obstacle_airspaceType"
      />
    </el-form-item>

    <el-form-item label="保护区(米)" prop="obstacle_abnormalWidth">
      <el-input
        v-model.number="formData.obstacle_abnormalWidth"
        type="number"
        placeholder="请输入保护区"
        @input="handleInput"
      />
    </el-form-item>

    <el-form-item label="障碍物类型" prop="obstacle_category">
      <el-input v-model="formData.obstacle_category" placeholder="请输入障碍物类型" @input="handleInput" />
    </el-form-item>

    <el-form-item label="障碍物标签" prop="obstacle_tags">
      <!-- <TagSelect
        v-model="formData.obstacle_tags"
        :show-match-mode="false"
        :use-json-format="true"
        @update:model-value="handleInput"
      /> -->
      <TypeTags v-model="formData.obstacle_tags" :options="defaultTags" @update:model-value="handleInput" />
    </el-form-item>

    <template v-if="showSharedFields">
      <el-form-item label="运行状态" prop="obstacle_runningStatus">
        <el-select
          v-model="formData.obstacle_runningStatus"
          placeholder="请选择运行状态"
          style="width: 100%"
          @change="handleInput"
        >
          <el-option label="运行中" value="RUNNING" />
          <el-option label="规划中" value="PLANNING" />
        </el-select>
      </el-form-item>
      <el-form-item label="样式配置" prop="obstacle_displayStyle">
        <StyleConfigEditor v-model="formData.obstacle_displayStyle" />
      </el-form-item>

      <el-form-item label="备注" prop="obstacle_remark">
        <el-input
          v-model="formData.obstacle_remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
          @input="handleInput"
        />
      </el-form-item>
    </template>
  </div>
</template>

<script setup>
import { reactive, watch, ref } from 'vue'
// import TagSelect from '@/components/common/tagSelect/TagSelect.vue'
import TypeTags from '@/components/common/typeTags/TypeTags.vue'
import AirSpaceType from '@/components/common/AirSpaceType.vue'
import StyleConfigEditor from '@/components/common/styleConfigEditor/StyleConfigEditor.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
  title: {
    type: String,
    default: '障碍物',
  },
  isAssociation: {
    type: Boolean,
    default: false,
  },
  showSharedFields: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue', 'validate'])

// 机场标识
const ifAirport = ref(false)

// 表单数据
const formData = reactive({
  obstacle_id: '',
  obstacle_name: '',
  obstacle_addr: '',
  obstacle_kind: '',
  obstacle_airspaceType: '',
  obstacle_category: '',
  obstacle_tags: '[]',
  obstacle_geometries: '',
  obstacle_geoJson: '',
  obstacle_abnormalWidth: 0,
  obstacle_bot: 0,
  obstacle_top: 0,
  obstacle_displayStyle: JSON.stringify({ color: '#f7712f' }),
  obstacle_runningStatus: '',
  obstacle_status: 1,
  obstacle_remark: '',
})
const defaultTags = [
  { value: 'hospital', label: '医院' },
  { value: 'school', label: '学校' },
  { value: 'park', label: '公园' },
  { value: 'shopping', label: '商场' },
  { value: 'restaurant', label: '餐厅' },
]

watch(
  () => props.modelValue,
  newValue => {
    if (newValue) {
      Object.keys(formData).forEach(key => {
        if (Object.prototype.hasOwnProperty.call(newValue, key)) {
          formData[key] = newValue[key]
        }
      })
    }
  },
  { immediate: true, deep: true },
)

watch(
  formData,
  newValue => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true },
)

const handleInput = () => {
  emit('validate')
}
</script>

<style scoped></style>
