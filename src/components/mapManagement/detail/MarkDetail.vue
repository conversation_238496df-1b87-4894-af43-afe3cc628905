<template>
  <div v-loading="formContent.loading" class="detail">
    <!-- 标题-->
    <CommonTitle is-back>
      <template #default>
        <div class="opearte">
          <el-button type="primary" @click="submit">提交</el-button>
        </div>
      </template>
    </CommonTitle>
    <el-row class="card">
      <el-col :span="18" class="flex-col">
        <EditMap v-model:map-data="mapData" :type="mapType" />
      </el-col>
      <el-col :span="6" class="flex-col">
        <el-tabs v-model="markEditTab" class="form-content">
          <el-tab-pane label="基本信息" name="1">
            <div class="scroll">
              <el-form ref="formRef" :model="formContent.param" :rules="formRules" label-position="top">
                <!-- 主要表单组件 -->
                <PoiDetailForm v-if="isPoi" v-model="poiFormData" :title="formContent.title" />
                <ObstacleDetailForm
                  v-else
                  v-model="obstacleFormData"
                  :title="formContent.title"
                  :edit-type="formContent.editType"
                  :default-rule-id="defaultRuleId"
                />

                <!-- 关联开关 -->
                <el-form-item v-if="isPoi && formContent.editType === 'add'" :label="`关联${associationType}设置`">
                  <el-switch
                    v-model="showAssociation"
                    :active-text="`显示关联${associationType}`"
                    :inactive-text="`隐藏关联${associationType}`"
                  />
                </el-form-item>

                <!-- 关联字段 - 使用子组件处理 -->
                <template v-if="showAssociation">
                  <el-divider v-if="isPoi && formContent.editType === 'add'" content-position="left">{{
                    labels.associationTitle
                  }}</el-divider>

                  <!-- 关联障碍物组件 - 当从POI来源时显示 -->
                  <ObstacleDetailForm
                    v-if="isPoi && formContent.editType === 'add'"
                    v-model="obstacleFormData"
                    title="关联障碍物"
                    :show-shared-fields="false"
                    :edit-type="formContent.editType"
                    :default-rule-id="defaultRuleId"
                  />
                </template>

                <el-form-item :label="`模型信息-${formContent.title}`">
                  <ModelDetails ref="modelDetailsRef" :model-value="mapData" @update:model-value="onMapDataUpdate" />
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
          <el-tab-pane label="GeoJson" disabled name="2">
            <div>
              <el-input v-model="exportIn.json" type="textarea" :rows="26" />
            </div>
            <div class="subIn">
              <el-button type="primary" style="margin-top: 20px" @click="subIn">导入</el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="markDetail">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as api from '@/request/api/index'
import CommonTitle from '@/components/common/commonTitle/CommonTitle.vue'
import PoiDetailForm from './PoiDetailForm.vue'
import ObstacleDetailForm from './ObstacleDetailForm.vue'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import { isJSON } from '@/utils/helper.js'
import ModelDetails from '@/components/common/modelDetails/ModelDetails.vue'
import EditMap from '@/components/map/EditMap.vue'
import useOrg from '@/hooks/useOrg.js'
import { useRule } from '@/hooks/useRule.js'

const { orgId } = useOrg()

const { size } = defineProps(['size'])
const formHeight = computed(() => size.height - 132 + 'px')
const { setPath, getParam } = useComponent()
const mapData = reactive({
  displayStyle: {},
  geometries: [],
  extra: {},
  geoJson: [],
  id: '',
})

const { ruleList } = useRule()
const defaultRuleId = ref('O8AZWcBfVfcfSD9M4kS9x') // 默认规则ID

watch(
  mapData,
  () => {
    console.log('mapData', mapData)
  },
  { deep: true },
)

const mapType = ref(getParam().data.type)
const markEditTab = ref('1')

// 判断调用来源和关联显示控制
const sourceInfo = computed(() => {
  const param = getParam()
  const source = param?.data?.source
  if (source === 'poi') {
    return { type: 'poi', name: 'POI点' }
  } else if (source === 'obstacle') {
    return { type: 'obstacle', name: '障碍物' }
  }
  // 默认根据数据类型判断
  return { type: 'poi', name: 'POI点' }
})

// 关联显示开关
const showAssociation = ref(false)

const isPoi = computed(() => sourceInfo.value.type === 'poi')
const associationType = computed(() => (isPoi.value ? '障碍物' : 'POI点'))

// 计算属性：动态标签文本
const labels = computed(() => ({
  associationTitle: `关联${associationType.value}信息`,
}))

// 监听关联开关变化，隐藏时清空关联数据
watch(showAssociation, newValue => {
  if (!newValue) {
    // 当关联开关关闭时，清空关联相关数据
    if (isPoi.value) {
      // 清空障碍物相关数据
      clearObstacleData()
    }
  }
})

// 表单验证规则 - 从子组件获取验证规则
const formRules = computed(() => {
  let rules = {}

  // 获取主要组件的验证规则
  if (isPoi.value) {
    // POI主要字段验证规则
    rules = {
      'poi_name': [{ required: true, message: '请输入POI名称', trigger: 'blur' }],
      'poi_statusInfo': [{ required: true, message: '请选择状态', trigger: 'change' }],
      'poi_regionInfo.regionCodes': [{ required: true, message: '请选择所属地区', trigger: 'change' }],
    }
    if (showAssociation.value) {
      rules['obstacle_name'] = [{ required: true, message: '请输入障碍物名称', trigger: 'blur' }]
      rules['obstacle_regionInfo.regionCodes'] = [{ required: true, message: '请选择所属地区', trigger: 'change' }]
    }
  } else {
    // 障碍物主要字段验证规则
    rules = {
      'obstacle_name': [{ required: true, message: '请输入障碍物名称', trigger: 'blur' }],
      'obstacle_statusInfo': [{ required: true, message: '请选择状态', trigger: 'change' }],
      'obstacle_regionInfo.regionCodes': [{ required: true, message: '请选择所属地区', trigger: 'change' }],
    }
  }
  return rules
})

// 标题映射
function initTitle(type) {
  const titleMap = {
    POINT: '地点',
    LINE: '线路',
    PLANE: '区域',
  }
  return titleMap[type] || ''
}

/** 表单内容 **/
const formContent = reactive({
  param: {
    // POI相关字段
    poi_id: '',
    poi_orgId: orgId,
    poi_name: '',
    poi_addr: '',
    poi_typeCode: '',
    poi_typeName: '',
    poi_displayStyle: JSON.stringify({ color: '#f7712f' }),
    poi_statusInfo: { status: 1, runningStatus: 'RUNNING' },
    poi_tags: '[]', // POI标签数组
    poi_latitude: 0,
    poi_longitude: 0,
    poi_altitude: 0,
    poi_remark: '',
    poi_regionInfo: { regionCodes: '', regionType: '' },

    // 障碍物相关字段
    obstacle_id: '',
    obstacle_orgId: orgId,
    obstacle_name: '',
    obstacle_addr: '',
    obstacle_kind: getParam().data.type,
    obstacle_airspaceType: '',
    obstacle_category: '',
    obstacle_tags: '[]',
    obstacle_geometries: '[]',
    obstacle_geoJson: '[]',
    obstacle_bot: 0,
    obstacle_top: 0,
    obstacle_displayStyle: JSON.stringify({ color: '#f7712f' }),
    obstacle_statusInfo: { status: 1, runningStatus: 'RUNNING' },
    obstacle_remark: '',
    obstacle_rule_idList: [defaultRuleId.value],
    obstacle_regionInfo: { regionCodes: '', regionType: '' },
  },
  editType: 'add',
  title: initTitle(getParam().data.type),
  loading: false,
})

// 清空障碍物相关数据
const clearObstacleData = () => {
  formContent.param.obstacle_name = ''
  formContent.param.obstacle_addr = ''
  formContent.param.obstacle_kind = ''
  formContent.param.obstacle_airspaceType = ''
  formContent.param.obstacle_category = ''
  formContent.param.obstacle_tags = '[]'
  formContent.param.obstacle_geometries = '[]'
  formContent.param.obstacle_geoJson = '[]'
  formContent.param.obstacle_bot = 0
  formContent.param.obstacle_top = 0
  formContent.param.obstacle_displayStyle = JSON.stringify({ color: '#f7712f' })
  formContent.param.obstacle_statusInfo = { status: 1, runningStatus: 'RUNNING' }
  formContent.param.obstacle_remark = ''
  formContent.param.obstacle_rule_idList = [defaultRuleId.value]
  formContent.param.obstacle_regionInfo = { regionCodes: '', regionType: '' }
}

// 表单引用
const formRef = ref(null)
const modelDetailsRef = ref(null)

// 子组件数据处理
const poiFormData = computed({
  get: () => {
    const data = {}
    Object.keys(formContent.param).forEach(key => {
      if (key.startsWith('poi_')) {
        data[key] = formContent.param[key]
      }
    })
    return data
  },
  set: newValue => {
    Object.assign(formContent.param, newValue)
  },
})

const obstacleFormData = computed({
  get: () => {
    const data = {}
    Object.keys(formContent.param).forEach(key => {
      if (key.startsWith('obstacle_')) {
        data[key] = formContent.param[key]
      }
    })
    return data
  },
  set: newValue => {
    Object.assign(formContent.param, newValue)
  },
})

const fieldsToSync = ['displayStyle', 'statusInfo', 'remark']

fieldsToSync.forEach(field => {
  const poiField = `poi_${field}`
  const obstacleField = `obstacle_${field}`

  // 监视 POI 字段的变化 -> 更新对应的障碍物字段
  watch(
    () => formContent.param[poiField],
    newValue => {
      if (newValue !== formContent.param[obstacleField]) {
        formContent.param[obstacleField] = newValue
      }
    },
  )

  // 监视障碍物字段的变化 -> 更新对应的 POI 字段
  watch(
    () => formContent.param[obstacleField],
    newValue => {
      if (newValue !== formContent.param[poiField]) {
        formContent.param[poiField] = newValue
      }
    },
  )
})

// 同步表单数据到地图
watch(
  () => formContent.param.obstacle_displayStyle,
  newValue => {
    if (isJSON(newValue)) {
      mapData.displayStyle = JSON.parse(newValue)
    } else {
      mapData.displayStyle = newValue || {}
    }
  },
)

const onMapDataUpdate = newMapData => {
  Object.assign(mapData, newMapData)
}

/** 初始化表单内容 **/
init()
function init() {
  if (getParam().data.id) {
    formContent.editType = 'edit'
    searchMark(getParam().data.id)
  }
}

// 查询标记
function searchMark(id) {
  const params = { id, offset: 0, limit: 10 }

  // 根据来源类型调用不同的API
  const apiMethod = isPoi.value ? api.poi_list : api.obstacle_list

  apiMethod(params).then(data => {
    const item = data.list[0]

    if (isPoi.value) {
      // 填充POI数据
      Object.assign(formContent.param, {
        poi_id: item.id,
        poi_name: item.name || '',
        poi_addr: item.addr || '',
        poi_airspaceType: item.airspaceType || '',
        poi_statusInfo: { status: item.status, runningStatus: item.runningStatus },
        poi_typeCode: item.typeCode || '',
        poi_typeName: item.typeName || '',
        poi_tags: JSON.stringify(item.tags) || '[]',
        poi_latitude: item.latitude,
        poi_longitude: item.longitude,
        poi_displayStyle: item.displayStyle || JSON.stringify({ color: '#f7712f' }),
        poi_altitude: item.altitude,
        poi_remark: item.remark || '',
        poi_regionInfo: { regionCodes: item.cityCode || '', regionType: '' },
      })

      // 回显POI点到地图
      if (item.longitude && item.latitude) {
        mapData.geometries = [
          {
            type: 'POINT',
            coordinate: [item.longitude, item.latitude],
          },
        ]
        mapData.id = item.id
      }

      // 检查是否有关联的障碍物数据
      if (item.obstacle.name) {
        showAssociation.value = true
        const obstacleRuleList = item.obstacle?.ruleList?.map(r => r.id) || []
        if (!obstacleRuleList.includes(defaultRuleId.value)) {
          obstacleRuleList.push(defaultRuleId.value)
        }
        Object.assign(formContent.param, {
          obstacle_id: item.obstacle.id || '',
          obstacle_name: item.obstacle.name || '',
          obstacle_addr: item.obstacle.addr || '',
          obstacle_kind: item.obstacle.kind || '',
          obstacle_airspaceType: item.obstacle.airspaceType || '',
          obstacle_category: item.obstacle.category || '',
          obstacle_tags: JSON.stringify(item.tags) || '[]',
          obstacle_bot: item.obstacle.bot || 0,
          obstacle_top: item.obstacle.top || 0,
          obstacle_displayStyle: item.obstacle.displayStyle || JSON.stringify({ color: '#f7712f' }),
          obstacle_statusInfo: { status: item.obstacle.status, runningStatus: item.obstacle.runningStatus },
          obstacle_remark: item.obstacle.remark || '',
          obstacle_rule_idList: obstacleRuleList,
          obstacle_regionInfo: {
            regionCodes: item.obstacle.cityCode || '',
            regionType: '',
          },
        })
      }
    } else {
      const obstacleRuleList = item.ruleList?.map(r => r.id) || []
      if (!obstacleRuleList.includes(defaultRuleId.value)) {
        obstacleRuleList.push(defaultRuleId.value)
      }
      // 填充障碍物数据
      Object.assign(formContent.param, {
        obstacle_id: item.id || '',
        obstacle_name: item.name || '',
        obstacle_addr: item.addr || '',
        obstacle_kind: item.kind || '',
        obstacle_airspaceType: item.airspaceType || '',
        obstacle_category: item.category || '',
        obstacle_tags: JSON.stringify(item.tags) || '[]',
        obstacle_bot: item.bot || 0,
        obstacle_top: item.top || 0,
        obstacle_displayStyle: item.displayStyle || JSON.stringify({ color: '#f7712f' }),
        obstacle_statusInfo: { status: item.status, runningStatus: item.runningStatus },
        obstacle_remark: item.remark || '',
        obstacle_rule_idList: obstacleRuleList,
        obstacle_regionInfo: { regionCodes: item.cityCode || '', regionType: '' },
      })
      // 回显几何数据到地图
      if (item.geometries) {
        mapData.geometries = item.geometries
      }
      if (item.geoJson) {
        mapData.geoJson = [item.geoJson]
      }
      mapData.id = item.id
    }
  })
}

// 地图交互
watch(
  () => mapData.geometries,
  geometriesFromMap => {
    if (!geometriesFromMap || geometriesFromMap.length === 0) {
      if (isPoi.value) {
        formContent.param.poi_longitude = 0
        formContent.param.poi_latitude = 0
      } else {
        formContent.param.obstacle_geometries = '[]'
        formContent.param.obstacle_geoJson = '[]'
      }
      return
    }

    const item = geometriesFromMap[0]

    if (isPoi.value) {
      // 新增或编辑POI时，从地图获取经纬度
      formContent.param.poi_longitude = item.coordinate[0]
      formContent.param.poi_latitude = item.coordinate[1]
      // 障碍物地图数据同步
      formContent.param.obstacle_geometries = JSON.stringify(geometriesFromMap)
      formContent.param.obstacle_geoJson = JSON.stringify(mapData.geoJson[0])
    } else {
      // 新增或编辑障碍物时，直接使用地图数据
      formContent.param.obstacle_geometries = JSON.stringify(geometriesFromMap)
      formContent.param.obstacle_geoJson = JSON.stringify(mapData.geoJson[0])
    }
  },
  { deep: true },
)

const exportIn = reactive({ json: '' })

const subIn = () => {
  if (!isJSON(exportIn.json)) {
    ElMessage.error('导入失败')
    return
  }

  ElMessage({ message: '导入成功', type: 'success' })
  formContent.exportIn = true

  const data = JSON.parse(exportIn.json)
  const geometries = []

  if (formContent.mapData.type === 'POINT') {
    data.forEach(item => {
      geometries.push({
        type: 'POINT',
        coordinate: item.coordinate,
      })
    })
  }

  formContent.param.obstacle_geometries = JSON.stringify(geometries)
  formContent.param.obstacle_geoJson = JSON.stringify(geometries)
}

/** 提交 **/
const submit = async () => {
  try {
    await formRef.value.validate()
  } catch {
    ElMessage.error('请检查表单填写是否正确')
    return
  }

  if (!orgId.value) {
    ElMessage.error('请选择机构')
    return
  }

  // 根据来源类型进行不同的验证
  if (isPoi.value) {
    // POI特有的验证逻辑可以在这里添加
  } else {
    if (!formContent.param.obstacle_airspaceType) {
      ElMessage.error('请选择障碍物空域类型')
      return
    }
  }
  // 检查几何体数据
  if (mapData.geometries.length === 0) {
    ElMessage.error('请选择地图模型')
    return
  }

  const result = await modelDetailsRef.value.validate()
  if (!result) {
    ElMessage.error('地图模型数据格式错误')
    return
  }

  const res = await ElMessageBox.confirm('确定提交吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
  if (res === 'cancel') {
    return
  }

  formContent.loading = true

  try {
    const payload = {}
    if (isPoi.value) {
      // 提取POI字段
      for (const key in formContent.param) {
        if (key.startsWith('poi_')) {
          if (key !== 'poi_regionInfo') {
            payload[key] = formContent.param[key]
          }
        }
      }
      payload.poi_regionCodes = JSON.stringify([formContent.param.poi_regionInfo.regionCodes])
      payload.poi_cityCode = formContent.param.poi_regionInfo.regionCodes
      payload.poi_status = formContent.param.poi_statusInfo.status
      payload.poi_runningStatus = formContent.param.poi_statusInfo.runningStatus
      // 如果是新增页面且有关联障碍物，则提取障碍物字段
      if (formContent.editType === 'add' && showAssociation.value) {
        for (const key in formContent.param) {
          if (key.startsWith('obstacle_')) {
            if (key !== 'obstacle_regionInfo') {
              payload[key] = formContent.param[key]
            }
          }
        }
        payload.obstacle_regionCodes = JSON.stringify([formContent.param.poi_regionInfo.regionCodes])
        payload.obstacle_cityCode = formContent.param.poi_regionInfo.regionCodes
        payload.rule_idList = JSON.stringify(formContent.param.obstacle_rule_idList)
        payload.obstacle_status = formContent.param.obstacle_statusInfo.status
        payload.obstacle_runningStatus = formContent.param.obstacle_statusInfo.runningStatus
      }
      // POI数据提交
      const url = formContent.editType === 'add' ? 'poi_create' : 'poi_update'
      await api[url](payload)
    } else {
      // 障碍物数据提交
      for (const key in formContent.param) {
        if (key.startsWith('obstacle_')) {
          if (key !== 'obstacle_regionInfo') {
            payload[key] = formContent.param[key]
          }
        }
      }
      payload.obstacle_regionCodes = JSON.stringify([formContent.param.obstacle_regionInfo.regionCodes])
      payload.obstacle_cityCode = formContent.param.obstacle_regionInfo.regionCodes
      payload.rule_idList = JSON.stringify(formContent.param.obstacle_rule_idList)
      payload.obstacle_status = formContent.param.obstacle_statusInfo.status
      payload.obstacle_runningStatus = formContent.param.obstacle_statusInfo.runningStatus
      const url = formContent.editType === 'add' ? 'obstacle_create' : 'obstacle_update'
      await api[url](payload)
    }

    ElMessage({ message: '保存成功', type: 'success' })
    setTimeout(() => {
      formContent.loading = false

      setPath(getParam().from, { tab: mapType.value }, { type: PathType.successBack })
    }, 1500)
  } catch {
    formContent.loading = false
  }
}
</script>

<style scoped lang="less">
.detail {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .card {
    width: 100%;
    display: flex;
    flex: 1;
    height: 100%;
    .flex-col {
      width: 100%;
      height: 100%;
      display: flex;
      flex: 1;
      flex-direction: column;
      .form-content {
        box-sizing: border-box;
        display: flex;
        flex: 1;
        flex-direction: column;
        padding: 0 20px 20px 20px;
        background-color: #ffffff; /* 白色背景 */
        border-radius: 0 16px 16px 0; /* 头部圆角 */
        width: 100%;
        .scroll {
          overflow-y: auto;
          height: v-bind(formHeight);
          /* 隐藏滚动条但保持滚动功能 */
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* IE and Edge */
        }
      }
    }
  }
}
::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
:deep(.el-form) {
  .el-form-item {
    margin-bottom: 20px;
    .el-form-item__label {
      font-weight: 500;
      color: var(--main-font-color9);
      margin-bottom: 8px;
      line-height: 1.4;
    }
    .el-input,
    .el-select {
      width: 100%;
    }
  }
}
</style>
