import { mapStatusToText } from '@/utils/helper.js'

// PoiPoint 组件表格配置
export const pointTableColumns = [
  {
    prop: 'name',
    label: '地点',
    width: 200,
    minWidth: 120,
  },
  {
    prop: 'addr',
    label: '地址',
    minWidth: 150,
  },
  {
    prop: 'cityName',
    label: '所属区县',
    minWidth: 150,
  },
  {
    prop: 'typeName',
    label: '类型',
    minWidth: 120,
  },
  {
    prop: 'status',
    label: '状态',
    minWidth: 80,
    type: 'tag',
    tagText: row => mapStatusToText(row.status, 'poi_status', '-'),
    tagTypeMap: { 1: 'success', 2: 'danger' },
  },
  {
    prop: 'runningStatus',
    label: '运行状态',
    minWidth: 100,
    type: 'tag',
    tagText: row => mapStatusToText(row.runningStatus, 'poi_runningStatus', '-'),
    tagTypeMap: { RUNNING: 'success', PLANNING: 'warning' },
  },
  {
    fixed: 'right',
    label: '操作',
    type: 'action',
    width: 200,
    actions: [
      {
        label: '编辑',
        type: 'primary',
        size: 'small',
        code: 'edit',
      },
      {
        label: '启用',
        type: 'success',
        size: 'small',
        code: 'enable',
        visible: row => row.status === 2,
      },
      {
        label: '停用',
        type: 'danger',
        size: 'small',
        code: 'disable',
        visible: row => row.status === 1,
      },
    ],
  },
]

// PoiPoint 搜索表单默认值
export const defaultSearchForm = {
  nameLike: undefined,
  addrLike: undefined,
  cityCodeList: undefined,
  status: undefined, // 改为可选择
  runningStatus: undefined, // 新增运行状态
  kind: 'POINT',
  deleted: 0,
}

// 状态选项配置
export const statusOptions = [
  { label: '启用', value: 1 },
  { label: '停用', value: 2 },
]

export const runningStatusOptions = [
  { label: '运行中', value: 'RUNNING' },
  { label: '规划中', value: 'PLANNING' },
]

// 表格配置
export const tableConfig = {
  stripe: true,
  border: false,
  showHeader: true,
  highlightCurrentRow: true,
  emptyText: '暂无数据',
}

// PoiPoint 表单配置项
export const pointFormItems = [
  {
    type: 'input',
    prop: 'nameLike',
    label: '地点名称',
    placeholder: '请输入地点名称模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'input',
    prop: 'addrLike',
    label: '地址',
    placeholder: '请输入地址模糊查询',
    span: 6,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'cityCodeList',
    prop: 'cityCodeList',
    label: '所属区县',
    span: 6,
  },

  {
    type: 'select',
    prop: 'status',
    label: '状态',
    placeholder: '请选择状态',
    span: 6,
    options: statusOptions,
    props: {
      clearable: true,
    },
  },
  {
    type: 'select',
    prop: 'runningStatus',
    label: '运行状态',
    placeholder: '请选择运行状态',
    span: 6,
    options: runningStatusOptions,
    props: {
      clearable: true,
    },
  },
  {
    type: 'slot',
    slotName: 'tagSelection',
    prop: 'tagSelection',
    label: '标签筛选',
    span: 6,
  },
]
