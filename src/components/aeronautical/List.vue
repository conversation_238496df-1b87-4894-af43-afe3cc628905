<template>
  <div class="custom">
    <div class="navigation">
      <div class="title">{{ paintTitle }}</div>
    </div>

    <div class="search">
      <div class="card">
        <el-row>
          <el-col :span="6">
            <div class="line">
              <div class="label">标题</div>
              <div class="in">
                <el-input placeholder="请输入" v-model="tableConfig.search.title"></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">类型</div>
              <div class="in">
                <el-select v-model="tableConfig.search.type" placeholder="请选择">
                  <el-option
                    v-for="item in informationTypeGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="operate">
              <div class="btn n" @click="search">查询</div>
              <div class="btn d" @click="reset">重置</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="content">
      <div class="card">
        <div class="filter">
          <div class="operate">
            <div class="btn n" @click="add">新增</div>
            <div class="btn d" @click="del">删除</div>
          </div>
        </div>
        <Table v-model:config="tableConfig" v-model:reload="tableConfigReload" @edit="edit" @select="selectTb"></Table>
      </div>
    </div>

    <el-dialog
      v-model="management.visible"
      :title="management.edit ? '编辑' : '新增'"
      width="640"
      top="1vh"
      @close="dataReset"
    >
      <div class="management dialog">
        <el-row>
          <el-col :span="12">
            <div class="line r">
              <div class="title w">类型</div>
              <div class="in">
                <el-select v-model="management.param.type" placeholder="请选择">
                  <el-option
                    v-for="item in informationTypeGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-col>

          <el-col :span="12">
            <div class="line l">
              <div class="title w">所属地区</div>
              <div class="in">
                <el-select v-model="management.param.cityCode" placeholder="请选择所属地区" style="width: 100%">
                  <el-option
                    v-for="item in areaOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="isAreaOptionDisabled(item)"
                  />
                </el-select>
              </div>
            </div>
          </el-col>

          <el-col :span="12">
            <div class="line r">
              <div class="title w">标题</div>
              <div class="in">
                <el-input v-model="management.param.title" placeholder="请输入"></el-input>
              </div>
            </div>
          </el-col>

          <el-col :span="12">
            <div class="line l">
              <div class="title w">发布时间</div>
              <div class="in">
                <el-date-picker
                  v-model="management.time"
                  type="datetime"
                  placeholder="请选择"
                  @change="getPublishTime"
                />
              </div>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="line">
              <div class="title w">内容</div>
              <div class="in">
                <el-input type="textarea" placeholder="请输入" :rows="3" v-model="management.param.content" />
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn c" @click="dataReset">取消</div>
          <div class="btn p" @click="submit">确定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import * as common from '@/utils/common.js'
import * as api from '@/request/api'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import { useDictionary } from '@/hooks/useDictionary.js'
import userOrg from '@/hooks/useOrg.js'
const { orgId, cityCode, cityName } = userOrg()
const areaOptions = useDictionary('所属地区')
// 组件
import Table from '@/components/common/Table.vue'
// 表格
const tableConfig = reactive({
  select: true,
  pagination: true,
  active: true,
  api: 'aeronautical_list',
  timeSearch: null,
  operate: 1,
  search: {
    type: undefined,
    title: undefined,
    deleted: 0,
    offset: 0,
    limit: 10,
  },
  column: [
    {
      prop: 'title',
      label: '标题',
      template: true,
      width: 150,
    },
    {
      prop: 'typeName',
      label: '类型',
      template: true,
      width: 150,
    },
    {
      prop: 'content',
      label: '内容',
      template: true,
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 200,
      template: false,
      formatter: v => {
        return common.formatTime(v.createTime).t7
      },
    },
    {
      prop: 'publishTime',
      label: '发布时间',
      width: 200,
      template: false,
      formatter: v => {
        return common.formatTime(v.publishTime).t7
      },
    },
  ],
})
const tableConfigReload = ref(0)

// 标题
import useComponent from '@/hooks/useComponent.js'
const { pathData } = useComponent()
const paintTitle = ref(pathData.value.name)

/** 查询 **/
const informationTypeGroup = ref([])
// 获取字典
getDic()
function getDic() {
  let p = {
    dicId: 'UkT7UDzdiY29R265heiME',
  }
  api.dic_list(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      const c = common.groupByBelongTo(d)
      c[0].children[0].itemList.map(item => {
        informationTypeGroup.value.push({
          label: item.itemValue,
          value: item.itemId,
        })
      })
    }
  })
}

/** 查询 **/
const search = () => {
  tableConfigReload.value = Math.random()
}
/** 重置 **/
const reset = () => {
  tableConfig.search.title = undefined
  tableConfig.search.type = undefined
}

/** 编辑 **/
const management = reactive({
  visible: false,
  edit: false,
  lock: false,
  loading: false,
  time: undefined,
  param: {
    orgId: orgId.value,
    cityCode: cityCode.value,
    type: '',
    title: '',
    content: '',
    fileIds: [],
    publishTime: '',
    startTime: -1,
    endTime: -1,
    status: 1,
  },
})
const add = () => {
  management.visible = true
}
const edit = o => {
  console.log(o)
  for (let i in o) {
    management.param[i] = o[i]
  }
  management.time = common.formatTime(o.publishTime).t1
  management.edit = true
  management.visible = true
}

const getPublishTime = o => {
  management.param.publishTime = common.newDateToFormatTime(o).t2
}

/** 所属地区 **/
const isAreaOptionDisabled = option => {
  if (orgId.value === 'cb8upAzuM07uQyNpzuj05') {
    return false
  }
  return option.label !== cityName.value
}

const submit = () => {
  if (management.lock) {
    ElMessage.error(t('message.lockError'))
    return
  }
  if (management.param.type === '') {
    ElMessage.error(t('message.aeroTypeLack'))
  } else if (management.param.title === '') {
    ElMessage.error(t('message.aeroTitleLack'))
  } else if (management.param.publishTime === '') {
    ElMessage.error(t('message.aeroPublishTimeLack'))
  } else if (management.param.content === '') {
    ElMessage.error(t('message.aeroContentLack'))
  } else {
    let url = management.edit ? 'aeronautical_update' : 'aeronautical_create'
    management.lock = true
    api[url](management.param)
      .then(() => {
        ElMessage({
          message: t('message.saveSuccess'),
          type: 'success',
        })
        setTimeout(() => {
          tableConfigReload.value = Math.random()
          dataReset()
        }, common.globalTime())
      })
      .catch(() => {
        management.lock = false
      })
  }
}

/**  删除  **/
let selectData = []
const selectTb = o => {
  selectData = []
  o.map(item => {
    selectData.push(item.id)
  })
}
const del = () => {
  if (selectData.length === 0) {
    ElMessage.error(t('message.selectError'))
  } else {
    ElMessageBox.confirm('是否确定删除当前信息', '警告', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        api.aeronautical_delete({ idList: JSON.stringify(selectData) }).then(() => {
          ElMessage({
            message: t('message.deleteSuccess'),
            type: 'success',
          })
          setTimeout(() => {
            tableConfigReload.value = Math.random()
            dataReset()
          }, common.globalTime())
        })
      })
      .catch(() => {})
  }
}

function dataReset() {
  management.visible = false
  management.param.type = ''
  management.param.title = ''
  management.param.content = ''
  management.param.fileIds = ''
  management.param.publishTime = ''
  management.param.cityCode = cityCode.value
}
</script>

<style>
@import '../rule/common/custom.css';
</style>
