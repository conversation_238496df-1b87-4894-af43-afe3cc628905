<template>
  <div class="control">
    <ShowMenu />
    <DrawerMenu v-show="showDrawer" :type />
  </div>
</template>

<script setup>
import ShowMenu from '@/components/common/map/showMenu/ShowMenu.vue'
import DrawerMenu from '@/components/common/map/DrawerMenu.vue'

const { type } = defineProps(['type'])
const showDrawer = computed(() => !!type)
</script>

<style scoped lang="less">
.control {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}
</style>
