<template>
  <div class="map-control">
    <div class="up-part-container">
      <div v-for="(item, index) in typesArr" :key="index" class="item-container">
        <img
          :src="item === currentMenu ? getImg('map/' + item + '_s.png') : getImg('map/' + item + '.png')"
          alt=""
          draggable="false"
          class="img"
          @click="changeMenu(item)"
        />
      </div>
    </div>
    <div class="item-container">
      <img :src="getImg('map/remove.png')" alt="" draggable="false" class="img" @click="onClickDelete" />
    </div>
  </div>
</template>

<script setup>
import { getImg } from '@/utils/getAsset.js'
import { getMapType } from '@/utils/mapHelper.js'
import useMapBase from '@/hooks/useMapBase.js'
const { type } = defineProps(['type'])
const typesArr = computed(() => getMapType(type))
const { currentMenu, changeMenu, onRemove } = useMapBase()
const onClickDelete = () => onRemove?.()
</script>

<style scoped lang="less">
.map-control {
  pointer-events: visibleFill;
  position: absolute;
  top: 20px;
  right: 24px;
  display: flex;
  flex-direction: column;
  width: 44px;
  box-sizing: border-box;
  .up-part-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    border-radius: 8px;
    margin-bottom: 10px;
  }
  .item-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 44px;
    background: white;
    border-radius: 8px;
    margin-bottom: 6px;
    .img {
      width: 28px;
      height: 28px;
      cursor: pointer;
      padding: 8px;
    }
  }
  .interval {
    width: 20px;
    height: 1px;
    background: #e4e7ed;
  }
}
</style>
