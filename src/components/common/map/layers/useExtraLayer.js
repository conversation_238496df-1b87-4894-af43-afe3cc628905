import * as turf from '@turf/turf'
import { calculateBuffer, calculateDifference } from '@/components/map/utils/calculateUtils.js'

const useExtraLayer = (data, width, type) => {
  const formatData = computed(() => {
    if (type === 'POINT')
      return {
        type: 'FeatureCollection',
        features: data.map(item => {
          return turf.circle(item.coordinate, width / 1000, {
            properties: {
              key: '点状异常区',
              name: '点状异常区',
              height: 0,
              color: '#DC143C',
              opacity: 0.3,
              index: 0,
              center: item.coordinate,
              radius: width,
            },
          })
        }),
      }
    if (type === 'LINE') {
      const line = {
        type: 'FeatureCollection',
        features: data.map(item => {
          return turf.lineString(item.coordinateList, {
            properties: {
              key: '线状异常区',
              name: '线状异常区',
              height: 0,
              color: '#DC143C',
              opacity: 0.3,
              index: 0,
            },
          })
        }),
      }
      return {
        type: 'FeatureCollection',
        features: data.map(item => {
          return {
            type: 'Feature',
            properties: {
              key: '线状异常区',
              name: '线状异常区',
              height: 0,
              color: '#DC143C',
              opacity: 0.1,
              index: 0,
            },
            geometry: {
              type: 'MultiPolygon',
              coordinates: [turf.buffer(line, width, { units: 'meters' }).features[0]?.geometry.coordinates],
            },
          }
        }),
      }
    }
    if (type === 'PLANE') {
      const polygon = turf.polygon(
        [calculateBuffer(data[0]?.shellCoordinateList, width), data[0]?.shellCoordinateList],
        {
          properties: {
            key: '多边形状异常区',
            name: '多边形状异常区',
            height: 0,
            color: '#DC143C',
            opacity: 0.3,
            index: 0,
          },
        },
      )
      return {
        type: 'FeatureCollection',
        features: data.map(item => {
          return {
            type: 'Feature',
            properties: {
              key: '线状异常区',
              name: '线状异常区',
              height: 0,
              color: '#DC143C',
              opacity: 0.1,
              index: 0,
            },
            geometry: {
              type: 'MultiPolygon',
              coordinates: [polygon.geometry.coordinates],
            },
          }
        }),
      }
    }
  })
  return formatData.value
}

export default useExtraLayer
