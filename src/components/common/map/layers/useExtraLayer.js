import { <PERSON>Lay<PERSON>, PointLayer, PolygonLayer } from '@antv/l7'
import LAYER_NAME from '@/components/common/map/data/LayerName.js'
import { EmptyData } from '@/data/MapConstant.js'

const useExtraLayer = (layers, scene) => {
  const extraPointLayer = new PointLayer()
    .source(EmptyData)
    .shape('triangle')
    .color('color')
    .size('size')
    .style({
      opacity: 0.9,
      strokeWidth: 2,
      stroke: '#FFF',
    })
    .hide()
  layers[LAYER_NAME.extraPoint] = markRaw(extraPointLayer)

  const extraLineLayer = new LineLayer()
    .source(EmptyData)
    .shape('line')
    .color('color')
    .size('coverWidth')
    .style({
      opacity: 0.5,
    })
    .hide()
  layers[LAYER_NAME.extraLine] = markRaw(extraLineLayer)

  const extraAreaLayer = new PolygonLayer()
    .source(EmptyData)
    .shape('fill')
    .color('color')
    .style({
      opacity: 0.5,
      strokeWidth: 10,
    })
    .hide()
  layers[LAYER_NAME.extraArea] = markRaw(extraAreaLayer)
}

export default useExtraLayer
