import LAYER_NAME from '@/components/common/map/data/LayerName.js'
import { PolygonLayer, PointLayer, LineLayer } from '@antv/l7'
import useShowData from '@/hooks/useShowData.js'

const useLowLayer = (layers, scene) => {
  const { airspace, airway, airline, poi, obstacle_point, obstacle_line, obstacle_plane } = useShowData()
  // 空域图层
  const airSpaceLayer = new PolygonLayer()
    .source(airspace)
    .shape('fill')
    .color('color')
    .style({
      opacity: 0.5,
      strokeWidth: 10,
    })
    .hide()
  layers[LAYER_NAME.airSpace] = markRaw(airSpaceLayer)

  // 航路图层
  const airWayLayer = new LineLayer()
    .source(airway)
    .shape('line')
    .size(2)
    .color('color')
    .style({
      opacity: 0.7,
    })
    .hide()
  layers[LAYER_NAME.airWay] = markRaw(airWayLayer)

  // 航路覆盖图层
  const airWayCoverLayer = new LineLayer()
    .source(airway)
    .shape('line')
    .size('coverWidth')
    .color('color')
    .style({
      opacity: 0.3,
    })
    .hide()
  layers[LAYER_NAME.airWayCover] = markRaw(airWayCoverLayer)

  // 航线图层
  const airLineLayer = new LineLayer()
    .source(airline)
    .shape('line')
    .size(1)
    .color('color')
    .style({
      opacity: 0.7,
      lineType: 'dash',
      dashArray: [5, 2],
    })
    .hide()
  layers[LAYER_NAME.airLine] = markRaw(airLineLayer)

  // POI图层
  const poiLayer = new PointLayer()
    .source(poi)
    .shape('circle')
    .color('color')
    .size(6)
    .style({
      opacity: 0.7,
    })
    .hide()
  layers[LAYER_NAME.poi] = markRaw(poiLayer)

  // 障碍物点图层
  const obstaclePointLayer = new PointLayer()
    .source(obstacle_point)
    .shape('circle')
    .color('color')
    .size(6)
    .style({
      opacity: 0.7,
    })
    .hide()
  layers[LAYER_NAME.obstaclePoint] = markRaw(obstaclePointLayer)

  // 障碍物线图层
  const obstacleLineLayer = new LineLayer()
    .source(obstacle_line)
    .shape('line')
    .color('color')
    .size(1)
    .style({
      opacity: 0.7,
    })
    .hide()
  layers[LAYER_NAME.obstacleLine] = markRaw(obstacleLineLayer)

  // 障碍物面图层
  const obstacleAreaLayer = new PolygonLayer()
    .source(obstacle_plane)
    .shape('fill')
    .color('color')
    .style({
      opacity: 0.7,
    })
    .hide()
  layers[LAYER_NAME.obstacleArea] = markRaw(obstacleAreaLayer)
}

export default useLowLayer
