import { useEventBus } from '@vueuse/core'
import useLowLayer from '@/components/common/map/layers/useLowLayer.js'
import useExtraLayer from '@/components/common/map/layers/useExtraLayer.js'
import { getImg } from '@/utils/getAsset.js'

const layers = shallowReactive({})

const layerRefreshBus = useEventBus('LayerRefresh')
layerRefreshBus.on(({ key, data, show = true, extra = null }) => {
  if (layers[key]) {
    if (data) layers[key].setData(data)
    if (show) {
      layers[key].show()
    } else layers[key].hide()
  } else {
    try {
      throw new Error('请确认图层的Key是否存在')
    } catch (e) {}
  }
})

const onMapLoadedInit = scene => {
  scene.value.addImage('airport', getImg('map/icon/airport.png'))
  scene.value.addImage('emergency', getImg('map/icon/emergency.png'))
}

const useLayer = scene => {
  const init = (field, value) => {
    onMapLoadedInit(scene)
    useLowLayer(layers, scene)
    useExtraLayer(layers, scene)
    Object.keys(layers).forEach(key => scene.value.addLayer(layers[key]))
  }
  scene.value && init()
  return layers
}

export default useLayer
