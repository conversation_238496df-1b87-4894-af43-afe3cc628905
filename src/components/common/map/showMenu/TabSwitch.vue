<template>
  <div class="tab-switch">
    <div
      v-for="(tab, index) in tabs"
      :key="index"
      :class="{ tab: true, active: index === active }"
      @click="clickTab(index)"
    >
      {{ tab }}
    </div>
  </div>
</template>

<script setup>
const { tabs, defaultActive, onChange } = defineProps({
  tabs: {
    type: Array,
    default: () => [],
  },
  defaultActive: {
    type: Number,
    default: -1,
  },
  onChange: {
    type: Function,
    default: () => {},
  },
})
const active = ref(defaultActive)
const clickTab = index => {
  active.value = index === active.value ? -1 : index
  onChange?.(active.value)
}
defineExpose({
  currentIndex: computed(() => active.value),
  setActive: index => (active.value = index),
})
</script>

<style scoped lang="less">
.tab-switch {
  width: fit-content;
  height: 40px;
  display: flex;
  align-items: center;
  flex: 0 0 1;
  padding: 11px 4px;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  font-size: 16px;
  line-height: 18px;
  z-index: 100;
  .tab {
    height: 32px;
    margin-right: 12px;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.6);
    padding: 7px 12px;
    box-sizing: border-box;
    border-radius: 50px;
    background: transparent;
    &:last-child {
      margin-right: 0;
    }
    &.active {
      color: #fff;
      background: var(--el-color-primary);
    }
  }
}
</style>
