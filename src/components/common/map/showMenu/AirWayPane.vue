<template>
  <div class="air-way-pane">
    <div class="item">
      <span>开启图层: </span>
      <el-switch v-model="config.enabled" active-text="开启" inactive-text="关闭" />
    </div>
    <div v-show="config.enabled" class="content">
      <div class="item">
        <span>{{ config.runningStatus.label }}:</span>
        <el-checkbox-group v-model="config.runningStatus.case">
          <el-checkbox
            v-for="(i, index) in config.runningStatus.options"
            :key="index"
            :label="i"
            :value="index"
            size="small"
          />
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<script setup>
const { config } = defineProps(['config'])
</script>

<style scoped lang="less">
.air-way-pane {
  display: flex;
  flex-direction: column;
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    span {
      color: white;
      font-size: 14px;
    }
  }
  .content {
    border-top: rgba(255, 255, 255, 0.1) 1px solid;
    margin-top: 6px;
    padding-top: 10px;
    display: flex;
    flex-direction: column;
  }
}
:deep(.el-switch__label:not(.is-active)) {
  color: rgba(255, 255, 255, 0.6);
}
:deep(.el-checkbox:not(:last-child)) {
  margin-right: 10px;
}
:deep(.el-checkbox:not(.is-checked) .el-checkbox__label) {
  color: rgba(255, 255, 255, 0.6);
}
</style>
