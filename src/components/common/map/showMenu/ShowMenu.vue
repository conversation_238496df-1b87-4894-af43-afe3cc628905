<template>
  <div class="map-menu">
    <TabSwitch ref="tabSwitch" :tabs="tabData.map(i => i.name)" />
    <component :is="tabComponents[tabSwitch?.currentIndex]" :config class="config-pane" />
  </div>
</template>

<script setup>
import TabSwitch from '@/components/common/map/showMenu/TabSwitch.vue'
import AirSpacePlanningPane from '@/components/common/map/showMenu/AirSpacePlanningPane.vue'
import AirSpaceCommonUsedPane from '@/components/common/map/showMenu/AirSpaceCommonUsedPane.vue'
import AirWayPane from '@/components/common/map/showMenu/AirWayPane.vue'
import AirLinePane from '@/components/common/map/showMenu/AirLinePane.vue'
import ImportantLocationPane from '@/components/common/map/showMenu/ImportantLocationPane.vue'
import TemporaryAreaPane from '@/components/common/map/showMenu/TemporaryAreaPane.vue'
import useShowMenu from '@/hooks/useShowMenu.js'

const tabSwitch = useTemplateRef('tabSwitch')
const showMenu = useShowMenu()
const config = computed(() => showMenu[tabData[tabSwitch.value?.currentIndex ?? 0]?.key])
const tabData = Object.keys(showMenu).map(key => ({ key, name: showMenu[key].name }))
const tabComponents = [
  markRaw(AirSpacePlanningPane),
  markRaw(AirWayPane),
  markRaw(AirLinePane),
  markRaw(AirSpaceCommonUsedPane),
  markRaw(ImportantLocationPane),
  markRaw(TemporaryAreaPane),
]
</script>

<style scoped lang="less">
.map-menu {
  position: absolute;
  top: 20px;
  left: 24px;
  z-index: 10;
  pointer-events: visibleFill;
  .config-pane {
    margin-top: 10px;
    width: 350px;
    background: rgba(255, 255, 255, 1);
    border-radius: 6px;
    box-sizing: border-box;
    padding: 12px 16px;
  }
}
</style>
