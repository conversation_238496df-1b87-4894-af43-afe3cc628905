<template>
  <div class="map-menu">
    <TabSwitch :tabs="tabData.map(i => i.name)" ref="tabSwitch" />
    <component :is="tabComponents[tabSwitch?.currentIndex]" :config class="config-pane" />
  </div>
</template>

<script setup>
import TabSwitch from '@/components/common/map/showMenu/TabSwitch.vue'
import AirSpacePane from '@/components/common/map/showMenu/AirSpacePane.vue'
import AirWayPane from '@/components/common/map/showMenu/AirWayPane.vue'
import AirLinePane from '@/components/common/map/showMenu/AirLinePane.vue'
import ObstaclePane from '@/components/common/map/showMenu/ObstaclePane.vue'
import POIPane from '@/components/common/map/showMenu/POIPane.vue'
import useShowMenu from '@/hooks/useShowMenu.js'

const tabSwitch = useTemplateRef('tabSwitch')
const showMenu = useShowMenu()
const config = computed(() => showMenu[tabData[tabSwitch.value?.currentIndex ?? 0]?.key])
const tabData = Object.keys(showMenu).map(key => ({ key, name: showMenu[key].name }))
const tabComponents = [
  markRaw(AirSpacePane),
  markRaw(AirWayPane),
  markRaw(AirLinePane),
  markRaw(ObstaclePane),
  markRaw(POIPane),
]
</script>

<style scoped lang="less">
.map-menu {
  position: absolute;
  top: 20px;
  left: 24px;
  z-index: 10;
  pointer-events: visibleFill;
  .config-pane {
    margin-top: 10px;
    width: 350px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border-radius: 16px;
    box-sizing: border-box;
    padding: 12px 16px;
  }
}
</style>
