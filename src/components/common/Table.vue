<template>
  <div class="wrap" :style="{ height: tableConfig.height ? tableConfig.height + 'px' : 'auto' }">
    <div class="tb" :style="{ height: tbHeight + 'px' }">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; height: 100%"
        :header-cell-style="{ background: '#f5f5f5', color: 'rgba(0, 0, 0, 0.4)' }"
        @row-click="toDetailPage"
        @select="handleSelect"
        @select-all="handleSelect"
      >
        <el-table-column v-if="tableConfig.select" type="selection" width="55" />
        <el-table-column
          v-for="(item, index) in tableProp"
          :key="index"
          :label="item.label"
          :formatter="item.formatter"
          :width="item.width"
          show-overflow-tooltip
        >
          <template v-if="item.template" #default="scope">
            <span>{{ scope.row[item.prop] }}</span>
            <div v-show="item.type === 1">
              <div class="name">
                <img :src="getImg(scope.row.defaultRule === 1 ? 'star_fill.png' : 'star_empty.png')" alt="" />
                {{ scope.row.ruleName }}
              </div>
            </div>
            <div v-show="item.type === 3">
              <el-switch
                v-model="scope.row.activeStatus"
                class="ml-2"
                :disabled="scope.row.defaultRule === 1"
                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                @change="getActiveStatus(scope.row)"
              />
            </div>
            <div v-show="item.type === 4">
              <div
                class="mask"
                v-if="scope.row.enterAlarmLevel > 0"
                :class="[getAlarmLevelColor(scope.row.enterAlarmLevel)]"
              >
                {{ scope.row.enterAlarmLevel }}级
              </div>
              <div
                class="mask"
                v-if="scope.row.leaveAlarmLevel > 0"
                :class="[getAlarmLevelColor(scope.row.leaveAlarmLevel)]"
              >
                {{ scope.row.leaveAlarmLevel }}级
              </div>
              <div
                class="mask"
                v-if="scope.row.heightAlarmLevel > 0"
                :class="[getAlarmLevelColor(scope.row.heightAlarmLevel)]"
              >
                {{ scope.row.heightAlarmLevel }}级
              </div>
            </div>
            <div v-show="item.type === 5">
              <template v-if="scope.row.heightAlarmLevel > 0">
                <div>-</div>
              </template>
              <template v-else>
                <template v-if="scope.row.bufferAlarmLevel > 0">
                  <div class="mask" :class="[getAlarmLevelColor(scope.row.bufferAlarmLevel)]">
                    {{ scope.row.bufferAlarmLevel }}级
                  </div>
                </template>
                <template v-else>
                  <div>-</div>
                </template>
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="tableConfig.operate" label="操作" width="120" fixed="right">
          <template #default="scope">
            <div v-show="tableConfig.operate === 1">
              <span class="edit" @click.stop="edit(scope.row)">编辑</span>
            </div>
            <div
              v-show="
                tableConfig.operate === 2 &&
                (scope.row.status === -1 || scope.row.status === 0 || scope.row.status === 3)
              "
            >
              <span class="pass" @click.stop="pass(scope.row)">通过</span>
              <span class="reject" @click.stop="reject(scope.row)">拒绝</span>
            </div>
            <div v-show="tableConfig.operate === 3 && scope.row.status === 3">
              <span class="pass" @click.stop="accept(scope.row)">接收</span>
            </div>
            <div v-show="tableConfig.operate === 3 && scope.row.status === 5">
              <span class="pass" @click.stop="pass(scope.row)">通过</span>
              <span class="reject" @click.stop="reject(scope.row)">拒绝</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="tableConfig.pagination" class="pl">共{{ total }}项数据</div>
    <div v-if="tableConfig.pagination" class="pt">
      <el-pagination
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        :background="pageBackground"
        layout="sizes, prev, pager, next"
        :total="total"
        @size-change="onSizeChange"
        @current-change="onChangePage"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import userOrg from '@/hooks/useOrg.js'
const { cityCode } = userOrg()
import { getImg } from '@/utils/getAsset.js'
// 表格配置
const tableConfig = defineModel('config')
const reload = defineModel('reload')
const emit = defineEmits(['passPlan', 'acceptPlan', 'rejectPlan', 'toDetail', 'edit', 'active', 'select'])
const tableData = ref([])
const loading = ref(true)

// 分页 配置
const page = ref(1)
const pageSize = ref(10)
const pageSizes = ref([10, 20, 30, 40, 50])
const pageBackground = ref(true)
const total = ref(0)
const tbHeight = ref(0)
const onChangePage = o => {
  page.value = o
  loadData()
}
const onSizeChange = o => {
  pageSize.value = o
  loadData()
}

// 表格数据
import * as api from '@/request/api/index'
const tableProp = ref([])
watch(reload, () => {
  loadData()
})

loadData()
function loadData() {
  if (tableConfig.value.piece) {
    tbHeight.value = tableConfig.value.height
  } else {
    tbHeight.value = tableConfig.value.height ? tableConfig.value.height - 88 : 'auto'
  }
  tableProp.value = []
  tableConfig.value.column.map(item => {
    tableProp.value.push({
      prop: item.prop,
      label: item.label,
      template: item.template,
      scope: item.scope,
      formatter: item.formatter,
      width: item.width,
      type: item.type,
    })
  })
  const p = tableConfig.value.search
  p.limit = pageSize.value
  p.offset = (page.value - 1) * pageSize.value
  if (cityCode && cityCode === '330600') {
    delete p.cityCodeList
  }
  api[tableConfig.value.api](p).then(data => {
    const res = data.list
    if (tableConfig.value.active) {
      res.map(item => {
        item.activeStatus = item.status === 1
      })
    }
    tableData.value = res
    total.value = res.length === 0 ? 0 : data.total
    loading.value = false
  })
}

function getAlarmLevelColor(v) {
  let color
  switch (v) {
    case 1:
      color = 'one_l'
      break
    case 2:
      color = 'two_l'
      break
    case 3:
      color = 'three_l'
      break
    case 4:
      color = 'four_l'
      break
  }
  return color
}

// 操作
const toDetailPage = row => {
  emit('toDetail', row)
}
const pass = row => {
  emit('passPlan', row)
}
const accept = row => {
  emit('acceptPlan', row)
}
const reject = row => {
  emit('rejectPlan', row)
}
const edit = row => {
  emit('edit', row)
}
const handleSelect = row => {
  emit('select', row)
}
const getActiveStatus = item => {
  emit('active', item)
}
</script>

<style scoped lang="less">
.wrap {
  position: relative;
  padding: 12px 24px;
  box-sizing: border-box;
  width: 100%;
  .tb {
    box-sizing: border-box;
  }
  .pt {
    position: absolute;
    bottom: 24px;
    right: 24px;
  }
  .pl {
    position: absolute;
    bottom: 35px;
    left: 24px;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.6);
  }
  .pass,
  .edit {
    color: var(--main-color);
    margin-right: 10px;
    cursor: pointer;
  }
  .reject {
    color: #fe4066;
    cursor: pointer;
  }
}
:deep(.el-table__header tr) {
  height: 48px;
}
:deep(.el-table__body tr) {
  height: 56px;
}
:deep(.el-table .cell) {
  font-size: 16px;
}
:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .btn-next) {
  border-radius: 8px;
  color: var(--main-font-color6);
}
:deep(.el-pagination.is-background .el-pager li) {
  background-color: transparent;
  color: var(--main-font-color6);
}
:deep(.el-pagination.is-background .el-pager li.is-active) {
  background-color: var(--main-color);
  color: #fff;
  border-radius: 8px;
  box-shadow: var(--main-box-shadown);
}
:deep(.el-sub-menu.is-active.is-opened > .el-sub-menu__title) {
  color: var(--main-color);
}
:deep(.el-select__wrapper.is-focused) {
  box-shadow: 0 0 0 1px var(--main-color);
}
:deep(.el-table .cell) {
  min-width: 50px;
  white-space: nowrap;
}
.name {
  display: flex;
  align-items: center;
  img {
    margin-right: 3px;
  }
}
.mask {
  border-width: 1px;
  border-style: solid;
  width: 50px;
  height: 30px;
  line-height: 26px;
  text-align: center;
  border-radius: 6px;
  font-size: 18px;
  font-weight: bold;
}
.mask.one_l {
  border-color: rgba(0, 176, 70, 0.5);
  color: rgba(0, 176, 70, 0.5);
  background: rgba(0, 176, 70, 0.12);
}
.mask.two_l {
  border-color: rgba(253, 171, 3, 0.5);
  color: rgba(253, 171, 3, 0.5);
  background: rgba(253, 171, 3, 0.12);
}
.mask.three_l {
  border-color: rgba(245, 41, 12, 0.5);
  color: rgba(245, 41, 12, 0.5);
  background: rgba(245, 41, 12, 0.12);
}
.mask.four_l {
  border-color: rgba(255, 60, 197, 0.5);
  color: rgba(255, 60, 197, 0.5);
  background: rgba(255, 60, 197, 0.12);
}
</style>
