# TypeTags 标签选择组件

一个灵活、美观的标签式选择器，支持单选、多选，并提供展开/收起功能来处理大量选项。

## 基础用法

```vue
<template>
  <div>
    <TypeTags 
      v-model="selectedItems"
      :options="tagOptions"
      :multiple="true"
    />
    <p>当前选择: {{ selectedItems }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TypeTags from '@/components/common/typeTags/TypeTags.vue';

// v-model 可以是数组，也可以是逗号分隔的字符串
const selectedItems = ref(['value1', 'value3']); 
// const selectedItems = ref('value1,value3');

const tagOptions = ref([
  { label: '选项一', value: 'value1' },
  { label: '选项二', value: 'value2' },
  { label: '选项三', value: 'value3' },
  // ... 更多选项
]);
</script>
```


## 使用示例

### 标签模式

```vue
<template>
  <div>
    <TypeTags 
      v-model="selectedItems"
      dic-type="空域标签"
    />
    <p>当前选择: {{ selectedItems }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TypeTags from '@/components/common/typeTags/TypeTags.vue';

const selectedItems = ref(['value1', 'value3']);
</script>
```



### 单选模式

```vue
<template>
  <TypeTags 
    v-model="selectedItem"
    :options="statusOptions"
    :multiple="false"
  />
</template>

<script setup>
import { ref } from 'vue';

const selectedItem = ref('RUNNING'); // 单选模式下，modelValue 应为字符串

const statusOptions = ref([
  { label: '运行中', value: 'RUNNING' },
  { label: '规划中', value: 'PLANNING' },
  { label: '已停止', value: 'STOPPED' },
]);
</script>
```
