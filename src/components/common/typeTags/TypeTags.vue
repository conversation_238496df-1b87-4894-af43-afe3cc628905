<template>
  <div class="type-tags-container">
    <div class="tags-wrapper" :class="{ 'is-expanded': isExpanded }">
      <div
        v-for="item in visibleOptions"
        :key="item.value"
        class="custom-tag"
        :class="{ 'tag-active': isSelected(item.value) }"
        @click="toggleSelection(item.value)"
      >
        {{ item.label }}
      </div>
    </div>
    <div v-if="visibleOptions.length > 10" class="toggle-button" @click="isExpanded = !isExpanded">
      {{ isExpanded ? '收起' : '展开' }}
      <el-icon class="toggle-icon" :class="{ 'is-rotated': !isExpanded }"><ArrowUp /></el-icon>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ArrowUp } from '@element-plus/icons-vue'
import { isJSON } from '@/utils/helper.js'
import { useDictionary } from '@/hooks/useDictionary.js'

const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: () => [],
  },
  options: {
    type: Array,
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  dicType: {
    type: String,
    default: '规则',
  },
})

const defaultOptions = computed(() => {
  return useDictionary(props.dicType)
})

const emit = defineEmits(['update:modelValue'])

const isExpanded = ref(false)
const selectedValues = ref([])

const visibleOptions = computed(() => {
  if (isExpanded.value || props.options.length <= 10) {
    return props.options.length > 0 ? props.options : defaultOptions.value
  }
  return props.options.length > 0 ? props.options.slice(0, 10) : defaultOptions.value.slice(0, 10)
})

const isSelected = value => {
  return selectedValues.value.includes(value)
}

watch(
  () => props.modelValue,
  newValue => {
    let parsedValue = []
    if (isJSON(newValue)) {
      parsedValue = JSON.parse(newValue)
    } else {
      parsedValue = newValue
    }
    if (Array.isArray(parsedValue)) {
      if (JSON.stringify(parsedValue) !== JSON.stringify(selectedValues.value)) {
        selectedValues.value = parsedValue
      }
    }
  },
  { immediate: true },
)

// 当用户点击标签时触发
const toggleSelection = value => {
  if (props.multiple) {
    const index = selectedValues.value.indexOf(value)
    if (index > -1) {
      selectedValues.value.splice(index, 1)
    } else {
      selectedValues.value.push(value)
    }
  } else {
    // 单选模式
    selectedValues.value = [value]
  }

  emit('update:modelValue', JSON.stringify(selectedValues.value))
}
</script>

<style scoped>
.type-tags-container {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex-grow: 1;
  overflow: hidden;
}

.custom-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  padding: 0 10px;
  font-size: 12px;
  line-height: 24px;
  border-radius: 4px;
  white-space: nowrap;
  border: 1px solid #e9e9eb;
  background-color: #f4f4f5;
  color: #909399;
  cursor: pointer;
  transition: all 0.3s ease;
}

.custom-tag:hover {
  border-color: #c0c4cc;
}

.tag-active {
  background-color: #f0f9eb;
  border-color: #11ce66;
  color: #11ce66;
}

.tag-active:hover {
  background-color: #e1f3d8;
  border-color: #11ce66;
}

.toggle-button {
  display: flex;
  align-items: center;
  color: #f7712f;
  cursor: pointer;
  font-size: 12px;
  flex-shrink: 0;
  height: 24px;
  line-height: 24px;
}

.toggle-button:hover {
  color: #f99b6d;
}

.toggle-icon {
  margin-left: 4px;
  transition: transform 0.3s ease;
}

.toggle-icon.is-rotated {
  transform: rotate(180deg);
}
</style>
