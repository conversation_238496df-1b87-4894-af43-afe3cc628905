<template>
  <el-select v-model="value" placeholder="请选择" size="large" clearable filterable>
    <el-option
      v-for="(item, index) in options"
      :key="index"
      :label="item.label"
      :value="isJson ? (isStr ? item.jsonStrValue : item.jsonValue) : item.value"
    />
  </el-select>
</template>

<script setup>
import useDict from '@/hooks/useDict.js'

const { dictList } = useDict()

const {
  id = null,
  isJson = true,
  isStr = false,
} = defineProps({
  id: {
    type: String,
  },
  isJson: {
    type: Boolean,
  },
  isStr: {
    type: Boolean,
  },
})

const options = computed(() => {
  return dictList.find(a => a.id === id)?.options || []
})

const value = defineModel()

const jsonValue = computed(() => {
  return options.value.find(a => a.jsonValue === value.value)?.jsonStrValue
})

defineExpose({
  jsonValue,
})
</script>

<style scoped lang="less"></style>
