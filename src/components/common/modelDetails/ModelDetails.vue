<template>
  <div class="model-info">
    <div v-if="localGeometries.length > 0" class="geometry-item">
      <div v-if="localGeometries[0].type" class="geometry-title">
        {{ getGeometryTitle(localGeometries[0].type) }}
        <span v-if="validationError" class="error-message">{{ validationError }}</span>
      </div>

      <!-- 点类型 -->
      <div v-if="localGeometries[0].type === 'POINT'" class="tb">
        <div class="th">
          <div class="name">经度</div>
          <div class="name">纬度</div>
        </div>
        <div class="tr">
          <div class="td">
            <el-input
              :model-value="localGeometries[0].coordinate[0]"
              @input="updateGeometryCoordinate(0, $event)"
              @blur="formatAndValidate(localGeometries[0].coordinate, 0)"
            />
          </div>
          <div class="td">
            <el-input
              :model-value="localGeometries[0].coordinate[1]"
              @input="updateGeometryCoordinate(1, $event)"
              @blur="formatAndValidate(localGeometries[0].coordinate, 1)"
            />
          </div>
        </div>
      </div>

      <!-- 线类型 -->
      <div v-if="localGeometries[0].type === 'LINE'" class="tb" :class="{ 'tb-extended': showAirwayFields }">
        <div class="table-wrapper" :class="{ 'with-scroll': showAirwayFields }">
          <div class="th">
            <div v-if="showAirwayFields" class="name">编号</div>
            <div v-if="showAirwayFields" class="name">名称</div>
            <div class="name">经度</div>
            <div class="name">纬度</div>
            <div v-if="showAirwayFields" class="name">海拔(米)</div>
          </div>
          <div v-for="(coordinate, coordIndex) in localGeometries[0].coordinateList" :key="coordIndex" class="tr">
            <div v-if="showAirwayFields" class="td">
              <el-input
                :model-value="getAirwayPoint(coordIndex, 'code')"
                @input="updateAirwayPoint(coordIndex, 'code', $event)"
              />
            </div>
            <div v-if="showAirwayFields" class="td">
              <el-input
                :model-value="getAirwayPoint(coordIndex, 'name')"
                @input="updateAirwayPoint(coordIndex, 'name', $event)"
              />
            </div>
            <div class="td">
              <el-input
                :model-value="coordinate[0]"
                @input="updateLineCoordinate(coordIndex, 0, $event)"
                @blur="formatAndValidate(localGeometries[0].coordinateList, coordIndex, 0)"
              />
            </div>
            <div class="td">
              <el-input
                :model-value="coordinate[1]"
                @input="updateLineCoordinate(coordIndex, 1, $event)"
                @blur="formatAndValidate(localGeometries[0].coordinateList, coordIndex, 1)"
              />
            </div>
            <div v-if="showAirwayFields" class="td">
              <el-input
                :model-value="getAirwayPoint(coordIndex, 'altitude')"
                type="number"
                @input="updateAirwayPoint(coordIndex, 'altitude', $event)"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 圆形类型 -->
      <div v-if="localGeometries[0].type === 'CIRCLE'" class="tb">
        <div class="th">
          <div class="name" style="width: 100%">圆心坐标</div>
        </div>
        <div class="tr">
          <div class="td">
            <el-input
              :model-value="localGeometries[0].centerCoordinate[0]"
              placeholder="经度"
              @input="updateCircleCenterCoordinate(0, $event)"
              @blur="formatAndValidate(localGeometries[0].centerCoordinate, 0)"
            />
          </div>
          <div class="td">
            <el-input
              :model-value="localGeometries[0].centerCoordinate[1]"
              placeholder="纬度"
              @input="updateCircleCenterCoordinate(1, $event)"
              @blur="formatAndValidate(localGeometries[0].centerCoordinate, 1)"
            />
          </div>
        </div>
        <div class="th">
          <div class="name" style="width: 100%">半径(米)</div>
        </div>
        <div class="tr">
          <div class="td" style="flex: 1; text-align: center">
            <el-input
              :model-value="localGeometries[0].radius?.toFixed(2) || '0.00000'"
              type="number"
              @input="updateCircleRadius($event)"
            />
          </div>
        </div>
      </div>

      <!-- 矩形类型 -->
      <div v-if="localGeometries[0].type === 'RECTANGLE'" class="tb">
        <div class="th">
          <div class="name">东北角坐标</div>
        </div>
        <div class="th">
          <div class="name">经度</div>
          <div class="name">纬度</div>
        </div>
        <div class="tr">
          <div class="td">
            <el-input
              :model-value="localGeometries[0].northEastCoordinate[0]"
              @input="updateRectangleCoordinate('northEast', 0, $event)"
              @blur="formatAndValidateRectangle('northEast', 0)"
            />
          </div>
          <div class="td">
            <el-input
              :model-value="localGeometries[0].northEastCoordinate[1]"
              @input="updateRectangleCoordinate('northEast', 1, $event)"
              @blur="formatAndValidateRectangle('northEast', 1)"
            />
          </div>
        </div>
        <div class="th">
          <div class="name">西南角坐标</div>
        </div>
        <div class="th">
          <div class="name">经度</div>
          <div class="name">纬度</div>
        </div>
        <div class="tr">
          <div class="td">
            <el-input
              :model-value="localGeometries[0].southWestCoordinate[0]"
              @input="updateRectangleCoordinate('southWest', 0, $event)"
              @blur="formatAndValidateRectangle('southWest', 0)"
            />
          </div>
          <div class="td">
            <el-input
              :model-value="localGeometries[0].southWestCoordinate[1]"
              @input="updateRectangleCoordinate('southWest', 1, $event)"
              @blur="formatAndValidateRectangle('southWest', 1)"
            />
          </div>
        </div>
      </div>

      <!-- 多边形类型 -->
      <div v-if="localGeometries[0].type === 'POLYGON'" class="tb">
        <div class="th">
          <div class="name">经度</div>
          <div class="name">纬度</div>
        </div>
        <div v-for="(coordinate, coordIndex) in localGeometries[0].shellCoordinateList" :key="coordIndex" class="tr">
          <div class="td">
            <el-input
              :model-value="coordinate[0]"
              @input="updatePolygonShellCoordinate(coordIndex, 0, $event)"
              @blur="formatAndValidate(localGeometries[0].shellCoordinateList, coordIndex, 0)"
            />
          </div>
          <div class="td">
            <el-input
              :model-value="coordinate[1]"
              @input="updatePolygonShellCoordinate(coordIndex, 1, $event)"
              @blur="formatAndValidate(localGeometries[0].shellCoordinateList, coordIndex, 1)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

// 定义组件接收的props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({ geometries: [] }),
  },
  airwayPoints: {
    type: Array,
    default: () => [],
  },
  // 是否显示航路点的额外字段（名称、编号、海拔）
  showAirwayFields: {
    type: Boolean,
    default: false,
  },
})

// 定义事件发射器
const emit = defineEmits(['update:modelValue', 'update:airwayPoints'])

// 本地状态，用于表单绑定
const localGeometries = ref([])
const localAirwayPoints = ref([])
const validationError = ref('') // For error messages

// 校验常量
const LON_MIN = 103.99918
const LON_MAX = 137.21573
const LAT_MIN = 18.56204
const LAT_MAX = 41.449

// 深拷贝函数
const deepClone = obj => JSON.parse(JSON.stringify(obj))

// 全局校验函数
const validateGeometries = () => {
  if (!localGeometries.value || localGeometries.value.length === 0) {
    validationError.value = ''
    return
  }

  const geometry = localGeometries.value[0]
  let coordinates = []
  let typeName = ''

  switch (geometry.type) {
    case 'POINT':
      coordinates.push({ lon: geometry.coordinate[0], lat: geometry.coordinate[1] })
      typeName = '点'
      break
    case 'LINE':
      geometry.coordinateList.forEach(c => coordinates.push({ lon: c[0], lat: c[1] }))
      typeName = '线路'
      break
    case 'POLYGON':
      geometry.shellCoordinateList.forEach(c => coordinates.push({ lon: c[0], lat: c[1] }))
      typeName = '多边形'
      break
    case 'CIRCLE':
      coordinates.push({ lon: geometry.centerCoordinate[0], lat: geometry.centerCoordinate[1] })
      typeName = '圆形'
      break
    case 'RECTANGLE':
      coordinates.push({ lon: geometry.northEastCoordinate[0], lat: geometry.northEastCoordinate[1] })
      coordinates.push({ lon: geometry.southWestCoordinate[0], lat: geometry.southWestCoordinate[1] })
      typeName = '矩形'
      break
  }

  for (const [index, coord] of coordinates.entries()) {
    const pointName = coordinates.length > 1 ? `第 ${index + 1} 个点` : `${typeName}`
    if (coord.lon < LON_MIN || coord.lon > LON_MAX) {
      validationError.value = `${pointName}的经度超出范围 (${LON_MIN} ~ ${LON_MAX})`
      return
    }
    if (coord.lat < LAT_MIN || coord.lat > LAT_MAX) {
      validationError.value = `${pointName}的纬度超出范围 (${LAT_MIN} ~ ${LAT_MAX})`
      return
    }
  }

  validationError.value = ''
}

// 格式化和校验函数
const formatAndValidate = (list, index1, index2) => {
  let value
  if (index2 !== undefined) {
    value = list[index1][index2]
  } else {
    value = list[index1]
  }

  const parsed = parseFloat(value)
  if (!isNaN(parsed)) {
    const formatted = parseFloat(parsed.toFixed(5))
    if (index2 !== undefined) {
      list[index1][index2] = formatted
    } else {
      list[index1] = formatted
    }
  }

  validateGeometries()
  updateAndEmit()
}

// 矩形特殊处理
const formatAndValidateRectangle = (corner, coordinateIndex) => {
  const coord = localGeometries.value[0][`${corner}Coordinate`]
  const parsed = parseFloat(coord[coordinateIndex])
  if (!isNaN(parsed)) {
    coord[coordinateIndex] = parseFloat(parsed.toFixed(5))
  }
  validateGeometries()
  updateAndEmit()
}

// 监听外部传入的modelValue变化，同步到本地状态
watch(
  () => props.modelValue,
  newValue => {
    localGeometries.value = deepClone(newValue.geometries || [])
    validateGeometries()
  },
  { immediate: true, deep: true },
)

watch(
  () => props.airwayPoints,
  newPoints => {
    localAirwayPoints.value = deepClone(newPoints || [])
  },
  { immediate: true, deep: true },
)

// 获取几何类型的中文标题
const getGeometryTitle = type => {
  const titleMap = {
    POINT: '点',
    LINE: '线路',
    CIRCLE: '圆形',
    RECTANGLE: '矩形',
    POLYGON: '多边形',
  }
  return titleMap[type] || type
}

// 更新并发出事件
const updateAndEmit = () => {
  const newValue = {
    ...props.modelValue,
    geometries: deepClone(localGeometries.value),
  }
  emit('update:modelValue', newValue)
}

const updateAirwayPointsAndEmit = () => {
  emit('update:airwayPoints', deepClone(localAirwayPoints.value))
}

// 点类型坐标更新
const updateGeometryCoordinate = (coordinateIndex, value) => {
  localGeometries.value[0].coordinate[coordinateIndex] = parseFloat(value) || 0
  updateAndEmit()
}

// 线类型坐标更新
const updateLineCoordinate = (coordIndex, coordinateIndex, value) => {
  localGeometries.value[0].coordinateList[coordIndex][coordinateIndex] = parseFloat(value) || 0
  updateAndEmit()
}

// 圆形圆心坐标更新
const updateCircleCenterCoordinate = (coordinateIndex, value) => {
  localGeometries.value[0].centerCoordinate[coordinateIndex] = parseFloat(value) || 0
  updateAndEmit()
}

// 圆形半径更新
const updateCircleRadius = value => {
  localGeometries.value[0].radius = parseFloat(value) || 0
  updateAndEmit()
}

// 矩形坐标更新
const updateRectangleCoordinate = (corner, coordinateIndex, value) => {
  if (corner === 'northEast') {
    localGeometries.value[0].northEastCoordinate[coordinateIndex] = parseFloat(value) || 0
  } else if (corner === 'southWest') {
    localGeometries.value[0].southWestCoordinate[coordinateIndex] = parseFloat(value) || 0
  }
  updateAndEmit()
}

// 多边形外壳坐标更新
const updatePolygonShellCoordinate = (coordIndex, coordinateIndex, value) => {
  localGeometries.value[0].shellCoordinateList[coordIndex][coordinateIndex] = parseFloat(value) || 0
  updateAndEmit()
}

// 获取航路点字段值
const getAirwayPoint = (coordIndex, field) => {
  if (!localAirwayPoints.value[coordIndex]) {
    // 创建默认航路点
    localAirwayPoints.value[coordIndex] = {
      code: '',
      name: '',
      altitude: 0,
    }
  }

  return localAirwayPoints.value[coordIndex][field]
}

// 航路点字段更新
const updateAirwayPoint = (coordIndex, field, value) => {
  if (!localAirwayPoints.value[coordIndex]) {
    localAirwayPoints.value[coordIndex] = {
      code: '',
      name: '',
      altitude: 0,
    }
  }

  localAirwayPoints.value[coordIndex][field] = field === 'altitude' ? parseFloat(value) || 0 : value
  updateAirwayPointsAndEmit()
}
</script>

<style scoped lang="less">
.model-info {
  width: 100%;

  .geometry-item {
    margin-bottom: 20px;
    border: 1px solid var(--main-border-color, #e4e7ed);
    border-radius: 4px;
    padding: 10px;

    .geometry-title {
      font-size: 16px;
      font-weight: bold;
      color: var(--main-font-color, #333);
      margin-bottom: 10px;
      padding: 8px 12px;
      background: var(--main-box-color3, #f5f7fa);
      border-radius: 4px;
      display: flex;
      align-items: center;
    }
  }

  .error-message {
    color: #f56c6c;
    font-size: 12px;
    margin-left: 15px;
    font-weight: normal;
  }

  .tb {
    width: 100%;
    margin-bottom: 10px;

    // 扩展模式的表格样式
    &.tb-extended {
      .table-wrapper {
        &.with-scroll {
          overflow-x: auto;
          overflow-y: visible;
          // 添加自定义滚动条样式
          &::-webkit-scrollbar {
            height: 6px;
          }
          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }
          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
            &:hover {
              background: #a8a8a8;
            }
          }
        }
      }

      .th,
      .tr {
        min-width: 600px;
        width: max-content;
      }

      .th .name,
      .tr .td {
        min-width: 120px;
        flex: 0 0 120px;
      }
    }

    .table-wrapper {
      width: 100%;
    }

    .th,
    .tr {
      display: flex;
      width: 100%;
      min-height: 40px;
    }

    .th {
      background: var(--main-box-color4, #f0f2f5);
      box-sizing: border-box;
      color: var(--main-font-color4, #666);
      align-items: center;
      font-weight: bold;
      border: 1px solid var(--main-border-color, #e4e7ed);

      .name {
        flex: 1;
        text-align: center;
        padding: 8px 5px;
        border-right: 1px solid var(--main-border-color, #e4e7ed);

        &:last-child {
          border-right: none;
        }
      }
    }

    .tr {
      align-items: center;
      border: 1px solid var(--main-border-color, #e4e7ed);
      border-top: none;

      .td {
        flex: 1;
        text-align: center;
        padding: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-right: 1px solid var(--main-border-color, #e4e7ed);

        &:last-child {
          border-right: none;
        }

        &[colspan='3'] {
          border-right: none;
        }
      }
    }
  }

  :deep(.el-input) {
    width: 100%;

    .el-input__wrapper {
      box-shadow: 0 0 0 1px var(--main-border-color, #dcdfe6) inset;
    }
  }

  :deep(.el-button) {
    margin: 0 2px;

    &.el-button--small {
      padding: 5px 8px;
      font-size: 12px;
    }
  }
}
</style>
