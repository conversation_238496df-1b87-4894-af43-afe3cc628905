<template>
  <el-card class="common-card">
    <template #header>
      <div class="card-header">
        <span>{{ title }}</span>
      </div>
    </template>
    <slot></slot>
  </el-card>
</template>

<script setup>
const { title = null, marginBottom = '24px' } = defineProps({
  title: {
    type: String,
  },
  marginBottom: {
    type: String,
  },
})
</script>

<style scoped lang="less">
.common-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: v-bind(marginBottom);
  box-shadow: none;
  border: none;
  --el-card-padding: 0;
  position: relative;

  :deep(.el-card__header) {
    padding: 0 0 24px 0;
    border-bottom: none;
    font-weight: 700;
    font-size: 18px;
  }
}
</style>
