<template>
  <div class="tab">
    <div
      v-for="(item, index) in list"
      :key="index"
      :class="{ btn: true, active: currentIndex === index }"
      @click="currentIndex = index"
    >
      {{ item }}
    </div>
  </div>
</template>

<script setup>
const { list = [], defaultIndex = 0 } = defineProps({
  list: {
    type: Array,
  },
  defaultIndex: {
    type: Number,
  },
})
const currentIndex = ref(defaultIndex)

defineExpose({
  currentIndex,
})
</script>

<style scoped lang="less">
.tab {
  display: flex;

  .btn {
    margin-left: 16px;
    height: 40px;
    padding: 0 16px;
    font-weight: 400;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.6);
    text-align: center;
    align-content: center;
    border-radius: 8px;
    cursor: pointer;
  }

  .active {
    background-color: var(--el-color-primary);
    box-shadow: 0 12px 12px 0 rgba(247, 113, 47, 0.16);
    color: #ffffff;
    font-weight: 700;
  }
}
</style>
