<template>
  <el-icon v-if="iconComponent" :style="{ cursor: onClick ? 'pointer' : '' }" v-bind="filteredAttrs">
    <component
      :is="iconComponent"
      :color="color ?? 'rgba(0, 0, 0, 0.6)'"
      :style="{ width: realSize, height: realSize }"
    />
  </el-icon>
</template>

<script setup>
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
const props = defineProps({
  name: {
    type: String,
    required: true,
  },
})
const iconComponent = computed(() => ElementPlusIconsVue[props.name])
const attrs = useAttrs()
const { onClick, size, color, ...filteredAttrs } = attrs
const realSize = computed(() => {
  if (!size) return '20px'
  if (!size.toString().endsWith('px')) return size + 'px'
  return size
})
</script>

<style scoped></style>
