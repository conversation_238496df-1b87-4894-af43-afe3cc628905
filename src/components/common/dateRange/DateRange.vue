<template>
  <div class="validity-period-container">
    <el-radio-group v-if="props.showRadioGroup" v-model="validityType" @change="handleTypeChange">
      <el-radio-button value="timeRange">时间范围</el-radio-button>
      <el-radio-button value="longTerm">长期有效</el-radio-button>
    </el-radio-group>
    <el-date-picker
      v-if="validityType === 'timeRange'"
      v-model="datePickerValue"
      v-bind="defaultProps"
      @change="handleDateChange"
    />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({ beginTime: null, endTime: null }),
  },
  // 是否显示“长期有效”的单选切换按钮
  showRadioGroup: {
    type: Boolean,
    default: false,
  },
  defaultProps: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue'])

const validityType = ref('timeRange')
const datePickerValue = ref([])

watch(
  () => props.modelValue,
  newValue => {
    if (!newValue || (!newValue.beginTime && !newValue.endTime)) {
      // 空值或无效值，重置为默认时间范围模式
      validityType.value = 'timeRange'
      datePickerValue.value = []
    } else if (newValue.beginTime === -1 && props.showRadioGroup) {
      // 外部设置为了“长期有效”
      validityType.value = 'longTerm'
      datePickerValue.value = []
    } else {
      // 外部设置了具体的时间范围
      validityType.value = 'timeRange'
      datePickerValue.value = [newValue.beginTime, newValue.endTime]
    }
  },
  { immediate: true, deep: true },
)

const handleTypeChange = newType => {
  if (newType === 'longTerm') {
    datePickerValue.value = [] // 清空日期选择器
    emit('update:modelValue', { beginTime: -1, endTime: -1 })
  } else {
    emit('update:modelValue', { beginTime: null, endTime: null })
  }
}

const handleDateChange = newRange => {
  if (newRange && newRange.length === 2) {
    emit('update:modelValue', { beginTime: newRange[0], endTime: newRange[1] })
  } else {
    emit('update:modelValue', { beginTime: null, endTime: null })
  }
}
</script>

<style scoped lang="less">
/* 有效期容器样式  */
.validity-period-container {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

:deep(.el-radio-button__inner) {
  padding: 8px;
  font-size: 14px;
}

.validity-date-picker {
  flex: 1;
  min-width: 300px;
}

:deep(.el-radio-group) {
  align-items: unset;
  flex-wrap: nowrap;
}
</style>
