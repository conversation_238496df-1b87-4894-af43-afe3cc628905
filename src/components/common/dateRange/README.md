# DateRange 组件

一个灵活的日期范围选择组件，支持常规的日期范围选择和“长期有效”的特殊场景。

## 使用示例

### 1. 基础用法

默认只显示日期范围选择器。

```vue
<template>
  <DateRange v-model="dateInfo" />
</template>

<script setup>
import { ref } from 'vue';
import DateRange from './DateRange.vue';

const dateInfo = ref({ startTime: '2024-01-01', endTime: '2024-01-31' });
</script>
```

### 2. 高级用法：带模式切换

通过设置 `show-radio-group` 属性为 `true`，可以显示“时间范围”和“长期有效”的切换按钮。

当用户选择“长期有效”时，`v-model` 的值会自动更新为 `{ startTime: -1, endTime: -1 }`。

```vue
<template>
  <DateRange v-model="dateInfo" :show-radio-group="true" />
</template>

<script setup>
import { ref } from 'vue';
import DateRange from './DateRange.vue';

// 初始可以是长期有效
const dateInfo = ref({ beginTime: -1, endTime: -1 }); 
</script>
```

