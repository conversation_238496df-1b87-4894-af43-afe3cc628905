<template>
  <div class="statistic-card">
    <img :src="getImg(img)" style="position: absolute; right: 24px; top: 24px; width: 56px; height: 56px" />
    <el-statistic :value="value">
      <template #title>
        <div style="display: inline-flex; align-items: center; font-size: 16px">{{ title }}</div>
      </template>
    </el-statistic>
    <div class="statistic-footer">
      <div v-if="showData">
        <div v-if="!dataList.length" class="footer-item">
          <span>{{ subTitle + ':' }}</span>
          <span :class="{ green: subValue >= 0, red: subValue < 0 }">
            {{ Number(subValue.toFixed(2)) + subUnit }}
          </span>
        </div>
        <div v-else class="footer-list">
          <div v-for="(item, index) in dataList" :key="index">
            <span>{{ item.subTitle + ':' }}</span>
            <span :class="item.color">
              {{ Number(item.subValue.toFixed(2)) + (item.subUnit || '') }}
            </span>
          </div>
        </div>
      </div>
      <div v-else style="height: 24px"></div>
    </div>
  </div>
</template>

<script setup>
import { getImg } from '@/utils/getAsset.js'

const {
  title = '',
  value = 0,
  subTitle = '',
  subValue = 0,
  subUnit = '',
  img = '',
  dataList = [],
  showData = true,
} = defineProps({
  title: {
    type: String,
  },
  value: {
    type: Number,
  },
  subTitle: {
    type: String,
  },
  subValue: {
    type: Number,
  },
  subUnit: {
    type: String,
  },
  img: {
    type: String,
  },
  dataList: {
    type: Array,
  },
  showData: {
    type: Boolean,
  },
})
</script>

<style scoped lang="less">
:global(h2#card-usage ~ .example .example-showcase) {
  background-color: var(--el-fill-color) !important;
}

.statistic-card {
  padding: 20px;
  border-radius: 12px;
  background-color: var(--el-bg-color-overlay);
  --el-statistic-title-color: rgba(0, 0, 0, 0.6);
  :deep(.el-statistic__number) {
    font-size: 36px;
    font-weight: 700;
  }
  position: relative;
}

.statistic-content {
  height: 50px;
}

.statistic-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  font-size: 16px;
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.6);
}

.statistic-footer .footer-list {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.green {
  margin-left: 8px;
  color: rgb(0, 176, 70);
}
.red {
  margin-left: 8px;
  color: var(--el-color-error);
}
.blue {
  margin-left: 8px;
  color: rgb(20, 146, 255);
}
</style>
