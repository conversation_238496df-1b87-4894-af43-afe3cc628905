<template>
  <div class="pagination-container">
    <div v-if="total > 0" class="total">共 {{ total }} 项数据</div>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      style="width: 100%"
      :page-sizes="pageSizes"
      layout="sizes, prev, pager, next"
      :total="total"
      :background="true"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { nextTick } from 'vue'

const page = defineModel('page', { type: Number, default: 1 })
const limit = defineModel('limit', { type: Number, default: 10 })

defineProps({
  total: {
    required: true,
    type: Number,
  },
  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 30, 50]
    },
  },
  // 移动端页码按钮的数量端默认值5
  pagerCount: {
    type: Number,
    default: document.body.clientWidth < 992 ? 5 : 7,
  },
  layout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper',
  },
  background: {
    type: Boolean,
    default: true,
  },
  hidden: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['pagination'])

function handleSizeChange(val) {
  limit.value = val
  // 当改变页面大小时，重置到第一页
  page.value = 1
  nextTick(() => {
    emit('pagination')
  })
}

function handleCurrentChange(val) {
  page.value = val
  nextTick(() => {
    emit('pagination')
  })
}
</script>

<style scoped lang="less">
.pagination-container {
  width: 100%;
  display: flex;
  align-items: center;

  .total {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.6);
    width: 200px;
  }

  :deep(.number) {
    border-radius: 8px;
  }

  :deep(.number:not(.is-active)) {
    background-color: transparent;
  }
}

:deep(.el-pagination) {
  --el-pagination-border-radius: 8px;
  justify-content: flex-end;
}
</style>
