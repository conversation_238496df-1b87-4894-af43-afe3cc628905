<template>
  <div class="airspace-type">
    <!-- 高度范围输入 -->
    <div class="height-range">
      <div class="height-input">
        <span class="label">底高</span>
        <el-input-number
          v-model="botHeight"
          :min="0"
          :precision="0"
          placeholder="0"
          :controls="false"
          style="width: 80%"
          @blur="validateHeight"
        />
        <span class="unit">米</span>
      </div>
      <span class="separator">~</span>
      <div class="height-input">
        <span class="label">顶高</span>
        <el-input-number
          v-model="topHeight"
          :min="0"
          :precision="0"
          placeholder="50"
          :controls="false"
          style="width: 80%"
          @blur="validateHeight"
        />
        <span class="unit">米</span>
      </div>
    </div>

    <!-- 空域类型标签 -->
    <div class="airspace-tags">
      <div class="label" style="font-weight: 500">空域类型:</div>
      <div class="tags">
        <div
          v-for="item in airspaceTypes"
          :key="item.code"
          class="custom-tag"
          :class="{
            'tag-active': item.active,
            'tag-matched': item.matched,
            'tag-disabled': !item.matched,
          }"
          @click="handleTagClick(item)"
        >
          {{ item.code }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 定义飞行高度层级常量
const FLIGHT_LEVELS = [
  { id: 'ow9qUAvs8FkgopQdWZeID', min: 0, max: 120 },
  { id: 'IfYs2kH1SxNvsydpDk1qD', min: 120, max: 300 },
  { id: 'nCgkxpgVzLDnM1d99As4T', min: 300, max: 600 },
]

// 定义组件属性
const props = defineProps({
  ifAirport: {
    type: Boolean,
    default: false,
  },
  bot: {
    type: [Number, String],
    default: 0,
  },
  top: {
    type: [Number, String],
    default: 50,
  },
  type: {
    type: String,
    default: '',
  },
  heightId: {
    type: String,
    default: '',
  },
  flightLevelIds: {
    type: Array,
    default: () => [],
  },
})

// 定义事件
const emit = defineEmits([
  'update:ifAirport',
  'update:bot',
  'update:top',
  'update:type',
  'update:heightId',
  'update:flightLevelIds',
])

// 本地状态
const botHeight = ref(props.bot)
const topHeight = ref(props.top)
const isManualMode = ref(false)

// 空域类型定义
const airspaceTypes = reactive([
  { code: 'A', name: 'A类空域', minHeight: 6000, maxHeight: 20000, active: false, matched: false },
  { code: 'B', name: 'B类空域', minHeight: 0, maxHeight: 6000, active: false, matched: false },
  { code: 'C', name: 'C类空域', minHeight: 0, maxHeight: 600, active: false, matched: false },
  { code: 'D', name: 'D类空域', minHeight: 20000, maxHeight: Infinity, active: false, matched: false },
  { code: 'E', name: 'E类空域', minHeight: 0, maxHeight: Infinity, active: false, matched: false },
  { code: 'G', name: 'G类空域', minHeight: 0, maxHeight: 300, active: false, matched: false },
  { code: 'W', name: '特殊用途非管制空域', minHeight: 0, maxHeight: 120, active: false, matched: false },
])

// 用于计算flightLevelIds
watch(
  [botHeight, topHeight],
  ([newBot, newTop]) => {
    const bot = Number(newBot) || 0
    const top = Number(newTop) || 0

    // 输入验证
    if (bot < 0 || top < 0 || bot > 600 || top > 600) {
      ElMessage.warning('高度范围必须在 0-600 米之间')
      // 清空ID或根据业务需求决定如何处理
      emit('update:flightLevelIds', [])
      return
    }
    if (bot > top) {
      // 如果底高大于顶高，暂时不计算，等待用户输入正确
      return
    }

    const selectedLevelIds = []
    for (const level of FLIGHT_LEVELS) {
      // 判断高度范围是否与层级有重叠
      // 重叠条件：(层级起点 <= 输入终点) AND (层级终点 >= 输入起点)
      if (level.min < top && level.max > bot) {
        selectedLevelIds.push(level.id)
      }
    }

    emit('update:flightLevelIds', selectedLevelIds)
  },
  { immediate: true },
)

const updateVModel = () => {
  const activeTypes = airspaceTypes.filter(type => type.active).map(type => type.code)
  emit('update:type', activeTypes.join(','))
}

// 切换到手动模式
const enterManualMode = () => {
  if (isManualMode.value) return
  isManualMode.value = true
}

// 切换到自动模式
const enterAutoMode = () => {
  isManualMode.value = false
  updateTypesFromHeight()
}

// 从高度更新类型（仅在自动模式下）
const updateTypesFromHeight = () => {
  if (isManualMode.value) return
  const botVal = Number(botHeight.value) || 0
  const topVal = Number(topHeight.value) || 0

  airspaceTypes.forEach(type => {
    const isMatched =
      (botVal <= type.maxHeight && topVal >= type.minHeight) ||
      (botVal >= type.minHeight && botVal <= type.maxHeight) ||
      (topVal >= type.minHeight && topVal <= type.maxHeight)
    type.matched = isMatched
    // B和C类空域不自动激活，但保留其匹配状态以供手动选择
    type.active = isMatched && !['B', 'C'].includes(type.code)
  })
  updateVModel()
}

const handleTagClick = type => {
  if (!type.matched) return
  enterManualMode() // 任何点击都会进入手动模式
  type.active = !type.active
  updateVModel()
}

watch(
  () => props.type,
  newType => {
    if (newType) {
      // 当外部传入类型时，进入手动模式并设置激活状态
      enterManualMode()
      const types = newType.split(',')
      airspaceTypes.forEach(item => {
        item.active = types.includes(item.code)
      })
    }
  },
  { immediate: true },
)

// 监听高度变化
watch([botHeight, topHeight], () => {
  if (isManualMode.value) {
    const botVal = Number(botHeight.value) || 0
    const topVal = Number(topHeight.value) || 0
    let modelChanged = false
    airspaceTypes.forEach(type => {
      const isMatched =
        (botVal <= type.maxHeight && topVal >= type.minHeight) ||
        (botVal >= type.minHeight && botVal <= type.maxHeight) ||
        (topVal >= type.minHeight && topVal <= type.maxHeight)
      type.matched = isMatched
      if (!isMatched && type.active) {
        type.active = false
        modelChanged = true
      }
    })
    if (modelChanged) {
      updateVModel()
    }
  } else {
    updateTypesFromHeight()
  }
})

// 监听底高变化
watch(
  () => props.bot,
  newValue => {
    botHeight.value = newValue
  },
)
// 监听顶高变化
watch(
  () => props.top,
  newValue => {
    topHeight.value = newValue
  },
)

// 验证高度输入
const validateHeight = () => {
  // 确保底高不小于0
  if (botHeight.value < 0) {
    botHeight.value = 0
  }
  // 确保顶高不小于0
  if (topHeight.value < 0) {
    topHeight.value = 0
  }

  // 确保高度不超过600
  if (botHeight.value > 600) {
    botHeight.value = 600
  }
  if (topHeight.value > 600) {
    topHeight.value = 600
  }
  if (botHeight.value > topHeight.value) {
    ElMessage.warning('底高不能大于顶高')
    // 如果底高大于顶高，则将底高设置为顶高高度
    botHeight.value = topHeight.value
  }

  emit('update:bot', botHeight.value)
  emit('update:top', topHeight.value)
  // 每次修改高度都强制返回自动模式
  enterAutoMode()
}

onMounted(() => {
  if (!props.type) {
    updateTypesFromHeight()
  }
})
</script>

<style scoped>
.airspace-type {
  width: 100%;
}

.height-range {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.height-input {
  display: flex;
  align-items: center;
}

.label {
  margin-right: 8px;
  white-space: nowrap;
}

.separator {
  margin: 0 12px;
  font-size: 16px;
}

.unit {
  margin-left: 8px;
  white-space: nowrap;
}

.airspace-tags {
  margin-top: 12px;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 自定义标签样式 */
.custom-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  padding: 0 10px;
  font-size: 12px;
  line-height: 24px;
  border-radius: 4px;
  white-space: nowrap;
  border: 1px solid #e9e9eb;
  background-color: #f4f4f5;
  color: #909399;
  cursor: pointer;
  transition: all 0.3s;
}

.tag-active {
  background-color: #f0f9eb;
  border-color: #11ce66;
  color: #11ce66;
}

.tag-matched {
  opacity: 1;
}

.tag-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

:deep(.el-input__inner) {
  text-align: center;
}
</style>
