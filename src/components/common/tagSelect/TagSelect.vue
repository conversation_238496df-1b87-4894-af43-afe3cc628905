<template>
  <div class="tag-select form-control-height">
    <!-- 匹配模式切换 - 条件显示 -->
    <div v-if="showMatchMode" class="mode-switch">
      <el-radio-group v-model="matchMode" @change="handleModeChange">
        <el-radio-button :value="MatchMode.OR">任一</el-radio-button>
        <el-radio-button :value="MatchMode.AND">全部</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 标签选择 -->
    <div class="tag-selector">
      <el-select
        v-model="selectedTags"
        multiple
        collapse-tags
        collapse-tags-tooltip
        placeholder="请选择标签"
        :disabled="disabled"
        @change="handleSelectionChange"
      >
        <el-option v-for="tag in computedTagOptions" :key="tag.value" :label="tag.label" :value="tag.value" />
      </el-select>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { MatchMode } from './data.js'
import { dic_list } from '@/request/api'

// Props 定义
const props = defineProps({
  /** 当前选中的标签配置 - 支持 Object、Array 或 JSON 字符串格式 */
  modelValue: {
    type: [Object, Array, String],
    default: () => ({
      matchAllTagList: [],
      matchAnyTagList: [],
    }),
  },
  /** 是否使用 JSON 字符串格式 */
  useJsonFormat: {
    type: Boolean,
    default: false,
  },
  /** 自定义标签数据源 */
  tagOptions: {
    type: Array,
    default: () => [],
  },
  /** 是否禁用 */
  disabled: {
    type: Boolean,
    default: false,
  },
  /** 是否显示匹配模式切换 */
  showMatchMode: {
    type: Boolean,
    default: true,
  },
})

// 事件定义
const emit = defineEmits(['update:modelValue', 'mode-change', 'selection-change'])

// 内部状态
const matchMode = ref(MatchMode.OR)
const selectedTags = ref([])
const defaultTags = ref([])

const getDicList = async () => {
  const res = await dic_list({
    dicId: '6Kx0Y55Z50FDBJT5WQLeX',
  })
  defaultTags.value = res.list.map(item => ({
    label: item.dicName,
    value: item.dicId,
  }))
}
getDicList()

// 计算当前使用的标签选项
const computedTagOptions = computed(() => {
  return props.tagOptions.length > 0 ? props.tagOptions : defaultTags.value
})

// 计算是否为 AND 模式（用于向后兼容）
const isAndMode = computed(() => {
  return matchMode.value === MatchMode.AND
})

// 计算返回的数据结构 - 根据显示模式和格式要求决定数据格式
const resultData = computed(() => {
  let result

  if (!props.showMatchMode) {
    // 隐藏匹配模式时，直接返回标签数组
    result = [...selectedTags.value]
  } else {
    // 显示匹配模式时，返回对象格式
    const objResult = {
      matchAllTagList: [],
      matchAnyTagList: [],
    }

    if (matchMode.value === MatchMode.AND) {
      // AND模式：存储到 matchAllTagList
      objResult.matchAllTagList = [...selectedTags.value]
    } else {
      // OR模式：存储到 matchAnyTagList
      objResult.matchAnyTagList = [...selectedTags.value]
    }

    result = objResult
  }

  // 如果需要 JSON 字符串格式，则转换为字符串
  if (props.useJsonFormat) {
    return JSON.stringify(result)
  }

  return result
})

// 处理模式切换
const handleModeChange = value => {
  // 只有在显示匹配模式时才处理模式切换
  if (!props.showMatchMode) return

  matchMode.value = value
  emit('mode-change', {
    mode: value,
    isAndMode: value === MatchMode.AND,
  })

  // 模式切换时重新计算结果
  updateModelValue()
}

// 处理标签选择变化
const handleSelectionChange = value => {
  selectedTags.value = value

  // 根据显示模式发送不同格式的事件数据
  const eventData = props.showMatchMode
    ? {
        selectedTags: value,
        mode: matchMode.value,
        isAndMode: matchMode.value === MatchMode.AND,
      }
    : {
        selectedTags: value,
      }

  emit('selection-change', eventData)

  // 选择变化时重新计算结果
  updateModelValue()
}

const updateModelValue = () => {
  emit('update:modelValue', resultData.value)
}

// 解析输入数据 - 支持 JSON 字符串、对象和数组格式
const parseInputData = inputValue => {
  if (!inputValue) return null

  // 如果是字符串，尝试解析为 JSON
  if (typeof inputValue === 'string') {
    try {
      return JSON.parse(inputValue)
    } catch {
      // 解析失败时静默处理，返回空数组
      return []
    }
  }

  // 如果是数组或对象，直接返回
  return inputValue
}

// 初始化数据 - 支持多种数据格式
const initializeData = () => {
  if (!props.modelValue) return

  const parsedValue = parseInputData(props.modelValue)
  if (!parsedValue) return

  if (!props.showMatchMode) {
    // 隐藏匹配模式时，modelValue 是数组格式
    if (Array.isArray(parsedValue)) {
      selectedTags.value = [...parsedValue]
      matchMode.value = MatchMode.OR // 默认使用 OR 模式
    }
  } else {
    // 显示匹配模式时，modelValue 是对象格式
    if (typeof parsedValue === 'object' && !Array.isArray(parsedValue)) {
      const { matchAllTagList = [], matchAnyTagList = [] } = parsedValue

      if (matchAllTagList.length > 0) {
        // AND 模式的数据
        matchMode.value = MatchMode.AND
        selectedTags.value = [...matchAllTagList]
      } else if (matchAnyTagList.length > 0) {
        // OR 模式的数据
        matchMode.value = MatchMode.OR
        selectedTags.value = [...matchAnyTagList]
      }
    }
  }
}

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newValue => {
    if (newValue) {
      initializeData()
    }
  },
  { deep: true },
)

// 组件挂载时初始化
onMounted(() => {
  initializeData()
})

// 暴露给父组件的方法和属性
defineExpose({
  /** 当前匹配模式 */
  matchMode,
  /** 当前选中的标签 */
  selectedTags,
  /** 是否为 AND 模式 */
  isAndMode,
  /** 清空选择 */
  clearSelection: () => {
    selectedTags.value = []
    updateModelValue()
  },
})
</script>

<style scoped lang="less">
// 表单控件高度统一类
.form-control-height {
  min-height: 32px;
  display: flex;
  align-items: center;
}

.tag-select {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  height: 32px; // 与 Element Plus 默认表单元素高度一致
  line-height: 32px;

  .mode-switch {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    :deep(.el-radio-button__inner) {
      width: 50px;
      padding: 8px;
    }
  }

  .tag-selector {
    min-width: 100%;
  }
}

@media (max-width: 768px) {
  .tag-select {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
    height: auto;

    .mode-switch {
      height: 32px;
    }

    .tag-selector {
      min-width: auto;
      height: 32px;
    }
  }
}
</style>
