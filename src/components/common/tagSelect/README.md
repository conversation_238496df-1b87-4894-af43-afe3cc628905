# TagSelect 标签选择组件

基于 Element Plus 封装的标签选择组件，支持匹配模式切换和多标签选择功能。

## 基础用法

```vue
<template>
  <div>
    <TagSelect 
      v-model="tagSelection" 
      :tag-type="'poi'"
      @mode-change="handleModeChange"
      @selection-change="handleSelectionChange"
    />
    <p>当前选择: {{ tagSelection }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TagSelect from '@/components/common/TagSelect/TagSelect.vue'

const tagSelection = ref({
  matchAllTagList: [],
  matchAnyTagList: [],
})

const handleModeChange = ({ mode, isAndMode }) => {
  console.log('匹配模式变化:', mode, isAndMode)
}

const handleSelectionChange = ({ selectedTags, mode }) => {
  console.log('标签选择变化:', selectedTags, mode)
}
</script>
```

## 组件属性 (Props)

| 参数        | 说明           | 类型    | 可选值                    | 默认值     |
| ----------- | -------------- | ------- | ------------------------- | ---------- |
| modelValue  | 当前选中的标签配置 | Object/Array/String  | —                         | { matchAllTagList: [], matchAnyTagList: [] } |
| useJsonFormat | 是否使用JSON字符串格式 | Boolean | —                         | false |
| tagType     | 标签类型       | String  | —                         | 'poi'      |
| tagOptions  | 自定义标签数据源 | Array   | —                         | []         |
| disabled    | 是否禁用       | Boolean | —                         | false      |

## 组件事件 (Events)

| 事件名             | 说明                 | 回调参数                                    |
| ------------------ | -------------------- | ------------------------------------------- |
| update:modelValue  | 选择变化时触发       | (value: Object)                             |
| mode-change        | 匹配模式变化时触发   | ({ mode: String, isAndMode: Boolean })      |
| selection-change   | 标签选择变化时触发   | ({ selectedTags: Array, mode: String })     |

## 数据结构

### modelValue 数据结构
```javascript
{
  matchAllTagList: [],  // AND模式时的标签列表
  matchAnyTagList: [],  // OR模式时的标签列表
}
```

## 匹配模式说明

- **任一标签 (OR模式)**: 匹配任一选中标签即可，数据存储在 `matchAnyTagList`
- **全部标签 (AND模式)**: 必须匹配所有选中标签，数据存储在 `matchAllTagList`

## 使用示例

### 自定义标签数据

```vue
<template>
  <TagSelect 
    v-model="tags" 
    :tag-options="customTags"
  />
</template>

<script setup>
import { ref } from 'vue'

const tags = ref({
  matchAllTagList: [],
  matchAnyTagList: [],
})

const customTags = [
  { value: 'type1', label: '类型1' },
  { value: 'type2', label: '类型2' },
  { value: 'type3', label: '类型3' },
]
</script>
```

### 监听变化事件

```vue
<template>
  <TagSelect 
    v-model="tags" 
    @mode-change="handleModeChange"
    @selection-change="handleSelectionChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const tags = ref({
  matchAllTagList: [],
  matchAnyTagList: [],
})

const handleModeChange = ({ mode, isAndMode }) => {
  console.log(`匹配模式切换为: ${mode}`)
  if (isAndMode) {
    console.log('现在需要匹配所有选中的标签')
  } else {
    console.log('现在只需要匹配任一选中的标签')
  }
}

const handleSelectionChange = ({ selectedTags, mode }) => {
  console.log(`在${mode}模式下选中了标签:`, selectedTags)
}
</script>
```

### JSON 字符串格式用法

```vue
<template>
  <TagSelect
    v-model="tagJsonString"
    :show-match-mode="false"
    :use-json-format="true"
  />
</template>

<script setup>
import { ref } from 'vue'

// 数据将以 JSON 字符串格式存储，如: '["tag1","tag2"]'
const tagJsonString = ref('[]')
</script>
```

### 带匹配模式的 JSON 格式

```vue
<template>
  <TagSelect
    v-model="tagJsonString"
    :show-match-mode="true"
    :use-json-format="true"
  />
</template>

<script setup>
import { ref } from 'vue'

// 数据将以 JSON 字符串格式存储，如: '{"matchAllTagList":[],"matchAnyTagList":["tag1"]}'
const tagJsonString = ref('{"matchAllTagList":[],"matchAnyTagList":[]}')
</script>
```
