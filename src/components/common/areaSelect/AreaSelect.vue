<template>
  <el-select
    :model-value="modelValue"
    :placeholder="placeholder"
    :disabled="disabled"
    :clearable="clearable"
    :size="size"
    class="area-select"
    @update:model-value="handleChange"
    @change="handleSelectChange"
    @clear="handleClear"
  >
    <el-option v-for="option in districtOptions" :key="option.key" :label="option.label" :value="option.value" />
  </el-select>
</template>

<script setup>
import { computed } from 'vue'
import { districtOptions } from './data.js'

// Props 定义
const props = defineProps({
  /** 当前选中的区县代码 */
  modelValue: {
    type: Number,
    default: undefined,
  },
  /** 占位符文本 */
  placeholder: {
    type: String,
    default: '请选择区县',
  },
  /** 是否禁用 */
  disabled: {
    type: Boolean,
    default: false,
  },
  /** 是否可清空 */
  clearable: {
    type: Boolean,
    default: true,
  },
  /** 组件尺寸 */
  size: {
    type: String,
    default: 'default',
    validator: value => ['large', 'default', 'small'].includes(value),
  },
})

// 事件定义
const emit = defineEmits(['update:modelValue', 'change', 'clear'])

// 工具函数：根据区县代码获取区县信息
const getDistrictByCode = code => {
  return districtOptions.find(option => option.value === code)
}

// 当前选中的选项信息
const selectedOption = computed(() => {
  return props.modelValue ? getDistrictByCode(props.modelValue) : undefined
})

// 处理选择变化
const handleChange = value => {
  emit('update:modelValue', value)
}

// 处理选择变化（带完整选项信息）
const handleSelectChange = value => {
  const option = value ? getDistrictByCode(value) : undefined
  emit('change', option, value)
}

// 处理清空
const handleClear = () => {
  emit('clear')
}

// 暴露给父组件的方法和属性
defineExpose({
  /** 当前选中的选项信息 */
  selectedOption,
  /** 所有选项列表 */
  options: districtOptions,
  /** 根据code获取区县信息 */
  getDistrictByCode,
})
</script>

<style scoped lang="less">
.area-select {
  width: 100%;

  :deep(.el-select__wrapper) {
    width: 100%;
  }
}
</style>
