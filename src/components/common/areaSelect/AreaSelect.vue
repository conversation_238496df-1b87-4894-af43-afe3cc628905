<template>
  <el-select
    :model-value="modelValue"
    :placeholder="placeholder"
    :disabled="isComponentDisabled"
    :clearable="clearable"
    :size="size"
    :multiple="true"
    :collapse-tags="collapseTags"
    :collapse-tags-tooltip="collapseTagsTooltip"
    class="area-select"
    @update:model-value="handleChange"
    @change="handleSelectChange"
    @clear="handleClear"
  >
    <el-option v-for="option in cityOption" :key="option.value" :label="option.label" :value="option.value" />
  </el-select>
</template>

<script setup>
import { computed, watch } from 'vue'
import useOrg from '@/hooks/useOrg.js'

const { cityCode, cityOption } = useOrg()
// Props 定义
const props = defineProps({
  /** 当前选中的区县代码 (Array) */
  modelValue: {
    type: Array,
    default: () => [],
  },
  /** 占位符文本 */
  placeholder: {
    type: String,
    default: '请选择地区',
  },
  /** 是否禁用 */
  disabled: {
    type: Boolean,
    default: false,
  },
  /** 是否可清空 */
  clearable: {
    type: Boolean,
    default: true,
  },
  /** 组件尺寸 */
  size: {
    type: String,
    default: 'default',
    validator: value => ['large', 'default', 'small'].includes(value),
  },
  /** 多选时是否将选中值按文字的形式展示 */
  collapseTags: {
    type: Boolean,
    default: true,
  },
  /** 当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签 */
  collapseTagsTooltip: {
    type: Boolean,
    default: true,
  },
})

// 事件定义
const emit = defineEmits(['update:modelValue', 'change', 'clear'])

// 工具函数：根据区县代码获取区县信息
const getDistrictByCode = code => {
  return cityOption.value.find(option => option.value === code)
}

// 工具函数：根据区县代码数组获取区县信息数组
const getDistrictsByCode = codes => {
  if (!Array.isArray(codes)) return []
  return codes.map(code => getDistrictByCode(code)).filter(Boolean)
}

// 当前选中的选项信息
const selectedOption = computed(() => {
  return Array.isArray(props.modelValue) ? getDistrictsByCode(props.modelValue) : []
})

// 处理选择变化
const handleChange = value => {
  emit('update:modelValue', value)
}

// 处理选择变化（带完整选项信息）
const handleSelectChange = value => {
  const options = Array.isArray(value) ? getDistrictsByCode(value) : []
  emit('change', options, value)
}

/**
 * 禁用状态
 */
const isComponentDisabled = computed(() => {
  if (props.disabled) return true
  if (cityCode.value && cityCode.value !== '330600') {
    return true
  }
  return false
})

// 1. 监听组织上下文的变化（初始化 + 运行时）
watch(
  cityCode,
  newCityCode => {
    let newValue
    // 如果上下文变为区县，则强制设定值
    if (newCityCode && newCityCode !== '330600') {
      newValue = [newCityCode]
    } else {
      // 如果上下文变为空或市级，则清空
      newValue = []
    }
    if (JSON.stringify(props.modelValue) !== JSON.stringify(newValue)) {
      emit('update:modelValue', newValue)
      handleSelectChange(newValue)
    }
  },
  { immediate: true },
)

// 2. 监听父组件的修改意图，并进行“防守”
watch(
  () => props.modelValue,
  newModelValue => {
    const currentCityCode = cityCode.value
    // 当处于区县上下文中...
    if (currentCityCode && currentCityCode !== '330600') {
      const requiredValue = [currentCityCode]
      // ...如果父组件的赋值不是我们要求的值...
      if (JSON.stringify(newModelValue) !== JSON.stringify(requiredValue)) {
        // ...则“反抗”这次修改，把值改回去
        emit('update:modelValue', requiredValue)
      }
    }
  },
)

// 处理清空
const handleClear = () => {
  emit('clear')
}

// 暴露给父组件的方法和属性
defineExpose({
  /** 当前选中的选项信息 */
  selectedOption,
  /** 所有选项列表 */
  options: cityOption,
  /** 根据code获取区县信息 */
  getDistrictByCode,
  /** 根据code数组获取区县信息数组 */
  getDistrictsByCode,
})
</script>

<style scoped lang="less">
.area-select {
  width: 100%;

  :deep(.el-select__wrapper) {
    width: 100%;
  }
}
</style>