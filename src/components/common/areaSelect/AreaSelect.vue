<template>
  <el-select
    :model-value="modelValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :size="size"
    :multiple="true"
    :collapse-tags="collapseTags"
    :collapse-tags-tooltip="collapseTagsTooltip"
    class="area-select"
    @update:model-value="handleChange"
    @change="handleSelectChange"
    @clear="handleClear"
  >
    <el-option
      v-for="option in cityOption"
      :key="option.value"
      :label="option.label"
      :value="option.value"
      :disabled="isComponentDisabled"
    />
  </el-select>
</template>

<script setup>
import { computed, watch } from 'vue'
import useOrg from '@/hooks/useOrg.js'

const { cityCode, cityOption } = useOrg()
// Props 定义
const props = defineProps({
  /** 当前选中的区县代码 (Array) */
  modelValue: {
    type: Array,
    default: () => [],
  },
  /** 占位符文本 */
  placeholder: {
    type: String,
    default: '请选择地区',
  },
  /** 是否禁用 */
  disabled: {
    type: Boolean,
    default: false,
  },
  /** 是否可清空 */
  clearable: {
    type: Boolean,
    default: true,
  },
  /** 组件尺寸 */
  size: {
    type: String,
    default: 'default',
    validator: value => ['large', 'default', 'small'].includes(value),
  },
  /** 多选时是否将选中值按文字的形式展示 */
  collapseTags: {
    type: Boolean,
    default: true,
  },
  /** 当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签 */
  collapseTagsTooltip: {
    type: Boolean,
    default: true,
  },
})

// 事件定义
const emit = defineEmits(['update:modelValue', 'change', 'clear'])

// 工具函数：根据区县代码获取区县信息
const getDistrictByCode = code => {
  return cityOption.value.find(option => option.value === code)
}

// 工具函数：根据区县代码数组获取区县信息数组
const getDistrictsByCode = codes => {
  if (!Array.isArray(codes)) return []
  return codes.map(code => getDistrictByCode(code)).filter(Boolean)
}

// 当前选中的选项信息
const selectedOption = computed(() => {
  return Array.isArray(props.modelValue) ? getDistrictsByCode(props.modelValue) : []
})

// 处理选择变化
const handleChange = value => {
  emit('update:modelValue', value)
}

// 处理选择变化（带完整选项信息）
const handleSelectChange = value => {
  const options = Array.isArray(value) ? getDistrictsByCode(value) : []
  emit('change', options, value)
}

/**
 * 禁用状态
 */
const isComponentDisabled = computed(() => {
  if (props.disabled) return true
  if (cityCode.value && cityCode.value !== '330600') {
    return true
  }
  return false
})

// 监听 cityCode 的变化以更新组件状态
watch(
  cityCode,
  newCityCode => {
    //  cityCode 是一个具体的地区
    if (newCityCode && newCityCode !== '330600') {
      const isValidOption = cityOption.value.some(opt => opt.value === newCityCode)
      if (isValidOption) {
        const newValue = [newCityCode]
        // 仅当值发生变化时才更新，避免不必要的重渲染和无限循环
        if (JSON.stringify(props.modelValue) !== JSON.stringify(newValue)) {
          emit('update:modelValue', newValue)
          handleSelectChange(newValue) // 手动触发 change 事件以保持行为一致
        }
      } else {
        // 如果 cityCode 无效，则清空选项
        const newValue = []
        if (JSON.stringify(props.modelValue) !== JSON.stringify(newValue)) {
          emit('update:modelValue', newValue)
          handleSelectChange(newValue)
        }
      }
    }
    // cityCode 是 "330600" (绍兴市)，允许用户自由选择，不做任何操作
    // cityCode 为空或无效值，清空当前选择
    else if (!newCityCode) {
      const newValue = []
      if (JSON.stringify(props.modelValue) !== JSON.stringify(newValue)) {
        emit('update:modelValue', newValue)
        handleSelectChange(newValue)
      }
    }
  },
  { immediate: true },
)

// 处理清空
const handleClear = () => {
  emit('clear')
}

// 暴露给父组件的方法和属性
defineExpose({
  /** 当前选中的选项信息 */
  selectedOption,
  /** 所有选项列表 */
  options: cityOption,
  /** 根据code获取区县信息 */
  getDistrictByCode,
  /** 根据code数组获取区县信息数组 */
  getDistrictsByCode,
})
</script>

<style scoped lang="less">
.area-select {
  width: 100%;

  :deep(.el-select__wrapper) {
    width: 100%;
  }
}
</style>
