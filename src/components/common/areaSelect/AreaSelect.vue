<template>
  <el-select
    :model-value="modelValue"
    :placeholder="placeholder"
    :disabled="disabled"
    :clearable="clearable"
    :size="size"
    :multiple="multiple"
    :collapse-tags="multiple && collapseTags"
    :collapse-tags-tooltip="multiple && collapseTagsTooltip"
    class="area-select"
    @update:model-value="handleChange"
    @change="handleSelectChange"
    @clear="handleClear"
  >
    <el-option v-for="option in cityOption" :key="option.value" :label="option.label" :value="option.value" />
  </el-select>
</template>

<script setup>
import { computed } from 'vue'
import useOrg from '@/hooks/useOrg.js'

const { orgId, cityCode, cityOption } = useOrg()
// Props 定义
const props = defineProps({
  /** 当前选中的区县代码（单选时为Number，多选时为Array） */
  modelValue: {
    type: [Number, Array],
    default: undefined,
  },
  /** 占位符文本 */
  placeholder: {
    type: String,
    default: '请选择区县',
  },
  /** 是否禁用 */
  disabled: {
    type: Boolean,
    default: false,
  },
  /** 是否可清空 */
  clearable: {
    type: Boolean,
    default: true,
  },
  /** 组件尺寸 */
  size: {
    type: String,
    default: 'default',
    validator: value => ['large', 'default', 'small'].includes(value),
  },
  /** 是否多选 */
  multiple: {
    type: Boolean,
    default: true,
  },
  /** 多选时是否将选中值按文字的形式展示 */
  collapseTags: {
    type: Boolean,
    default: true,
  },
  /** 当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签 */
  collapseTagsTooltip: {
    type: Boolean,
    default: true,
  },
})

// 事件定义
const emit = defineEmits(['update:modelValue', 'change', 'clear'])

// 工具函数：根据区县代码获取区县信息
const getDistrictByCode = code => {
  return cityOption.value.find(option => option.value === code)
}

// 工具函数：根据区县代码数组获取区县信息数组
const getDistrictsByCode = codes => {
  if (!Array.isArray(codes)) return []
  return codes.map(code => getDistrictByCode(code)).filter(Boolean)
}

// 当前选中的选项信息
const selectedOption = computed(() => {
  if (props.multiple) {
    // 多选模式：返回选中选项数组
    return Array.isArray(props.modelValue) ? getDistrictsByCode(props.modelValue) : []
  } else {
    // 单选模式：返回单个选项
    return props.modelValue ? getDistrictByCode(props.modelValue) : undefined
  }
})

// 处理选择变化
const handleChange = value => {
  emit('update:modelValue', value)
}

// 处理选择变化（带完整选项信息）
const handleSelectChange = value => {
  if (props.multiple) {
    // 多选模式：传递选项数组和值数组
    const options = Array.isArray(value) ? getDistrictsByCode(value) : []
    emit('change', options, value)
  } else {
    // 单选模式：传递单个选项和值
    const option = value ? getDistrictByCode(value) : undefined
    emit('change', option, value)
  }
}

// 处理清空
const handleClear = () => {
  emit('clear')
}

// 暴露给父组件的方法和属性
defineExpose({
  /** 当前选中的选项信息 */
  selectedOption,
  /** 所有选项列表 */
  options: cityOption,
  /** 根据code获取区县信息 */
  getDistrictByCode,
  /** 根据code数组获取区县信息数组 */
  getDistrictsByCode,
})
</script>

<style scoped lang="less">
.area-select {
  width: 100%;

  :deep(.el-select__wrapper) {
    width: 100%;
  }
}
</style>
