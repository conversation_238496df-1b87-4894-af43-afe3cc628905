# AreaSelect 区县选择组件

基于 Element Plus 封装的绍兴市区县选择下拉组件，支持 v-model 双向绑定。

## 基础用法

```vue
<template>
  <div>
    <AreaSelect v-model="selectedDistrict" @change="handleDistrictChange" />
    <p>选中的区县代码: {{ selectedDistrict }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AreaSelect from '@/components/common/AreaSelect/AreaSelect.vue'

const selectedDistrict = ref()

const handleDistrictChange = (option, value) => {
  console.log('选中的区县:', option)
  console.log('区县代码:', value)
}
</script>
```


## 监听变化事件

```vue
<template>
  <AreaSelect 
    v-model="district" 
    @change="handleChange"
    @clear="handleClear"
  />
</template>

<script setup>
import { ref } from 'vue'

const district = ref()

const handleChange = (option, value) => {
  if (option) {
    console.log(`选中了: ${option.label} (${option.value})`)
  }
}

const handleClear = () => {
  console.log('已清空选择')
}
</script>
```









