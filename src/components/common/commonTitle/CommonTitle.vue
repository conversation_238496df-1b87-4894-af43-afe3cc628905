<template>
  <div class="title">
    <div class="label">
      <EIcon v-if="isBack" name="Back" style="height: 31px" @click="back" />
      <span class="name">{{ name }}</span>
      <el-tag v-if="tagInfo?.text" style="margin-left: 8px" :type="tagInfo.type">{{ tagInfo.text }}</el-tag>
    </div>
    <slot></slot>
  </div>
</template>

<script setup>
import useComponent from '@/hooks/useComponent.js'
const { setPath, setPathBack, pathData } = useComponent()

const emit = defineEmits(['beforeBack'])
const back = () => {
  emit('beforeBack')
  if (backPath) setPath?.(backPath)
  else setPathBack?.()
}
const {
  title = '',
  isBack = false,
  tagInfo = {},
  backPath = null,
} = defineProps({
  title: {
    type: String,
  },
  isBack: {
    type: Boolean,
  },
  tagInfo: {
    type: Object,
  },
  backPath: {
    type: String,
  },
})
const name = computed(() => title || pathData.value?.name)
</script>

<style scoped lang="less">
.title {
  width: 100%;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  font-size: 24px;
  justify-content: space-between;
  color: rgba(0, 0, 0, 0.9);
  .label {
    display: flex;
    align-items: center;
    .name {
      font-weight: 700;
      font-size: 24px;
    }
    .el-tag {
      --el-tag-font-size: 15px;
    }
  }
}
:deep(.el-icon svg path) {
  stroke-width: 50px;
  stroke: black;
  fill: black;
}
</style>
