<template>
  <div class="common-upload-container">
    <el-upload
      v-model:file-list="fileList"
      :action="''"
      :http-request="handleHttpRequest"
      :before-upload="handleBeforeUpload"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :on-exceed="handleExceed"
      :multiple="multiple"
      :limit="limit"
      :disabled="disabled"
      :accept="accept"
    >
      <el-button :disabled="disabled" type="primary">点击上传</el-button>
      <template #tip>
        <div class="el-upload__tip">
          <p v-if="accept">支持的文件类型：{{ accept }}</p>
          <!-- <p v-if="maxSize">单个文件大小不超过 {{ maxSize }}MB</p>
          <p v-if="multiple && maxCount">最多上传 {{ maxCount }} 个文件</p> -->
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { uploadFile, downloadFile, fileList as getFileListByIds } from '@/request/api/system'
import { saveAs } from 'file-saver'

const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  accept: {
    type: String,
    default: '',
  },
  maxSize: {
    type: Number,
    default: 10, // 单位 MB
  },
  maxCount: {
    type: Number,
    default: 5,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error', 'file-remove'])

const fileList = ref([])
const isInternalUpdate = ref(false)

const limit = computed(() => (props.multiple ? props.maxCount : 1))

// 将 modelValue (fileIds) 同步到内部 fileList
watch(
  () => props.modelValue,
  async newVal => {
    if (isInternalUpdate.value) {
      isInternalUpdate.value = false
      return
    }

    const newFileIds = Array.isArray(newVal) ? newVal.filter(Boolean) : newVal ? [newVal] : []
    if (newFileIds.length === 0) {
      fileList.value = []
      return
    }

    try {
      const res = await getFileListByIds({ idList: JSON.stringify(newFileIds) })
      fileList.value = res.list.map(fileInfo => ({
        name: fileInfo.fileName,
        url: '#',
        uid: Date.now() + Math.random(),
        fileId: fileInfo.id,
        status: 'success',
      }))
    } catch (error) {
      fileList.value = []
    }
  },
  { immediate: true, deep: true },
)

// 自定义上传处理
const handleHttpRequest = async options => {
  const { file, onProgress, onSuccess, onError } = options
  const formData = new FormData()
  formData.append('fileName', file.name)
  formData.append('fileSize', file.size)
  formData.append('file', file)

  try {
    const res = await uploadFile(formData, progressEvent => {
      const percent = progressEvent.lengthComputable ? (progressEvent.loaded / progressEvent.total) * 100 : 0
      onProgress({ percent })
    })

    const fileIndex = fileList.value.findIndex(item => item.uid === file.uid)
    if (fileIndex > -1) {
      fileList.value[fileIndex].fileId = res.fileId
      fileList.value[fileIndex].status = 'success'
      fileList.value[fileIndex].name = file.name
    }

    isInternalUpdate.value = true
    const newModelValue = props.multiple
      ? fileList.value.map(f => f.fileId).filter(Boolean)
      : fileList.value[0]?.fileId || ''
    emit('update:modelValue', newModelValue)
    emit('upload-success', { file: fileList.value[fileIndex], response: res })
    onSuccess(res)
  } catch (error) {
    ElMessage.error('上传失败')
    emit('upload-error', { file, error })
    onError(error)
    const failedFileIndex = fileList.value.findIndex(f => f.uid === file.uid)
    if (failedFileIndex > -1) {
      fileList.value.splice(failedFileIndex, 1)
    }
  }
}

// 上传前校验
const handleBeforeUpload = file => {
  const isTypeValid = props.accept ? props.accept.split(',').some(type => file.name.endsWith(type.trim())) : true
  const isSizeValid = file.size / 1024 / 1024 < props.maxSize

  if (!isTypeValid) {
    ElMessage.error(`文件类型不正确！请上传 ${props.accept} 格式的文件。`)
    return false
  }
  if (!isSizeValid) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB！`)
    return false
  }
  return true
}

// 文件移除
const handleRemove = (file, uploadedFiles) => {
  fileList.value = uploadedFiles
  isInternalUpdate.value = true
  const newModelValue = props.multiple ? fileList.value.map(f => f.fileId).filter(Boolean) : ''
  emit('update:modelValue', newModelValue)
  emit('file-remove', { fileId: file.fileId || file.uid })
}

// 文件预览（实现为下载）
const handlePreview = async file => {
  if (!file.fileId) {
    ElMessage.warning('文件尚未上传成功，无法下载')
    return
  }
  try {
    downloadFile({ id: file.fileId }).then(res => {
      saveAs(res, file.name) // 自动处理下载
    })
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

// 文件超出限制
const handleExceed = () => {
  ElMessage.warning(`最多只能上传 ${limit.value} 个文件。`)
}
</script>

<style scoped>
.common-upload-container {
  width: 100%;
}
.el-upload__tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>
