<template>
  <div class="common-upload-container">
    <el-upload
      v-model:file-list="fileList"
      :action="''"
      :http-request="handleHttpRequest"
      :before-upload="handleBeforeUpload"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :on-exceed="handleExceed"
      :multiple="multiple"
      :limit="limit"
      :disabled="disabled"
      :accept="accept"
    >
      <el-button :disabled="disabled" type="primary">点击上传</el-button>
      <template #tip>
        <div class="el-upload__tip">
          <p v-if="accept">支持的文件类型：{{ accept }}</p>
          <!-- <p v-if="maxSize">单个文件大小不超过 {{ maxSize }}MB</p>
          <p v-if="multiple && maxCount">最多上传 {{ maxCount }} 个文件</p> -->
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { uploadFile, downloadFile } from '@/request/api/system'

const props = defineProps({
  modelValue: {
    type: [String, Array],
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  accept: {
    type: String,
    default: '',
  },
  maxSize: {
    type: Number,
    default: 10, // 单位 MB
  },
  maxCount: {
    type: Number,
    default: 5,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error', 'file-remove'])

const fileList = ref([])

const limit = computed(() => (props.multiple ? props.maxCount : 1))

// 将 modelValue (IDs) 同步到内部 fileList
watch(
  () => props.modelValue,
  newVal => {
    const currentIds = fileList.value.map(f => f.uid)
    const newIds = Array.isArray(newVal) ? newVal : newVal ? [newVal] : []

    if (JSON.stringify(currentIds) === JSON.stringify(newIds)) {
      return
    }

    fileList.value = newIds.map(id => ({
      name: id, // 理想情况下，这里应该是真实文件名
      url: '#', // 理想情况下，这里应该是可访问的URL
      uid: id,
      status: 'success',
    }))
  },
  { immediate: true, deep: true },
)

// 自定义上传处理
const handleHttpRequest = async options => {
  const { file, onProgress, onSuccess, onError } = options
  const formData = new FormData()
  formData.append('fileName', file.name)
  formData.append('fileSize', file.size)
  formData.append('file', file)

  try {
    const res = await uploadFile(formData, progressEvent => {
      const percent = progressEvent.lengthComputable ? (progressEvent.loaded / progressEvent.total) * 100 : 0
      onProgress({ percent })
    })

    const uploadedFile = {
      name: file.name,
      url: '#',
      uid: res.fileId,
      status: 'success',
    }

    if (props.multiple) {
      fileList.value.push(uploadedFile)
    } else {
      fileList.value = [uploadedFile]
    }

    const newModelValue = props.multiple ? fileList.value.map(f => f.uid) : fileList.value[0]?.uid || ''
    emit('update:modelValue', newModelValue)
    emit('upload-success', { file: uploadedFile, response: res })
    onSuccess(res)
  } catch (error) {
    emit('upload-error', { file, error })
    onError(error)
    const failedFileIndex = fileList.value.findIndex(f => f.uid === file.uid)
    if (failedFileIndex > -1) {
      fileList.value.splice(failedFileIndex, 1)
    }
  }
}

// 上传前校验
const handleBeforeUpload = file => {
  const isTypeValid = props.accept ? props.accept.split(',').some(type => file.name.endsWith(type.trim())) : true
  const isSizeValid = file.size / 1024 / 1024 < props.maxSize

  if (!isTypeValid) {
    ElMessage.error(`文件类型不正确！请上传 ${props.accept} 格式的文件。`)
    return false
  }
  if (!isSizeValid) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB！`)
    return false
  }
  return true
}

// 文件移除
const handleRemove = (file, uploadedFiles) => {
  const removedFileId = file.uid
  fileList.value = uploadedFiles

  const newModelValue = props.multiple ? fileList.value.map(f => f.uid) : ''
  emit('update:modelValue', newModelValue)
  emit('file-remove', { fileId: removedFileId })
}

// 文件预览（实现为下载）
const handlePreview = async file => {
  try {
    const res = await downloadFile({ fileId: file.fileId })
    const blob = new Blob([res], { type: 'application/octet-stream' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = file.name
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

// 文件超出限制
const handleExceed = () => {
  ElMessage.warning(`最多只能上传 ${limit.value} 个文件。`)
}
</script>

<style scoped>
.common-upload-container {
  width: 100%;
}
.el-upload__tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
}
</style>
