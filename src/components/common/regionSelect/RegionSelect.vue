<template>
  <!-- Wrapper div to ensure component has a single root for better compatibility -->
  <div class="region-select-wrapper">
    <el-form-item label="所属地区" :prop="propPrefix ? `${propPrefix}.regionCodes` : 'regionCodes'">
      <el-select
        v-model="localValue.regionCodes"
        placeholder="请选择所属地区"
        style="width: 100%"
        @change="onRegionChange"
      >
        <el-option
          v-for="item in areaOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :disabled="isAreaOptionDisabled(item)"
        />
      </el-select>
    </el-form-item>

    <el-form-item v-if="showRegionType" label="区域类型" :prop="propPrefix ? `${propPrefix}.regionType` : 'regionType'">
      <el-select v-model="localValue.regionType" placeholder="请选择区域类型" style="width: 100%">
        <el-option v-for="item in regionTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
  </div>
</template>

<script setup>
import { reactive, watch, computed } from 'vue'
import { useDictionary } from '@/hooks/useDictionary.js'
import useOrg from '@/hooks/useOrg.js'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({ regionCodes: '', regionType: '' }),
  },
  propPrefix: {
    type: String,
    default: '',
  },
  showRegionType: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:modelValue'])

const localValue = reactive({
  regionCodes: '',
  regionType: '',
})

watch(
  () => props.modelValue,
  newValue => {
    if (newValue) {
      localValue.regionCodes = newValue.regionCodes || ''
      localValue.regionType = newValue.regionType || ''
    }
  },
  { immediate: true, deep: true },
)

watch(
  localValue,
  newValue => {
    emit('update:modelValue', { ...newValue })
  },
  { deep: true },
)

watch(
  () => props.showRegionType,
  isVisible => {
    if (!isVisible) {
      localValue.regionType = ''
    }
  },
)

const { orgId, cityName } = useOrg()
const areaOptions = useDictionary('所属地区')

const regionTypeOptions = computed(() => {
  if (localValue.regionCodes === '330600') {
    return [
      { label: '市', value: 'CITY' },
      { label: '跨市', value: 'CORSS_CITY' },
    ]
  } else if (localValue.regionCodes) {
    return [
      { label: '区', value: 'DISTRICT' },
      { label: '跨区', value: 'CORSS_DISTRICT' },
    ]
  }
  return []
})

const isAreaOptionDisabled = option => {
  if (orgId.value === 'cb8upAzuM07uQyNpzuj05') {
    return false
  }
  return option.label !== cityName.value
}

const onRegionChange = () => {
  localValue.regionType = ''
}

watch(
  () => orgId.value,
  (newOrgId, oldOrgId) => {
    if (newOrgId !== oldOrgId) {
      localValue.regionType = ''
    }
  },
)

watch(
  [areaOptions, cityName],
  ([options, currentCityName]) => {
    if (!props.modelValue.regionCodes && currentCityName && options?.length > 0) {
      const matchedOption = options.find(option => option.label === currentCityName)
      if (matchedOption) {
        localValue.regionCodes = matchedOption.value
      }
    }
  },
  { immediate: true },
)
</script>

<style scoped lang="less">
.region-select-wrapper {
  width: 100%;
}
</style>
