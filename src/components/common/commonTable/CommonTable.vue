<template>
  <div ref="tableContainer" class="common-table">
    <!-- 工具栏 -->
    <div v-if="showToolbar" ref="toolbarRef" class="table-toolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left" :selected-rows="selectedRows" />
      </div>
      <div class="toolbar-right">
        <slot name="toolbar-right" :selected-rows="selectedRows" />
        <el-button v-if="showRefresh" type="primary" :size="size" @click="handleRefresh">刷新</el-button>
      </div>
    </div>

    <!-- 表格主体 -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="data"
      :height="tableHeight"
      v-bind="tableProps"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
    >
      <!-- 选择列 -->
      <el-table-column v-if="showSelection" type="selection" width="55" :selectable="getRowSelectable" />

      <!-- 展开列 -->
      <el-table-column v-if="showExpand" type="expand" width="50">
        <template #default="{ row }">
          <div class="expand-content">
            <el-table
              :data="row[expandField] || []"
              :show-header="expandColumns.length > 0"
              class="expand-table"
              size="small"
            >
              <!-- 展开表格的列 -->
              <template v-if="expandColumns.length > 0">
                <el-table-column
                  v-for="(column, index) in expandColumns"
                  :key="column.prop || index"
                  :prop="column.prop"
                  :label="column.label"
                  :width="column.width"
                  :min-width="column.minWidth"
                  :align="column.align || 'left'"
                  v-bind="column.props"
                >
                  <template v-if="column.type === 'slot'" #default="scope">
                    <slot
                      :name="column.slotName"
                      :row="scope.row"
                      :column="column"
                      :index="scope.$index"
                      :parent-row="row"
                    />
                  </template>
                  <template v-else-if="column.type === 'tag'" #default="scope">
                    <el-tag :type="getTagType(scope.row, column)">
                      {{ getTagText(scope.row, column) }}
                    </el-tag>
                  </template>
                  <template v-else-if="column.type === 'action'" #default="scope">
                    <div class="table-action">
                      <el-button
                        v-for="(action, actionIndex) in getVisibleActions(scope.row, column.actions)"
                        :key="actionIndex"
                        :type="action.type || 'primary'"
                        :size="action.size || 'small'"
                        :disabled="getActionDisabled(scope.row, action)"
                        @click="handleExpandActionClick(action, scope.row, scope.$index, row)"
                      >
                        {{ action.label }}
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </template>
              <!-- 如果没有配置列，显示默认的子数据 -->
              <template v-else>
                <el-table-column label="子项信息" prop="name" />
              </template>
            </el-table>
          </div>
        </template>
      </el-table-column>

      <!-- 序号列 -->
      <el-table-column v-if="showIndex" type="index" label="序号" width="50" />

      <!-- 数据列 -->
      <template v-for="(column, index) in visibleColumns" :key="column.prop || index">
        <el-table-column
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :sortable="column.sortable"
          :align="column.align || 'left'"
          v-bind="column.props"
        >
          <template v-if="column.type === 'slot'" #default="scope">
            <slot :name="column.slotName" :row="scope.row" :column="column" :index="scope.$index" />
          </template>
          <template v-else-if="column.type === 'tag'" #default="scope">
            <el-tag :type="getTagType(scope.row, column)">
              {{ getTagText(scope.row, column) }}
            </el-tag>
          </template>
          <template v-else-if="column.type === 'action'" #default="scope">
            <div class="table-action">
              <el-button
                v-for="(action, actionIndex) in getVisibleActions(scope.row, column.actions)"
                :key="actionIndex"
                :type="action.type || 'primary'"
                :size="action.size || 'small'"
                :disabled="getActionDisabled(scope.row, action)"
                @click="handleActionClick(action, scope.row, scope.$index)"
              >
                {{ action.label }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页组件 -->
    <div v-if="showPagination" ref="paginationRef" class="table-pagination">
      <Pagination
        v-model:page="currentPage"
        v-model:limit="currentPageSize"
        :total="total"
        :page-sizes="pageSizes"
        @pagination="handlePaginationChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import Pagination from '../pagination/Pagination.vue'

// 组件属性定义
const props = defineProps({
  // 表格数据
  data: {
    type: Array,
    default: () => [],
  },
  // 列配置
  columns: {
    type: Array,
    default: () => [],
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false,
  },
  // 表格尺寸
  size: {
    type: String,
    default: 'default',
  },
  // 显示选择列
  showSelection: {
    type: Boolean,
    default: false,
  },
  // 行选择控制函数
  selectionControl: {
    type: Function,
    default: null,
  },
  // 显示序号列
  showIndex: {
    type: Boolean,
    default: false,
  },
  // 显示展开列
  showExpand: {
    type: Boolean,
    default: false,
  },
  // 展开行的字段名
  expandField: {
    type: String,
    default: 'children',
  },
  // 展开行的列配置
  expandColumns: {
    type: Array,
    default: () => [],
  },
  // 显示工具栏
  showToolbar: {
    type: Boolean,
    default: true,
  },
  // 显示刷新按钮
  showRefresh: {
    type: Boolean,
    default: false,
  },
  // 显示分页
  showPagination: {
    type: Boolean,
    default: true,
  },
  // 当前页码
  page: {
    type: Number,
    default: 1,
  },
  // 每页条数
  pageSize: {
    type: Number,
    default: 10,
  },
  // 总条数
  total: {
    type: Number,
    default: 0,
  },
  // 每页条数选项
  pageSizes: {
    type: Array,
    default: () => [10, 20, 30, 50, 100],
  },
  // Element Plus Table 组件的其他属性
  tableProps: {
    type: Object,
    default: () => ({
      stripe: true,
      // border: true,
    }),
  },
  // 减去的高度值（用于计算表格高度）
  subtractHeight: {
    type: Number,
    default: 30, // 默认底部预留30px
  },
  // 是否启用自适应高度
  autoHeight: {
    type: Boolean,
    default: true,
  },
  // 表格行高
  rowHeight: {
    type: [Number, String],
    default: undefined,
  },
  observeElementSelector: {
    type: String,
    default: '.common-query',
  },
})

// 事件定义
const emit = defineEmits([
  'update:page',
  'update:pageSize',
  'pagination-change',
  'selection-change',
  'row-click',
  'sort-change',
  'refresh',
  'action-click',
  'expand-action-click',
])

// 表格引用
const tableRef = ref()
const tableContainer = ref()
const toolbarRef = ref()
const paginationRef = ref()
const resizeObserver = ref(null)

// 自适应高度
const tableHeight = ref(null)

// 当前页码
const currentPage = computed({
  get: () => props.page,
  set: val => emit('update:page', val),
})

// 每页条数
const currentPageSize = computed({
  get: () => props.pageSize,
  set: val => emit('update:pageSize', val),
})

// 可见列
const visibleColumns = computed(() => {
  return props.columns.filter(column => !column.hidden)
})

// 选中的行数据
const selectedRows = ref([])

// 行样式计算
// const rowStyle = computed(() =>  ({
//   height: '60px',
//   lineHeight: '60px',
// }))

// 获取字段值
const getFieldValue = (row, prop) => {
  if (!prop) return ''
  return prop.split('.').reduce((obj, key) => obj?.[key], row) || ''
}

// 获取标签类型
const getTagType = (row, column) => {
  if (typeof column.tagType === 'function') {
    return column.tagType(row, column)
  }
  if (column.tagTypeMap) {
    const value = getFieldValue(row, column.prop)
    return column.tagTypeMap[value] || 'primary'
  }
  return column.tagType || 'primary'
}

// 获取标签文本
const getTagText = (row, column) => {
  if (typeof column.tagText === 'function') {
    return column.tagText(row, column)
  }
  if (column.tagTextMap) {
    const value = getFieldValue(row, column.prop)
    return column.tagTextMap[value] || value
  }
  return getFieldValue(row, column.prop)
}

// 获取可见的操作按钮
const getVisibleActions = (row, actions) => {
  if (!actions) return []
  return actions.filter(action => {
    if (typeof action.visible === 'function') {
      return action.visible(row, action)
    }
    return action.visible !== false
  })
}

// 获取行是否可选择
const getRowSelectable = (row, index) => {
  if (typeof props.selectionControl === 'function') {
    return props.selectionControl(row, index)
  }
  return true // 默认都可选择
}

// 获取操作按钮的禁用状态
const getActionDisabled = (row, action) => {
  if (typeof action.disabled === 'function') {
    return action.disabled(row, action)
  }
  return action.disabled || false
}

// 表格事件处理
const handleSelectionChange = selection => {
  selectedRows.value = selection
  emit('selection-change', selection)
}

const handleRowClick = (row, column, event) => {
  emit('row-click', row, column, event)
}

const handleSortChange = ({ column, prop, order }) => {
  emit('sort-change', { column, prop, order })
}

// 分页事件处理
const handlePaginationChange = () => {
  emit('pagination-change', {
    page: currentPage.value,
    pageSize: currentPageSize.value,
  })
}

// 刷新事件处理
const handleRefresh = () => {
  emit('refresh')
}

// 操作按钮事件处理
const handleActionClick = (action, row, index) => {
  if (action.click) {
    action.click(row, action, index)
  }
  emit('action-click', { action, row, index })
}

// 展开行操作按钮事件处理
const handleExpandActionClick = (action, row, index, parentRow) => {
  if (action.click) {
    action.click(row, action, index, parentRow)
  }
  emit('expand-action-click', { action, row, index, parentRow })
}

// 计算表格高度
const calculateTableHeight = () => {
  // 如果未启用自适应高度，直接返回
  if (!props.autoHeight || !tableContainer.value) return

  nextTick(() => {
    const containerRect = tableContainer.value.getBoundingClientRect()

    // 简化计算：视窗高度 - 表格容器顶部位置 - 外部传入的减去高度
    let calculatedHeight = window.innerHeight - containerRect.top - props.subtractHeight
    // 减去工具栏高度（如果显示）
    if (props.showToolbar && toolbarRef.value) {
      calculatedHeight -= toolbarRef.value.offsetHeight + 16 // 16px margin
    }

    // 减去分页器高度（如果显示）
    if (props.showPagination && paginationRef.value) {
      calculatedHeight -= paginationRef.value.offsetHeight + 16 // 16px margin
    }
    // 设置最小高度
    tableHeight.value = Math.max(calculatedHeight, 220)
  })
}

// 窗口大小变化监听
const handleResize = () => {
  calculateTableHeight()
}

// 监听 subtractHeight 变化，重新计算表格高度
watch(
  () => props.subtractHeight,
  () => {
    if (props.autoHeight) {
      calculateTableHeight()
    }
  },
)

// 监听 autoHeight 变化
watch(
  () => props.autoHeight,
  newVal => {
    if (newVal) {
      calculateTableHeight()
    } else {
      tableHeight.value = null
    }
  },
)

// 组件挂载
onMounted(() => {
  setTimeout(calculateTableHeight, 100)
  window.addEventListener('resize', handleResize)

  if (props.observeElementSelector) {
    const targetNode = document.querySelector(props.observeElementSelector)
    if (targetNode) {
      resizeObserver.value = new ResizeObserver(() => {
        calculateTableHeight()
      })
      resizeObserver.value.observe(targetNode)
    }
  }
})

// 组件卸载
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  }
})

// 清空选择
const clearSelection = () => {
  tableRef.value?.clearSelection()
}

// 暴露方法给父组件
defineExpose({
  clearSelection,
  tableRef,
  selectedRows,
  calculateTableHeight,
})
</script>

<style lang="less" scoped>
.common-table {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 24px 24px 0;
  box-shadow: 0 8px 20px 0 rgba(0, 0, 0, 0.02);
}
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.toolbar-left {
  flex: 1;
}

.toolbar-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

.table-action {
  display: flex;
  gap: 8px;
}

.table-pagination {
  height: 50px !important;
  display: flex;
  justify-content: flex-end;
}

/* 展开行样式 */
.expand-content {
  padding: 0 20px 10px 20px;
  // background-color: #f1f3ff;
}

.expand-table {
  background-color: transparent;
}
:deep(.el-table__row) {
  height: 60px;
}

:deep(.expand-table .el-table__header) {
  background-color: #f1f3ff !important;
}

:deep(.expand-table .el-table__row) {
  background-color: #f1f3ff;
}

:deep(.expand-table .el-table__row:hover > td) {
  background-color: #e8ecff !important;
}
:deep(.el-button) {
  border: none;
}
</style>
