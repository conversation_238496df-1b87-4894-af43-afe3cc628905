# CommonTable 公共表格组件

基于 Element Plus 封装的通用表格组件，支持分页、排序、操作列等功能。

## 基础用法

```vue
<template>
  <CommonTable
    :data="tableData"
    :columns="columns"
    :total="total"
    v-model:page="page"
    v-model:page-size="pageSize"
    @pagination-change="handlePaginationChange"
    @action-click="handleActionClick"
  />
</template>

<script setup>
import { ref } from 'vue'

const tableData = ref([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    status: 1,
    createTime: '2023-01-01',
  },
])

const columns = ref([
  { prop: 'name', label: '姓名', sortable: true },
  { prop: 'email', label: '邮箱' },
  {
    prop: 'status',
    label: '状态',
    type: 'tag',
    tagTypeMap: {
      1: 'success',
      0: 'danger',
    },
    tagTextMap: {
      1: '启用',
      0: '禁用',
    },
  },
  {
    label: '操作',
    type: 'action',
    actions: [
      {
        label: '编辑',
        type: 'primary',
        code: 'edit',
      },
      {
        label: '删除',
        type: 'danger',
        code: 'delete',
      },
    ],
  },
])

const total = ref(100)
const page = ref(1)
const pageSize = ref(10)

const handlePaginationChange = ({ page: currentPage, pageSize: currentPageSize }) => {
  page.value = currentPage
  pageSize.value = currentPageSize
  // 重新加载数据
}

const handleActionClick = ({ action, row, index }) => {
  console.log('操作点击:', action.code, row)
}
</script>
```

## 列配置类型

### 普通列

```js
{
  prop: 'name',
  label: '姓名',
  width: 120,
  sortable: true,
  align: 'center'
}
```

### 标签列

```js
{
  prop: 'status',
  label: '状态',
  type: 'tag',
  tagTypeMap: { 1: 'success', 0: 'danger' },
  tagTextMap: { 1: '启用', 0: '禁用' }
}
```

### 操作列

```js
{
  label: '操作',
  type: 'action',
  actions: [
    {
      label: '编辑',
      type: 'primary',
      size: 'small',
      code: 'edit'
    },
    {
      label: '删除',
      type: 'danger',
      size: 'small',
      code: 'delete',
      visible: (row) => row.status === 1 // 动态显示
    }
  ]
}
```

### 自定义插槽列

```js
{
  prop: 'avatar',
  label: '头像',
  type: 'slot',
  slotName: 'avatar'
}
```

## 工具栏插槽

```vue
<template>
  <CommonTable :data="tableData" :columns="columns" :show-selection="true">
    <template #toolbar-left="{ selectedRows }">
      <el-button v-if="selectedRows.length > 0" type="warning" @click="handleBatchUpdate">
        批量更新 ({{ selectedRows.length }})
      </el-button>
    </template>

    <template #toolbar-right>
      <el-button type="primary" @click="handleAdd"> 新增 </el-button>
    </template>
  </CommonTable>
</template>
```

## Props

| 参数           | 说明                    | 类型    | 默认值                         |
| -------------- | ----------------------- | ------- | ------------------------------ |
| data           | 表格数据                | Array   | []                             |
| columns        | 列配置                  | Array   | []                             |
| loading        | 加载状态                | Boolean | false                          |
| size           | 尺寸                    | String  | 'default'                      |
| showSelection  | 显示选择列              | Boolean | false                          |
| showIndex      | 显示序号列              | Boolean | false                          |
| showToolbar    | 显示工具栏              | Boolean | true                           |
| showRefresh    | 显示刷新按钮            | Boolean | true                           |
| showPagination | 显示分页                | Boolean | true                           |
| page           | 当前页码                | Number  | 1                              |
| pageSize       | 每页条数                | Number  | 10                             |
| total          | 总条数                  | Number  | 0                              |
| pageSizes      | 每页条数选项            | Array   | [10, 20, 30, 50, 100]          |
| tableProps     | Element Plus Table 属性 | Object  | { stripe: true, border: true } |
| rowHeight      | 表格行高                | Number/String | undefined                    |

## 行高配置

支持自定义表格行高，提供更好的视觉效果和数据密度控制：

```vue
<template>
  <!-- 使用数字（像素值） -->
  <CommonTable
    :data="tableData"
    :columns="columns"
    :row-height="60"
  />

  <!-- 使用字符串（带单位） -->
  <CommonTable
    :data="tableData"
    :columns="columns"
    row-height="60px"
  />

</template>
```


## 展开行功能

支持展开行显示子数据，适用于主从表格结构：

```vue
<template>
  <CommonTable
    :data="tableData"
    :columns="columns"
    :show-expand="true"
    expand-field="batchList"
    :expand-columns="expandColumns"
    @expand-action-click="handleExpandActionClick"
  />
</template>

<script setup>
// 主表格数据（每条记录包含子数据）
const tableData = ref([
  {
    id: 1,
    name: '培训计划A',
    batchList: [
      { id: 101, name: '批次1', status: '进行中' },
      { id: 102, name: '批次2', status: '未开始' },
    ],
  },
])

// 展开行列配置
const expandColumns = ref([
  { prop: 'name', label: '批次名称', minWidth: 150 },
  { prop: 'status', label: '状态', width: 100, type: 'tag' },
  {
    label: '操作',
    type: 'action',
    width: 150,
    actions: [
      { label: '编辑', type: 'warning', code: 'edit', size: 'small' },
      { label: '删除', type: 'danger', code: 'delete', size: 'small' },
    ],
  },
])

// 展开行操作处理
const handleExpandActionClick = ({ action, row, index, parentRow }) => {
  console.log('展开行操作:', action.code, row, '父行:', parentRow)
}
</script>
```

## 高级用法

### 使用 Element Plus 原生属性

```js
const tableProps = {
  height: 400,
  stripe: false,
  border: false,
  emptyText: '暂无数据',
}

const columns = [
  {
    prop: 'name',
    label: '姓名',
    props: {
      // Element Plus TableColumn 的所有属性
      showOverflowTooltip: true,
      resizable: true,
      sortable: 'custom',
    },
  },
]
```

### 动态控制操作按钮

```js
const getActions = row => {
  const actions = [
    { label: '查看', type: 'primary', code: 'view' },
    { label: '编辑', type: 'warning', code: 'edit' },
  ]

  if (row.status === 1) {
    actions.push({ label: '禁用', type: 'danger', code: 'disable' })
  } else {
    actions.push({ label: '启用', type: 'success', code: 'enable' })
  }

  return actions
}

const columns = [
  {
    label: '操作',
    type: 'action',
    actions: getActions,
  },
]
```

### 分页数据加载

```js
const loadTableData = async () => {
  loading.value = true
  try {
    const { data, total } = await getUserList({
      page: currentPage.value,
      pageSize: currentPageSize.value,
    })
    tableData.value = data
    totalCount.value = total
  } finally {
    loading.value = false
  }
}

const handlePaginationChange = ({ page, pageSize }) => {
  currentPage.value = page
  currentPageSize.value = pageSize
  loadTableData()
}
```
