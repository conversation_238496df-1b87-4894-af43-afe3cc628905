# CommonForm 公共表单组件

基于 Element Plus 封装的通用表单组件，支持常用的表单控件和查询重置功能。

## 基础用法

```vue
<template>
  <CommonForm v-model="formData" :form-items="formItems" @query="handleQuery" @reset="handleReset" />
</template>

<script setup>
import { ref } from 'vue'

const formData = ref({
  name: '',
  status: '',
  date: '',
})

const formItems = ref([
  {
    type: 'input',
    prop: 'name',
    label: '姓名',
    placeholder: '请输入姓名',
    required: true,
  },
  {
    type: 'select',
    prop: 'status',
    label: '状态',
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 },
    ],
  },
  {
    type: 'date',
    prop: 'date',
    label: '日期',
    dateType: 'date',
  },
])

const handleQuery = data => {
  console.log('查询参数:', data)
}

const handleReset = data => {
  console.log('重置后数据:', data)
}
</script>
```

## 支持的控件类型

### input - 输入框

```js
{
  type: 'input',
  prop: 'name',
  label: '姓名',
  placeholder: '请输入姓名',
  props: {
    clearable: true,
    showPassword: false, // 密码框
    maxlength: 100
  }
}
```

### select - 选择器

```js
{
  type: 'select',
  prop: 'status',
  label: '状态',
  options: [
    { label: '启用', value: 1 },
    { label: '禁用', value: 0 }
  ],
  props: {
    multiple: false,
    filterable: true
  }
}
```

### dict - 字典选择器

```js
{
  type: 'dict',
  prop: 'trainingType',
  label: '培训类别',
  placeholder: '请选择培训类别',
  dictCode: 'training_type', // 字典编码
  required: true,
  props: {
    clearable: true
  }
}
```

### cascader - 级联选择器

```js
{
  type: 'cascader',
  prop: 'area',
  label: '地区',
  options: [
    {
      value: 'zhejiang',
      label: '浙江',
      children: [
        { value: 'hangzhou', label: '杭州' }
      ]
    }
  ]
}
```

### date - 日期选择器

```js
{
  type: 'date',
  prop: 'date',
  label: '日期',
  dateType: 'date', // date、datetime、daterange
  props: {
    valueFormat: 'YYYY-MM-DD',
    format: 'YYYY-MM-DD'
  }
}
```

### time - 时间选择器

```js
{
  type: 'time',
  prop: 'startTime',
  label: '开始时间',
  placeholder: '请选择时间',
  props: {
    valueFormat: 'HH:mm:ss',
    format: 'HH:mm:ss',
    clearable: true
  }
}
```

### datetime - 日期时间选择器

```js
{
  type: 'datetime',
  prop: 'createTime',
  label: '创建时间',
  placeholder: '请选择日期时间',
  props: {
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
    format: 'YYYY-MM-DD HH:mm:ss',
    clearable: true
  }
}
```

## 高级用法

### 自定义验证规则

```js
const formItems = [
  {
    type: 'input',
    prop: 'email',
    label: '邮箱',
    required: true,
    rules: [{ type: 'email', message: '请输入正确的邮箱格式' }],
  },
]

// 或者通过 rules 属性
const rules = {
  username: [
    { required: true, message: '用户名不能为空' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符' },
  ],
}
```

### 自定义插槽

```vue
<template>
  <CommonForm v-model="formData" :form-items="formItems">
    <template #custom-field="{ item, formData }">
      <el-button @click="handleCustomAction">自定义操作</el-button>
    </template>
  </CommonForm>
</template>

<script setup>
const formItems = [
  {
    type: 'slot',
    slotName: 'custom-field',
    label: '自定义字段',
  },
]
</script>
```

### 使用 Element Plus 原生属性

```js
const formItems = [
  {
    type: 'input',
    prop: 'name',
    label: '姓名',
    props: {
      // Element Plus Input 的额外属性使用-示例
      clearable: true,
      showWordLimit: true,
      maxlength: 50,
    },
  },
]

// 表单级别的属性
const formProps = {
  labelWidth: '100px',
  labelPosition: 'top',
  size: 'large',
}
```
