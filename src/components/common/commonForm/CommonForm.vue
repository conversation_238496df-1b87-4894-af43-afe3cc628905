<template>
  <div class="common-query">
    <!-- 栅格布局表单 -->
    <div v-if="!inline" class="grid-form-container">
      <el-form ref="formRef" :model="formData" :rules="formRules" v-bind="formProps" class="form-container">
        <el-row :gutter="20">
          <!-- 表单项 -->
          <el-col v-for="(item, index) in displayFormItems" :key="item.prop || index" :span="item.span || 8">
            <el-form-item :label="item.label" :prop="item.prop" :required="item.required">
              <!-- 输入框 -->
              <el-input
                v-if="item.type === 'input'"
                v-model="formData[item.prop]"
                :placeholder="item.placeholder || `请输入${item.label}`"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              />

              <!-- 选择器 -->
              <el-select
                v-else-if="item.type === 'select'"
                v-model="formData[item.prop]"
                :placeholder="item.placeholder || `请选择${item.label}`"
                style="width: 100%"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              >
                <el-option
                  v-for="option in item.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 字典选择器 -->
              <!-- <DictSelect
                v-else-if="item.type === 'dict'"
                v-model:value="formData[item.prop]"
                :dict-code="item.dictCode"
                :placeholder="item.placeholder || `请选择${item.label}`"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              /> -->

              <!-- 级联选择器 -->
              <el-cascader
                v-else-if="item.type === 'cascader'"
                v-model="formData[item.prop]"
                :options="item.options"
                :placeholder="item.placeholder || `请选择${item.label}`"
                style="width: 100%"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              />

              <!-- 日期选择器 -->
              <el-date-picker
                v-else-if="item.type === 'date'"
                v-model="formData[item.prop]"
                :type="item.dateType || 'date'"
                :placeholder="item.placeholder || `请选择${item.label}`"
                style="width: 100%"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              />

              <!-- 时间选择器 -->
              <el-time-picker
                v-else-if="item.type === 'time'"
                v-model="formData[item.prop]"
                :placeholder="item.placeholder || `请选择${item.label}`"
                style="width: 100%"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              />

              <!-- 日期时间选择器 -->
              <el-date-picker
                v-else-if="item.type === 'datetime'"
                v-model="formData[item.prop]"
                type="datetime"
                :placeholder="item.placeholder || `请选择${item.label}`"
                style="width: 100%"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              />

              <!-- 自定义插槽 -->
              <slot v-else-if="item.type === 'slot'" :name="item.slotName" :item="item" :form-data="formData" />
            </el-form-item>
          </el-col>

          <!-- 操作按钮区域 - 根据表单项数量决定位置 -->
          <el-col v-if="showAction" :span="actionButtonSpan" :class="actionButtonClass">
            <div class="action-buttons-wrapper">
              <div class="action-left-slot">
                <slot name="action-left" :form-data="formData" />
              </div>
              <div class="action-buttons-container">
                <div class="button-group">
                  <el-button v-if="showQuery" type="primary" :loading="queryLoading" @click="handleQuery">
                    {{ queryText }}
                  </el-button>
                  <el-button v-if="showReset" @click="handleReset">
                    {{ resetText }}
                  </el-button>
                  <!-- 展开/收起按钮 - 仅在表单项>=4个时显示 -->
                  <div class="ignore">
                    <div v-if="needToggleButton" class="toggle-button" @click="toggleExpanded">
                      {{ isExpanded ? '收起' : '展开' }}
                      <el-icon :class="{ 'rotate-180': isExpanded }">
                        <ArrowDown />
                      </el-icon>
                    </div>
                  </div>
                </div>
                <div class="action-right-slot">
                  <slot name="action-right" :form-data="formData" />
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 行内表单 -->
    <div v-else class="inline-form-container">
      <el-form ref="formRef" :model="formData" :rules="formRules" v-bind="formProps" class="form-container">
        <div class="inline-form-content">
          <!-- 表单字段 -->
          <div class="form-fields">
            <el-form-item
              v-for="(item, index) in visibleFormItems"
              :key="item.prop || index"
              :label="item.label"
              :prop="item.prop"
              :required="item.required"
            >
              <el-input
                v-if="item.type === 'input'"
                v-model="formData[item.prop]"
                :placeholder="item.placeholder || `请输入${item.label}`"
                style="width: 240px"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              />
              <el-select
                v-else-if="item.type === 'select'"
                v-model="formData[item.prop]"
                :placeholder="item.placeholder || `请选择${item.label}`"
                style="width: 240px"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              >
                <el-option
                  v-for="option in item.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <!-- 字典选择器 -->
              <!-- <DictSelect
                v-else-if="item.type === 'dict'"
                v-model:value="formData[item.prop]"
                :dict-code="item.dictCode"
                :placeholder="item.placeholder || `请选择${item.label}`"
                style="width: 240px"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              /> -->
              <el-cascader
                v-else-if="item.type === 'cascader'"
                v-model="formData[item.prop]"
                :options="item.options"
                :placeholder="item.placeholder || `请选择${item.label}`"
                style="width: 240px"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              />
              <el-date-picker
                v-else-if="item.type === 'date'"
                v-model="formData[item.prop]"
                :type="item.dateType || 'date'"
                :placeholder="item.placeholder || `请选择${item.label}`"
                style="width: 240px"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              />
              <!-- 时间选择器 -->
              <el-time-picker
                v-else-if="item.type === 'time'"
                v-model="formData[item.prop]"
                :placeholder="item.placeholder || `请选择${item.label}`"
                style="width: 240px"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              />
              <!-- 日期时间选择器 -->
              <el-date-picker
                v-else-if="item.type === 'datetime'"
                v-model="formData[item.prop]"
                type="datetime"
                :placeholder="item.placeholder || `请选择${item.label}`"
                style="width: 240px"
                v-bind="item.props"
                @change="handleFieldChange(item, $event)"
              />
              <!-- 自定义插槽 -->
              <slot v-else-if="item.type === 'slot'" :name="item.slotName" :item="item" :form-data="formData" />
            </el-form-item>
          </div>

          <!-- 操作按钮区域 - 行内表单时在右侧 -->
          <div v-if="showAction" class="inline-action-buttons">
            <slot name="action-left" :form-data="formData" />
            <div class="button-group">
              <el-button v-if="showQuery" type="primary" :loading="queryLoading" @click="handleQuery">
                {{ queryText }}
              </el-button>
              <el-button v-if="showReset" @click="handleReset">
                {{ resetText }}
              </el-button>
            </div>
            <slot name="action-right" :form-data="formData" />
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
// import DictSelect from '../DictSelect/DictSelect.vue'

// 组件属性定义
const props = defineProps({
  // 表单数据
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  // 表单配置项
  formItems: {
    type: Array,
    default: () => [],
  },
  // 验证规则
  rules: {
    type: Object,
    default: () => ({}),
  },
  // 是否行内表单
  inline: {
    type: Boolean,
    default: false,
  },
  // 是否显示操作按钮
  showAction: {
    type: Boolean,
    default: true,
  },
  // 是否显示查询按钮
  showQuery: {
    type: Boolean,
    default: true,
  },
  // 是否显示重置按钮
  showReset: {
    type: Boolean,
    default: true,
  },
  // 查询按钮文本
  queryText: {
    type: String,
    default: '查询',
  },
  // 重置按钮文本
  resetText: {
    type: String,
    default: '重置',
  },
  // 查询按钮加载状态
  queryLoading: {
    type: Boolean,
    default: false,
  },
  // Element Plus Form 组件的其他属性
  formProps: {
    type: Object,
    default: () => ({
      labelWidth: '120px',
      labelPosition: 'right',
    }),
  },
  // 默认显示的表单项数量（当表单项>=4个时生效）
  defaultShowCount: {
    type: Number,
    default: 4,
  },
})

const emit = defineEmits(['update:modelValue', 'query', 'reset', 'field-change'])

// 表单引用
const formRef = ref()

// 展开/收起状态
const isExpanded = ref(false)

// 表单数据
const formData = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

// 表单验证规则
const formRules = computed(() => {
  const rules = { ...props.rules }
  // 自动生成必填规则
  props.formItems.forEach(item => {
    if (item.required && !rules[item.prop]) {
      rules[item.prop] = [
        {
          required: true,
          message: `${item.label}不能为空`,
          trigger: ['blur', 'change'],
        },
      ]
    }
    // 合并字段自定义规则
    if (item.rules) {
      rules[item.prop] = rules[item.prop] ? [...rules[item.prop], ...item.rules] : item.rules
    }
  })
  return rules
})

// 可见的表单项（过滤隐藏项）
const visibleFormItems = computed(() => {
  return props.formItems.filter(item => !item.hidden)
})

// 是否需要显示展开/收起按钮
const needToggleButton = computed(() => {
  return visibleFormItems.value.length > props.defaultShowCount
})

// 实际显示的表单项（考虑展开/收起状态）
const displayFormItems = computed(() => {
  if (!needToggleButton.value) {
    // 表单项<=4个时，全部显示
    return visibleFormItems.value
  }

  if (isExpanded.value) {
    // 展开状态，显示全部
    return visibleFormItems.value
  } else {
    // 收起状态，只显示前几个
    return visibleFormItems.value.slice(0, props.defaultShowCount)
  }
})

// 操作按钮的span值
const actionButtonSpan = computed(() => {
  if (needToggleButton.value) {
    // 表单项>4个时，操作按钮独占一行，右对齐
    return 24
  } else {
    // 表单项<=4个时，计算剩余空间
    const usedSpan = visibleFormItems.value.reduce((total, item) => total + (item.span || 8), 0)
    const remainingSpan = 24 - (usedSpan % 24)
    // 如果正好占满一行，则按钮另起一行
    return remainingSpan === 0 ? 24 : remainingSpan
  }
})

// 操作按钮的CSS类
const actionButtonClass = computed(() => {
  if (needToggleButton.value) {
    return 'action-buttons-right'
  } else {
    // 计算是否需要另起一行
    const usedSpan = visibleFormItems.value.reduce((total, item) => total + (item.span || 8), 0)
    const remainingSpan = 24 - (usedSpan % 24)
    return remainingSpan === 0 ? 'action-buttons-right' : 'action-buttons-inline'
  }
})

// 展开/收起切换
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// 处理字段变化
const handleFieldChange = (item, value) => {
  // 对数字类型的输入框进行类型转换
  let processedValue = value
  if (item.type === 'input' && item.props?.type === 'number') {
    // 如果是数字输入框，转换为数字类型
    if (value !== '' && value !== null && value !== undefined) {
      const numValue = Number(value)
      if (!isNaN(numValue)) {
        processedValue = numValue
      }
    }
  }

  // 更新表单数据
  formData.value[item.prop] = processedValue

  emit('field-change', {
    prop: item.prop,
    value: processedValue,
    item,
  })
  // 执行字段自定义事件处理器
  if (item.onChange) {
    item.onChange(processedValue, formData.value)
  }
}

// 查询处理
const handleQuery = async () => {
  const valid = await validate()
  if (valid) {
    emit('query', formData.value)
  }
}

// 重置处理
const handleReset = () => {
  resetFields()
  emit('reset', formData.value)
}

// 表单验证
const validate = callback => {
  return formRef.value?.validate(callback)
}

// 重置表单
const resetFields = () => {
  formRef.value?.resetFields()
}

// 暴露方法给父组件
defineExpose({
  validate,
  resetFields,
  formRef,
  toggleExpanded,
  isExpanded,
})
</script>

<style lang="less" scoped>
.common-query {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 24px 24px 6px 24px;
  margin-bottom: 24px;
}

.form-container {
  width: 100%;
}

/* 栅格布局容器 */
.grid-form-container {
  width: 100%;
  :deep(.el-form-item__label) {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.9);
  }
}

/* 操作按钮包装器 */
.action-buttons-wrapper {
  display: flex;
  align-items: flex-start;
  height: 100%;
  width: 100%;
  padding-top: 6px; /* 与表单项标签对齐 */
}

/* 左侧插槽 */
.action-left-slot {
  flex-shrink: 0;
}

/* 按钮容器 */
.action-buttons-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}

/* 右侧插槽 */
.action-right-slot {
  flex-shrink: 0;
}

/* 表单项>=4个时，操作按钮右对齐 */
// .action-buttons-right {
//   .action-buttons-container {
//     justify-content: flex-end;
//   }
// }

/* 表单项<4个时，操作按钮也右对齐 */
// .action-buttons-inline {
//   .action-buttons-container {
//     justify-content: flex-end;
//   }
// }

/* 按钮组 */
.button-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 展开/收起按钮样式 */
.ignore .toggle-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px !important;
  color: #606266;
  cursor: pointer;
  font-size: 14px;
}

.toggle-button:hover {
  color: #f87130;
}

/* 图标旋转动画 */
.toggle-button .el-icon {
  transition: transform 0.3s ease;
}

.toggle-button .el-icon.rotate-180 {
  transform: rotate(180deg);
}

/* 行内表单容器 */
.inline-form-container {
  width: 100%;
}

.inline-form-content {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  width: 100%;
}

.form-fields {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16px;
}

/* 行内表单时的操作按钮样式 - 右侧 */
.inline-action-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

/* 行内表单项样式调整 */
.inline-form-container .el-form-item {
  margin-bottom: 0 !important;
  margin-right: 0;
}

/* 响应式处理 */
@media (max-width: 768px) {
  .inline-form-content {
    flex-direction: column;
    align-items: stretch;
  }

  .form-fields {
    margin-bottom: 16px;
  }

  .inline-action-buttons {
    justify-content: flex-end;
  }

  /* 移动端时操作按钮也改为右对齐 */
  .action-buttons-container {
    justify-content: flex-end !important;
  }
}
</style>
