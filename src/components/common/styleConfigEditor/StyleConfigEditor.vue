<template>
  <div class="style-config-editor">
    <el-tabs v-model="activeTab" class="style-tabs">
      <!-- 颜色选择器标签页 -->
      <el-tab-pane label="颜色选择器" name="colorPicker">
        <div class="color-picker-panel">
          <div class="color-section">
            <div class="color-item">
              <label class="color-label">颜色</label>
              <el-color-picker
                v-model="colorValues.color"
                :predefine="predefineColors"
                @change="handleColorChange('color', $event)"
              />
              <span class="color-value">{{ colorValues.color || '#000000' }}</span>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 手动配置标签页 -->
      <el-tab-pane label="手动配置" name="manualConfig">
        <div class="manual-config-panel">
          <el-form :model="styleConfig" label-position="top" class="style-form">
            <el-row :gutter="20"> </el-row>
            <el-form-item label="自定义CSS">
              <el-input
                v-model="styleConfig.customCSS"
                type="textarea"
                :rows="4"
                placeholder="输入自定义CSS属性(用分号间隔)，如: color: #ffffff; size: 12"
                @input="handleManualChange"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- JSON输出预览 -->
    <div class="json-output">
      <div class="output-label">
        <span>JSON输出</span>
      </div>
      <el-input :model-value="jsonOutput" type="textarea" :rows="3" readonly class="json-textarea" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: [Object, String],
    default: () => ({}),
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue'])

let isInternalChange = false // 用于防止更新循环的标志

// 响应式数据
const activeTab = ref('colorPicker')

// 预定义颜色
const predefineColors = ref([
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  '#ff1493',
  '#000000',
  '#ffffff',
])

// 颜色值（用于颜色选择器）
const colorValues = reactive({
  color: '#000000',
})

// 样式配置（用于手动配置）
const styleConfig = reactive({
  color: '#000000',
  customCSS: '',
})

const getParsedStyles = config => {
  const output = { ...config }
  if (output.customCSS) {
    const cssRules = output.customCSS.split(';').filter(rule => rule.trim() !== '')
    cssRules.forEach(rule => {
      const [key, value] = rule.split(':').map(part => part.trim())
      if (key && value) {
        output[key] = value
      }
    })
  }
  delete output.customCSS

  // 移除空值
  Object.keys(output).forEach(key => {
    if (output[key] === null || output[key] === undefined || output[key] === '') {
      delete output[key]
    }
  })
  return output
}

// 计算属性：JSON输出
const jsonOutput = computed(() => {
  const parsed = getParsedStyles({ ...styleConfig })
  return JSON.stringify(parsed, null, 2)
})

// 方法：处理颜色选择器变化
const handleColorChange = (colorType, value) => {
  colorValues[colorType] = value
  styleConfig[colorType] = value
  emitChange()
}

// 方法：处理手动配置变化
const handleManualChange = () => {
  // 同步颜色值到颜色选择器
  colorValues.color = styleConfig.color
  emitChange()
}

// 方法：发出变化事件
const emitChange = () => {
  const parsedOutput = getParsedStyles({ ...styleConfig })
  isInternalChange = true
  emit('update:modelValue', JSON.stringify(parsedOutput, null, 2))
}

// 方法：初始化数据
const initializeData = () => {
  let initialValue = props.modelValue

  if (typeof initialValue === 'string') {
    try {
      initialValue = JSON.parse(initialValue)
    } catch {
      initialValue = {}
    }
  }

  const knownProps = ['color', 'customCSS']
  const cssParts = []

  if (initialValue && typeof initialValue === 'object') {
    for (const key in initialValue) {
      if (!knownProps.includes(key)) {
        cssParts.push(`${key}: ${initialValue[key]}`)
      }
    }
  }

  const defaultConfig = {
    color: '#000000',
    customCSS: cssParts.join('; ') + (cssParts.length > 0 ? ';' : ''),
  }

  const mergedConfig = { ...defaultConfig, ...initialValue }

  styleConfig.color = mergedConfig.color
  styleConfig.customCSS = mergedConfig.customCSS

  // 同步颜色值
  colorValues.color = styleConfig.color
}

// 监听props变化
watch(
  () => props.modelValue,
  () => {
    if (isInternalChange) {
      isInternalChange = false // 重置标志并跳过本次更新
      return
    }
    initializeData()
  },
  { deep: true },
)

// 组件挂载时初始化
onMounted(() => {
  initializeData()
})
</script>

<style scoped lang="less">
.style-config-editor {
  width: 100%;

  .color-picker-panel {
    .color-section {
      margin-bottom: 30px;

      .color-item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        gap: 15px;

        .color-label {
          min-width: 80px;
          font-weight: 500;
          color: #606266;
        }

        .color-value {
          font-family: 'Courier New', monospace;
          color: #909399;
          font-size: 12px;
          min-width: 100px;
        }
      }
    }
  }

  .manual-config-panel {
    .style-form {
      margin-bottom: 30px;

      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #606266;
      }

      :deep(.el-input-group__prepend) {
        background-color: #f5f7fa;
        border-color: #dcdfe6;
        color: #909399;
      }
    }
  }

  .preview-section {
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    .preview-label {
      font-weight: 500;
      color: #606266;
      margin-bottom: 15px;
    }

    .style-preview {
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #fff;
      text-align: center;
      font-size: 16px;
      min-height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .json-output {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    .output-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      font-weight: 500;
      color: #606266;
    }

    .json-textarea {
      :deep(.el-textarea__inner) {
        font-family: 'Courier New', monospace;
        font-size: 12px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
      }
    }
  }
}
</style>
