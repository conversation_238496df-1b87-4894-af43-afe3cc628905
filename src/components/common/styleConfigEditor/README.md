# StyleConfigEditor 样式配置编辑器

一个功能完整的Vue 3组件，用于管理和配置样式属性，支持颜色选择器和手动配置两种方式。


## 基本用法

### 1. 导入组件

```vue
<script setup>
import StyleConfigEditor from '@/components/common/styleConfigEditor/StyleConfigEditor.vue'
import { ref } from 'vue'

</script>
```

### 2. 完整示例

```vue
<template>
  <div class="demo">
    <h2>样式配置编辑器演示</h2>
    
    <!-- 样式编辑器 -->
    <StyleConfigEditor v-model="currentStyle" />
    
    <!-- 应用样式的示例元素 -->
    <div class="styled-element" :style="currentStyle">
      这是应用了配置样式的示例文本
    </div>
    
    <!-- 配置结果 -->
    <div class="config-result">
      <h3>当前样式配置：</h3>
      <pre>{{ JSON.stringify(currentStyle, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import StyleConfigEditor from '@/components/common/styleConfigEditor/StyleConfigEditor.vue'

// 样式配置数据
const currentStyle = ref({
  color: '#333333',
})
</script>

```
