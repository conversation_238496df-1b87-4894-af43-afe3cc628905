<template>
  <el-select v-model="selectedKey" :placeholder="placeholder" :disabled="disabled" clearable style="width: 100%">
    <el-option v-for="option in statusOptions" :key="option.key" :label="option.label" :value="option.key" />
  </el-select>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '请选择状态',
  },
})

const emit = defineEmits(['update:modelValue'])

const statusOptions = [
  {
    label: '运行中',
    key: 'running',
    value: { status: 1, runningStatus: 'RUNNING' },
  },
  {
    label: '规划中',
    key: 'planning',
    value: { status: 1, runningStatus: 'PLANNING' },
  },
  {
    label: '停用',
    key: 'stopped',
    value: { status: 2, runningStatus: null },
  },
]

const selectedKey = computed({
  get() {
    const currentValue = props.modelValue
    if (!currentValue || currentValue.status === null || currentValue.status === undefined) {
      return null
    }

    // 如果状态是“停用”，则忽略runningStatus
    if (currentValue.status === 2) {
      return 'stopped'
    }

    // 如果状态是“启用”，则根据runningStatus判断
    if (currentValue.status === 1) {
      if (currentValue.runningStatus === 'RUNNING') {
        return 'running'
      }
      if (currentValue.runningStatus === 'PLANNING') {
        return 'planning'
      }
    }

    return null
  },
  set(newKey) {
    if (!newKey) {
      emit('update:modelValue', null)
      return
    }
    const selectedOption = statusOptions.find(opt => opt.key === newKey)
    if (selectedOption) {
      emit('update:modelValue', { ...selectedOption.value })
    }
  },
})
</script>

<style scoped></style>
