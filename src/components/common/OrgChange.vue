<template>
  <el-dialog
    v-model="open"
    title="选择机构"
    :width="320"
    :align-center="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
  >
    <el-form>
      <el-form-item>
        <el-radio-group v-model="code">
          <el-radio v-for="(item, index) in orgList" :key="index" :value="item.id" class="radio" size="large" border>{{
            item.orgName
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <div style="width: 100%; text-align: right">
          <el-button @click="onClose">取 消</el-button>
          <el-button type="primary" @click="onSubmit">确 定</el-button>
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup>
import useOrg from '@/hooks/useOrg.js'
const { orgId, orgList, selectOrg } = useOrg()

const open = ref(false)
const code = ref(orgId.value)

const onOpen = () => {
  open.value = true
}

const onClose = () => {
  open.value = false
}

const onSubmit = () => {
  selectOrg(code.value)
  open.value = false
}

defineExpose({
  onOpen,
})
</script>

<style scoped lang="less">
.radio {
  width: 100%;
  margin: 4px 0 !important;
}

:deep(.el-radio.is-bordered.is-checked) {
  border-width: 1.5px;
}
</style>
