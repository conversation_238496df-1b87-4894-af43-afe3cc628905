<template>
  <el-dialog
    v-model="open"
    title="选择机构"
    :width="320"
    :align-center="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    :show-close="canClose"
  >
    <el-form>
      <el-form-item>
        <el-radio-group v-model="code">
          <el-radio
            v-for="(item, index) in orgList"
            :key="index"
            :value="item.id"
            :disabled="!item.enable"
            class="radio"
            size="large"
            border
            >{{ item.orgName }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <span class="tip" v-if="canClose">* 切换机构将退回首页</span>
      <el-form-item>
        <div style="width: 100%; text-align: right; margin-top: 10px">
          <el-button v-show="canClose" @click="onClose">取 消</el-button>
          <el-button v-show="code" type="primary" @click="onSubmit">确 定</el-button>
        </div>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup>
import useOrg from '@/hooks/useOrg.js'
import useComponent, { PathType } from '@/hooks/useComponent.js'
const { orgId, orgList, selectOrg } = useOrg()
const { setPath } = useComponent()

const open = ref(false)
const code = ref(orgId.value)
const canClose = ref(true)

const onOpen = (close = true) => {
  open.value = true
  canClose.value = close
  code.value = orgId.value
}

const onClose = () => (open.value = false)

const onSubmit = () => {
  onClose()
  if (orgId.value === code.value) return
  selectOrg?.(code.value)
  setPath?.('map/ShowMap.vue', null, { type: PathType.successBack })
}

defineExpose({
  onOpen,
})
</script>

<style scoped lang="less">
.radio {
  width: 100%;
  margin: 4px 0 !important;
}
.tip {
  color: red;
  margin-left: 5px;
  font-size: 13px;
}
:deep(.el-radio.is-bordered.is-checked) {
  border-width: 1.5px;
}
</style>
