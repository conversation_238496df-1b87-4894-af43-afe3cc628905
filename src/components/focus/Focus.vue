<template>
  <div class="focus">
    <CommonTitle />
    <div class="common-container">
      <div ref="cardRef" class="common-statistics-card">
        <el-row :gutter="24">
          <el-col :span="6">
            <StatisticsCard
              title="今日无人机关注数"
              :value="countInfo.today.uav.count"
              :data-list="[
                {
                  subTitle: '审批',
                  subValue: countInfo.today.uav.plan,
                  color: 'blue',
                },
                {
                  subTitle: '备案',
                  subValue: countInfo.today.uav.filing,
                  color: 'blue',
                },
                {
                  subTitle: '临时',
                  subValue: countInfo.today.uav.temp,
                  color: 'blue',
                },
              ]"
              img="statistics/personal-1.png"
            />
          </el-col>
          <el-col :span="6">
            <StatisticsCard
              title="今日有人机关注数"
              :value="countInfo.today.noUav.count"
              :data-list="[
                {
                  subTitle: '审批',
                  subValue: countInfo.today.noUav.plan,
                  color: 'blue',
                },
                {
                  subTitle: '备案',
                  subValue: countInfo.today.noUav.filing,
                  color: 'blue',
                },
                {
                  subTitle: '临时',
                  subValue: countInfo.today.noUav.temp,
                  color: 'blue',
                },
              ]"
              img="statistics/personal-1.png"
            />
          </el-col>
          <el-col :span="6">
            <StatisticsCard
              title="明日无人机关注数"
              :value="countInfo.nextDay.uav.count"
              :data-list="[
                {
                  subTitle: '审批',
                  subValue: countInfo.nextDay.uav.plan,
                  color: 'blue',
                },
                {
                  subTitle: '备案',
                  subValue: countInfo.nextDay.uav.filing,
                  color: 'blue',
                },
                {
                  subTitle: '临时',
                  subValue: countInfo.nextDay.uav.temp,
                  color: 'blue',
                },
              ]"
              img="statistics/personal-1.png"
            />
          </el-col>
          <el-col :span="6">
            <StatisticsCard
              title="明日有人机关注数"
              :value="countInfo.nextDay.noUav.count"
              :data-list="[
                {
                  subTitle: '审批',
                  subValue: countInfo.nextDay.noUav.plan,
                  color: 'blue',
                },
                {
                  subTitle: '备案',
                  subValue: countInfo.nextDay.noUav.filing,
                  color: 'blue',
                },
                {
                  subTitle: '临时',
                  subValue: countInfo.nextDay.noUav.temp,
                  color: 'blue',
                },
              ]"
              img="statistics/personal-1.png"
            />
          </el-col>
        </el-row>
      </div>
      <div ref="queryRef" class="common-query">
        <el-form :inline="true" :model="queryParams" label-width="140px">
          <el-row :gutter="20">
            <el-col :span="6"
              ><el-form-item label="序列号">
                <el-input
                  v-model="queryParams.aircraftCodeLike"
                  placeholder="输入序列号模糊查询"
                  clearable
                /> </el-form-item
            ></el-col>
            <el-col :span="6"
              ><el-form-item label="航空器类型">
                <el-select v-model="queryParams.aircraftType" placeholder="选择航空器类型" clearable>
                  <el-option label="无人机" value="UAV" />
                  <el-option label="非无人机" value="NO_UAV" />
                </el-select> </el-form-item
            ></el-col>
            <el-col :span="6"
              ><el-form-item label="目的">
                <el-select v-model="queryParams.purpose" placeholder="选择目的" clearable>
                  <el-option label="审批监视" value="PLAN" />
                  <el-option label="备案监视" value="FILING" />
                  <el-option label="临时关注" value="TEMP" />
                </el-select> </el-form-item
            ></el-col>
            <el-col v-show="isExpand" :span="6"
              ><el-form-item label="来源">
                <el-select v-model="queryParams.source" placeholder="选择来源" clearable>
                  <el-option label="华航信" value="华航信" />
                  <el-option label="电信巡龙" value="电信巡龙" />
                  <el-option label="万丰" value="万丰" />
                </el-select> </el-form-item
            ></el-col>
            <el-col v-show="isExpand" :span="6"
              ><el-form-item label="状态">
                <el-select v-model="queryParams.originalStatus" placeholder="选择状态" clearable>
                  <el-option label="启用" value="1" />
                  <el-option label="停用" value="2" />
                  <el-option label="删除" value="Del" />
                </el-select> </el-form-item
            ></el-col>
            <el-col v-show="isExpand" :span="6">
              <el-form-item label="关注日期">
                <el-date-picker
                  v-model="queryParams.createTime"
                  type="daterange"
                  range-separator="-"
                  format="YYYY-MM-DD"
                  date-format="YYYY-MM-DD"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :shortcuts="shortcuts"
                  :clearable="false"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6"
              ><el-form-item label="备注">
                <el-input v-model="queryParams.memoLike" placeholder="输入备注模糊查询" clearable /> </el-form-item
            ></el-col>
            <el-col :span="6"
              ><el-form-item>
                <div style="text-align: right; width: 100%">
                  <el-button type="primary" @click="handlerQuery">查询</el-button>
                  <el-button @click="handlerReset">重置</el-button>
                  <el-button v-if="isExpand" type="primary" link @click="collapse"
                    >收起<el-icon style="margin-left: 6px"><ArrowUp /></el-icon
                  ></el-button>
                  <el-button v-if="!isExpand" type="primary" link @click="expend"
                    >展开<el-icon style="margin-left: 6px"><ArrowDown /></el-icon
                  ></el-button>
                </div> </el-form-item
            ></el-col>
          </el-row>
        </el-form>
      </div>
      <div class="common-table" :style="`height:${tableRefHeight}px`">
        <div style="height: 40px; display: flex; justify-content: space-between; margin-bottom: 8px">
          <div style="font-weight: 600; font-size: 18px">重点关注表</div>
          <div>
            <el-button type="primary" @click="handlerAdd">新增</el-button>
          </div>
        </div>
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="tableData"
          :style="{ width: '100%', height: tableHeight + 'px' }"
          row-class-name="default-row"
          row-key="id"
        >
          <el-table-column prop="aircraftCode" label="航空器序列号或标牌号" :show-overflow-tooltip="true" width="220" />
          <el-table-column prop="aircraftType" label="航空器类型" :show-overflow-tooltip="true" width="120">
            <template #default="scope">
              <span v-if="scope.row.aircraftType === 'UAV'">无人机</span>
              <span v-if="scope.row.aircraftType === 'NO_UAV'">非无人机</span>
            </template>
          </el-table-column>
          <el-table-column prop="purpose" label="目的" :show-overflow-tooltip="true" width="110">
            <template #default="scope">
              <span v-if="scope.row.purpose === 'PLAN'">审批监视</span>
              <span v-if="scope.row.purpose === 'FILING'">备案监视</span>
              <span v-if="scope.row.purpose === 'TEMP'">临时关注</span>
            </template>
          </el-table-column>
          <el-table-column prop="source" label="来源" :show-overflow-tooltip="true" width="100" />
          <el-table-column prop="watchStartDate" label="关注开始日期" :show-overflow-tooltip="true" width="120" />
          <el-table-column prop="watchEndDate" label="关注结束日期" :show-overflow-tooltip="true" width="120" />
          <el-table-column prop="memo" label="备注" :show-overflow-tooltip="true" />
          <el-table-column prop="status" label="状态" :show-overflow-tooltip="true" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.deleteTime" type="danger" size="large">已删除</el-tag>
              <el-tag v-else-if="scope.row.status === 1" type="success" size="large">已启用</el-tag>
              <el-tag v-else-if="scope.row.status === 2" type="danger" size="large">已停用</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" :show-overflow-tooltip="true" width="200">
            <template #default="scope">
              {{ scope.row.createTime && formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="deleteTime" label="删除时间" :show-overflow-tooltip="true" width="200">
            <template #default="scope">
              {{ (scope.row.deleteTime || null) && formatDate(scope.row.deleteTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220">
            <template #default="scope">
              <el-button v-if="scope.row.deleteTime" type="primary" @click.native.stop="recoverData(scope.row.id)"
                >恢复</el-button
              >
              <div v-else>
                <el-button v-if="scope.row.status === 2" type="danger" @click.native.stop="deleteData(scope.row.id)"
                  >删除</el-button
                >
                <el-button v-if="scope.row.status === 1" type="primary" @click.native.stop="handlerEdit(scope.row)"
                  >编辑</el-button
                >
                <el-button v-if="scope.row.status === 1" type="danger" @click.native.stop="changeStatus(scope.row, 2)"
                  >停用</el-button
                >
                <el-button v-if="scope.row.status === 2" type="danger" @click.native.stop="changeStatus(scope.row, 1)"
                  >启用</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.limit"
          @pagination="handlerQuery"
        />
      </div>
      <Dialog ref="dialogRef" @refresh="refresh(false)" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Pagination from '@/components/common/pagination/Pagination.vue'
import Dialog from '@/components/focus/Dialog.vue'
import StatisticsCard from '@/components/common/statisticsCard/StatisticsCard.vue'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { list, count, del, recover, update } from '@/request/api/focus.js'
import { formatDate } from '@/utils/formatUtils.js'
import { useDateFormat } from '@vueuse/core'
import { ElMessage } from 'element-plus'
import { deleteSpaceKey, getDatePeriod } from '@/utils/common.js'
import CommonTitle from '@/components/common/commonTitle/CommonTitle.vue'
import { CommonNext3DayRange, CommonNextDayRange, CommonNextNextDayRange, CommonTodayRange } from '@/data/Data.js'

const queryParams = ref({
  page: 0,
  limit: 10,
  aircraftCodeLike: undefined,
  purpose: undefined,
  source: undefined,
  memoLike: undefined,
  status: undefined,
  originalStatus: undefined,
  deleteStatus: undefined,
  createTime: CommonNext3DayRange,
})

const tableData = ref([])
const total = ref(0)
const loading = ref(false)
const queryRef = useTemplateRef('queryRef')
const tableRefHeight = ref(0)
const tableHeight = ref(0)
const dialogRef = useTemplateRef('dialogRef')
const isExpand = ref(true)

const countInfo = ref({
  today: {
    uav: {
      count: 0,
      plan: 0,
      filing: 0,
      temp: 0,
    },
    noUav: {
      count: 0,
      plan: 0,
      filing: 0,
      temp: 0,
    },
  },
  nextDay: {
    uav: {
      count: 0,
      plan: 0,
      filing: 0,
      temp: 0,
    },
    noUav: {
      count: 0,
      plan: 0,
      filing: 0,
      temp: 0,
    },
  },
})

const shortcuts = [
  {
    text: '今天',
    value: CommonTodayRange,
  },
  {
    text: '明天',
    value: CommonNextDayRange,
  },
  {
    text: '后天',
    value: CommonNextNextDayRange,
  },
]

onMounted(() => {
  calcHeight()
})

const calcHeight = () => {
  console.log(window.innerHeight)
  nextTick(() => {
    tableRefHeight.value = window.innerHeight - queryRef.value.offsetHeight - 100 - 63 - 100 - 140 - 30
    tableHeight.value = tableRefHeight.value - 40 - 32 - 24
  })
}

const expend = () => {
  isExpand.value = true
  calcHeight()
}
const collapse = () => {
  isExpand.value = false
  calcHeight()
}

// 查询
const handlerQuery = () => {
  loading.value = true
  queryParams.value.offset = queryParams.value.page * queryParams.value.limit
  queryParams.value.watchStartDate = useDateFormat(queryParams.value.createTime[0], 'YYYYMMDD')
  queryParams.value.watchEndDate = useDateFormat(queryParams.value.createTime[1], 'YYYYMMDD')
  if (queryParams.value.originalStatus && queryParams.value.originalStatus === 'Del') {
    queryParams.value.status = undefined
    queryParams.value.deleted = 1
  } else {
    queryParams.value.status = queryParams.value.originalStatus
    queryParams.value.deleted = undefined
  }
  list(deleteSpaceKey(queryParams.value)).then(res => {
    tableData.value = res.list
    total.value = res.total
    loading.value = false
  })
}

const handlerQueryCount = () => {
  const paramList = formatParam()
  count({ paramList: JSON.stringify(paramList) }).then(res => {
    calcData(res.list)
  })
}

const calcData = data => {
  const todayUavPlan = data.find(a => a.key === 'todayUavPlan').count
  const todayUavFiling = data.find(a => a.key === 'todayUavFiling').count
  const todayUavTemp = data.find(a => a.key === 'todayUavTemp').count
  const todayNoUavPlan = data.find(a => a.key === 'todayNoUavPlan').count
  const todayNoUavFiling = data.find(a => a.key === 'todayNoUavFiling').count
  const todayNoUavTemp = data.find(a => a.key === 'todayNoUavTemp').count
  const nextDayUavPlan = data.find(a => a.key === 'nextDayUavPlan').count
  const nextDayUavFiling = data.find(a => a.key === 'nextDayUavFiling').count
  const nextDayUavTemp = data.find(a => a.key === 'nextDayUavTemp').count
  const nextDayNoUavPlan = data.find(a => a.key === 'nextDayNoUavPlan').count
  const nextDayNoUavFiling = data.find(a => a.key === 'nextDayNoUavFiling').count
  const nextDayNoUavTemp = data.find(a => a.key === 'nextDayNoUavTemp').count
  countInfo.value.today.uav.plan = todayUavPlan
  countInfo.value.today.uav.filing = todayUavFiling
  countInfo.value.today.uav.temp = todayUavTemp
  countInfo.value.today.uav.count = todayUavPlan + todayUavFiling + todayUavTemp
  countInfo.value.today.noUav.plan = todayNoUavPlan
  countInfo.value.today.noUav.filing = todayNoUavFiling
  countInfo.value.today.noUav.temp = todayNoUavTemp
  countInfo.value.today.noUav.count = todayNoUavPlan + todayNoUavFiling + todayNoUavTemp
  countInfo.value.nextDay.uav.plan = nextDayUavPlan
  countInfo.value.nextDay.uav.filing = nextDayUavFiling
  countInfo.value.nextDay.uav.temp = nextDayUavTemp
  countInfo.value.nextDay.uav.count = nextDayUavPlan + nextDayUavFiling + nextDayUavTemp
  countInfo.value.nextDay.noUav.plan = nextDayNoUavPlan
  countInfo.value.nextDay.noUav.filing = nextDayNoUavFiling
  countInfo.value.nextDay.noUav.temp = nextDayNoUavTemp
  countInfo.value.nextDay.noUav.count = nextDayNoUavPlan + nextDayNoUavFiling + nextDayNoUavTemp
}

const formatParam = () => {
  //今日
  const today = getDatePeriod(0)
  const todayUavPlanObj = {
    key: 'todayUavPlan',
    watchStartDate: parseInt(today[0]),
    watchEndDate: parseInt(today[1]),
    aircraftType: 'UAV',
    purpose: 'PLAN',
    status: 1,
  }
  const todayUavFilingObj = {
    key: 'todayUavFiling',
    watchStartDate: parseInt(today[0]),
    watchEndDate: parseInt(today[1]),
    aircraftType: 'UAV',
    purpose: 'FILING',
    status: 1,
  }
  const todayUavTempObj = {
    key: 'todayUavTemp',
    watchStartDate: parseInt(today[0]),
    watchEndDate: parseInt(today[1]),
    aircraftType: 'UAV',
    purpose: 'TEMP',
    status: 1,
  }
  const todayNoUavPlanObj = {
    key: 'todayNoUavPlan',
    watchStartDate: parseInt(today[0]),
    watchEndDate: parseInt(today[1]),
    aircraftType: 'NO_UAV',
    purpose: 'PLAN',
    status: 1,
  }
  const todayNoUavFilingObj = {
    key: 'todayNoUavFiling',
    watchStartDate: parseInt(today[0]),
    watchEndDate: parseInt(today[1]),
    aircraftType: 'NO_UAV',
    purpose: 'FILING',
    status: 1,
  }
  const todayNoUavTempObj = {
    key: 'todayNoUavTemp',
    watchStartDate: parseInt(today[0]),
    watchEndDate: parseInt(today[1]),
    aircraftType: 'NO_UAV',
    purpose: 'TEMP',
    status: 1,
  }
  //明日
  const nextDay = getDatePeriod(4)
  const nextDayUavPlanObj = {
    key: 'nextDayUavPlan',
    watchStartDate: parseInt(nextDay[0]),
    watchEndDate: parseInt(nextDay[1]),
    aircraftType: 'UAV',
    purpose: 'PLAN',
    status: 1,
  }
  const nextDayUavFilingObj = {
    key: 'nextDayUavFiling',
    watchStartDate: parseInt(nextDay[0]),
    watchEndDate: parseInt(nextDay[1]),
    aircraftType: 'UAV',
    purpose: 'FILING',
    status: 1,
  }
  const nextDayUavTempObj = {
    key: 'nextDayUavTemp',
    watchStartDate: parseInt(nextDay[0]),
    watchEndDate: parseInt(nextDay[1]),
    aircraftType: 'UAV',
    purpose: 'TEMP',
    status: 1,
  }
  const nextDayNoUavPlanObj = {
    key: 'nextDayNoUavPlan',
    watchStartDate: parseInt(nextDay[0]),
    watchEndDate: parseInt(nextDay[1]),
    aircraftType: 'NO_UAV',
    purpose: 'PLAN',
    status: 1,
  }
  const nextDayNoUavFilingObj = {
    key: 'nextDayNoUavFiling',
    watchStartDate: parseInt(nextDay[0]),
    watchEndDate: parseInt(nextDay[1]),
    aircraftType: 'NO_UAV',
    purpose: 'FILING',
    status: 1,
  }
  const nextDayNoUavTempObj = {
    key: 'nextDayNoUavTemp',
    watchStartDate: parseInt(nextDay[0]),
    watchEndDate: parseInt(nextDay[1]),
    aircraftType: 'NO_UAV',
    purpose: 'TEMP',
    status: 1,
  }

  return [
    { ...todayUavPlanObj },
    { ...todayUavFilingObj },
    { ...todayUavTempObj },
    { ...todayNoUavPlanObj },
    { ...todayNoUavFilingObj },
    { ...todayNoUavTempObj },
    { ...nextDayUavPlanObj },
    { ...nextDayUavFilingObj },
    { ...nextDayUavTempObj },
    { ...nextDayNoUavPlanObj },
    { ...nextDayNoUavFilingObj },
    { ...nextDayNoUavTempObj },
  ]
}

// 重置
const handlerReset = () => {
  queryParams.value = {
    page: 0,
    aircraftCodeLike: undefined,
    purpose: undefined,
    source: undefined,
    memoLike: undefined,
    status: undefined,
    originalStatus: undefined,
    deleteStatus: undefined,
    createTime: CommonNext3DayRange,
    limit: queryParams.value.limit,
  }
  handlerQuery()
}

const handlerAdd = () => {
  const p = {
    aircraftType: 'UAV',
    source: '华航信',
    status: 1,
  }
  dialogRef.value.onOpen(p)
}
const handlerEdit = row => {
  row.watchDate = formatDate(row.watchStartDate)
  dialogRef.value.onOpen(row)
}

const changeStatus = (row, status) => {
  row.status = status
  update(row).then(() => {
    ElMessage({
      message: '操作成功',
      type: 'success',
    })
    refresh(false)
  })
}

const deleteData = id => {
  del({ idList: JSON.stringify([id]) }).then(() => {
    ElMessage({
      message: '删除成功',
      type: 'success',
    })
    refresh(false)
  })
}

const recoverData = id => {
  recover({ idList: JSON.stringify([id]) }).then(() => {
    ElMessage({
      message: '恢复成功',
      type: 'success',
    })
    refresh(false)
  })
}

const refresh = (isResetTime = true) => {
  if (isResetTime) queryParams.value.createTime = CommonNext3DayRange
  handlerQuery()
  handlerQueryCount()
}

refresh()
</script>

<style scoped>
.focus {
  width: 100%;
  display: flex;
  flex-direction: column;

  .common-query {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 24px 24px 6px 24px;
    margin-bottom: 24px;
  }

  .common-container {
    width: 100%;
    height: 100%;
  }

  .common-container .el-input {
    --el-input-width: 100%;
  }

  .common-container .el-select {
    --el-select-width: 100%;
  }

  .common-statistics-card {
    border-radius: 8px;
    margin-bottom: 24px;
  }

  .common-query {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 24px 24px 6px 24px;
    margin-bottom: 24px;
  }

  .common-query .el-form-item {
    width: 100%;
  }

  .common-table {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 8px 20px 0 rgba(0, 0, 0, 0.02);
  }

  .common-form-label {
    font-weight: 400;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.9);
    margin-right: 12px;
    text-align: right;
    cursor: default;
  }

  .common-form-label:before {
    color: var(--el-color-danger);
    content: '*';
    margin-right: 4px;
  }
}
</style>
