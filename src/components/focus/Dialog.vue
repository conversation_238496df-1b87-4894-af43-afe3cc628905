<template>
  <el-dialog
    v-model="open"
    :title="`${data.id ? '编辑' : '新增'}重点关注`"
    width="1080"
    :align-center="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :show-close="false"
  >
    <Form :form-data="data" @on-close="onClose" />
  </el-dialog>
</template>

<script setup>
import Form from '@/components/focus/Form.vue'

const open = ref(false)
const data = ref({})

const onOpen = row => {
  open.value = true
  data.value = { ...row }
}

const emit = defineEmits(['refresh'])
const onClose = () => {
  open.value = false
  emit('refresh')
}

defineExpose({
  onOpen,
})
</script>

<style scoped lang="less"></style>
