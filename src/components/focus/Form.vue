<template>
  <el-form
    ref="formRef"
    :model="data"
    label-width="132px"
    :inline="true"
    label-position="top"
    class="dialog-form"
    :rules="rules"
  >
    <el-row :gutter="24">
      <el-col :span="6">
        <el-form-item label="序列号或标牌号" prop="aircraftCode" style="width: 100%">
          <el-input v-model="data.aircraftCode" maxlength="24" show-word-limit placeholder="请输入" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="航空器类型" prop="aircraftType" style="width: 100%">
          <el-select v-model="data.aircraftType" placeholder="请选择">
            <el-option label="无人机" value="UAV" />
            <el-option label="非无人机" value="NO_UAV" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="目的" prop="purpose" style="width: 100%">
          <el-select v-model="data.purpose" placeholder="请选择">
            <el-option label="审批监视" value="PLAN" />
            <el-option label="备案监视" value="FILING" />
            <el-option label="临时关注" value="TEMP" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="来源" prop="source" style="width: 100%">
          <el-select v-model="data.source" placeholder="请选择" disabled>
            <el-option label="华航信" value="华航信" />
            <el-option label="电信巡龙" value="电信巡龙" />
            <el-option label="万丰" value="万丰" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="关注日期" prop="watchDate" style="width: 100%">
          <el-date-picker
            v-model="data.watchDate"
            type="date"
            format="YYYY-MM-DD"
            date-format="YYYY-MM-DD"
            placeholder="请选择"
            style="width: 100%"
            :clearable="false"
            :disabled-date="disabledDate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="是否启用" prop="status" style="width: 100%">
          <el-select v-model="data.status" placeholder="请选择">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="2" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="memo" style="width: 100%">
          <el-input v-model="data.memo" placeholder="请输入" maxlength="100" show-word-limit style="width: 100%" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <div class="dialog-form-footer">
    <el-button @click="close">取消</el-button>
    <el-button type="primary" @click="save">保存</el-button>
  </div>
</template>

<script setup>
import { addFocus, update } from '@/request/api/focus.js'
import { ElMessage } from 'element-plus'
import { useDateFormat } from '@vueuse/core'
const { formData = {} } = defineProps({
  formData: {
    type: Object,
  },
})
const data = ref(formData)
const rules = {
  aircraftCode: [
    {
      required: true,
      message: '序列号或标牌号不能为空',
      trigger: 'blur',
    },
  ],
  aircraftType: [
    {
      required: true,
      message: '航空器类型不能为空',
      trigger: 'change',
    },
  ],
  purpose: [
    {
      required: true,
      message: '目的不能为空',
      trigger: 'change',
    },
  ],
  source: [
    {
      required: true,
      message: '来源不能为空',
      trigger: 'change',
    },
  ],
  watchDate: [
    {
      required: true,
      message: '关注日期不能为空',
      trigger: 'change',
    },
  ],
  status: [
    {
      required: true,
      message: '是否启用不能为空',
      trigger: 'change',
    },
  ],
}

const disabledDate = time => {
  // 获取今天的日期（时间部分设为0:0:0）
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // 如果时间早于今天，则禁用
  return time.getTime() < today.getTime()
}

const formRef = useTemplateRef('formRef')

const emit = defineEmits(['onClose'])
const save = () => {
  formRef.value.validate(valid => {
    data.value.watchStartDate = useDateFormat(data.value.watchDate, 'YYYYMMDD').value
    data.value.watchEndDate = useDateFormat(data.value.watchDate, 'YYYYMMDD').value
    if (valid) {
      if (data.value.id)
        update(data.value).then(() => {
          ElMessage({
            message: '修改成功',
            type: 'success',
          })
          emit('onClose')
        })
      else
        addFocus(data.value).then(() => {
          ElMessage({
            message: '新增成功',
            type: 'success',
          })
          emit('onClose')
        })
    }
  })
}

const close = () => {
  emit('onClose')
}
</script>

<style scoped lang="less">
.dialog-form {
  max-height: 70vh;
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: 64px;
}

.dialog-form-footer {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 2;
  background-color: white;
  height: 48px;
  width: calc(100% - 16px);
  text-align: right;
  padding: 16px 16px 0 0;
  border-radius: 8px;
}
</style>
