<template>
  <div class="custom">
    <div class="navigation">
      <div class="title">{{ paintTitle }}</div>
    </div>
    <div class="search">
      <div class="card">
        <el-row>
          <el-col :span="6">
            <div class="line">
              <div class="label">规则名称</div>
              <div class="in">
                <el-input v-model="tableConfig.search.ruleNameLike" placeholder="请输入规则名称模糊查询"></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">告警类型</div>
              <div class="in">
                <el-select v-model="alarm.typeValue" placeholder="请选择" @change="getAlarm">
                  <el-option
                    v-for="item in alarm.typeGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">规则告警等级</div>
              <div class="in">
                <el-select v-model="alarm.levelValue" multiple clearable @change="gerAlarmLevel" placeholder="请选择">
                  <el-option
                    v-for="item in alarm.levelGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">所属地区</div>
              <div class="in">
                <AreaSelect v-model="tableConfig.cityCodeList" @change="getCity" />
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">规则主体类型</div>
              <div class="in">
                <el-select
                  v-model="tableConfig.search.subjectType"
                  placeholder="请选择规则主体类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in subject.typeGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">规则主体名称</div>
              <div class="in">
                <el-input
                  v-model="tableConfig.search.subjectNameLike"
                  placeholder="请输入规则主体名称模糊查询"
                ></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">默认规则</div>
              <div class="in">
                <el-select
                  v-model="defaultRuleSearch.defaultRuleValue"
                  placeholder="请选择"
                  style="width: 100%"
                  @change="setDefaultRule"
                >
                  <el-option
                    v-for="item in defaultRuleSearch.group"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="operate">
              <div class="btn n" @click="search">查询</div>
              <div class="btn d" @click="reset">重置</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="content">
      <div class="card">
        <div class="filter">
          <div class="operate">
            <div class="btn n" @click="add">新增</div>
            <div class="btn d" @click="del">删除</div>
          </div>
        </div>
        <Table
          v-model:config="tableConfig"
          v-model:reload="reload"
          @edit="edit"
          @select="selectTb"
          @active="active"
        ></Table>
      </div>
    </div>

    <el-dialog v-model="defaultRule.show" title="编辑默认规则白名单" width="640" top="1vh" @close="defaultRuleReset">
      <div class="management dialog">
        <el-row>
          <el-col :span="24">
            <div class="line">
              <div class="title">白名单选择</div>
              <div class="in">
                <el-select
                  v-model="defaultRule.whiteListValue"
                  multiple
                  collapse-tags
                  placeholder="请选择"
                  style="width: 100%"
                  size="large"
                  @change="getWhiteList"
                >
                  <el-option
                    v-for="item in defaultRule.whiteListGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-col>
          <div class="tableData">
            <el-table style="width: 100%; height: 200px" :data="defaultRule.tableData">
              <el-table-column prop="label" label="名称" />
              <el-table-column label="操作">
                <template #default="scope">
                  <div class="delete" @click="whiteListDelete(scope.row)">删除</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-row>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn c" @click="defaultRuleReset">取消</div>
          <div class="btn p" @click="whiteListSubmit">确定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
// 组件
import Table from '@/components/common/Table.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as common from '@/utils/common.js'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import * as api from '@/request/api'
import userOrg from '@/hooks/useOrg.js'
import AreaSelect from '@/components/common/areaSelect/AreaSelect.vue'
const { cityCode } = userOrg()
// 标题
const { pathData, setPath } = useComponent()
const paintTitle = ref(pathData.value.name)
/** 表格初始化 **/
const tableConfig = reactive({
  api: 'rule_aggregate_list',
  select: true,
  pagination: true,
  active: true,
  operate: 1,
  area: undefined,
  cityCodeList: [],
  search: {
    ruleType: 'STATIC',
    cityCodeList: undefined,
    ruleNameLike: undefined,
    defaultRule: undefined,
    flyRestrict: undefined,
    subjectType: undefined,
    subjectNameLike: undefined,
    isEnterAlarm: undefined,
    isLeaveAlarm: undefined,
    isHeightAlarm: undefined,
    deleted: 0,
    alarmLevelList: JSON.stringify([0]),
  },
  column: [
    {
      label: '名称',
      width: 230,
      template: true,
      type: 1,
    },
    {
      prop: 'cityName',
      label: '地区',
      template: false,
      formatter: v => {
        return v.defaultRule === 1 ? '-' : v.cityName
      },
    },
    {
      prop: 'subjectType',
      label: '规则主体类型',
      template: false,
      formatter: v => {
        return common.getSubjectTypeName(v.subjectTypeList[0])
      },
    },
    {
      prop: 'subjectType',
      width: 230,
      label: '规则主体名称',
      template: false,
      formatter: v => {
        return v.subjectNameList[0]
      },
    },
    {
      prop: 'alarmTypeName',
      label: '告警类型',
      template: false,
      formatter: v => {
        let n
        if (v.enterAlarmLevel > 0) {
          n = '靠近告警'
        } else if (v.leaveAlarmLevel > 0) {
          n = '飞离告警'
        } else if (v.heightAlarmLevel > 0) {
          n = '高度告警'
        }
        return n
      },
    },
    {
      prop: 'alarmTypeLevel',
      label: '规则告警等级',
      template: true,
      type: 4,
    },
    {
      prop: 'bufferWidth',
      label: '缓冲区(米)',
      template: false,
      formatter: v => {
        return v.heightAlarmLevel > 0 ? '-' : v.bufferWidth + '米'
      },
    },
    {
      prop: 'bufferAlarm',
      label: '缓冲区告警等级',
      template: true,
      type: 5,
    },
    {
      prop: 'defaultRule',
      label: '是否默认规则',
      template: false,
      formatter: v => {
        return v.defaultRule === 1 ? '是' : '否'
      },
    },
    {
      prop: 'active',
      label: '激活',
      template: true,
      type: 3,
    },
  ],
})
const reload = ref(0)

/** 查询 **/
const search = () => {
  reload.value = Math.random()
}

/** 主体 **/
const subject = reactive({
  typeGroup: [
    {
      label: '空域',
      value: 'AIRSPACE',
    },
    {
      label: '航路',
      value: 'LA_AIRWAY',
    },
    {
      label: '航线',
      value: 'UA_AIRLINE',
    },
    {
      label: '障碍物',
      value: 'OBSTACLE',
    },
  ],
})

/** 告警 **/
const alarm = reactive({
  typeGroup: [
    {
      label: '靠近告警',
      value: 'isEnterAlarm',
    },
    {
      label: '飞离告警',
      value: 'isLeaveAlarm',
    },
    {
      label: '高度告警',
      value: 'isHeightAlarm',
    },
  ],
  typeValue: undefined,
  levelGroup: [
    {
      label: '1级',
      value: 1,
    },
    {
      label: '2级',
      value: 2,
    },
    {
      label: '3级',
      value: 3,
    },
    {
      label: '4级',
      value: 4,
    },
  ],
  levelValue: undefined,
})

/** 所属地区 **/
const getCity = o => {
  let c = []
  o.map(item => {
    c.push(item.value)
  })
  tableConfig.search.cityCodeList = JSON.stringify(c)
}

/** 是否默认规则 **/
const defaultRuleSearch = reactive({
  group: [
    {
      label: '全部',
      value: '2',
    },
    {
      label: '是',
      value: '1',
    },
    {
      label: '否',
      value: '0',
    },
  ],
  defaultRuleValue: '2',
})

const setDefaultRule = o => {
  switch (o) {
    case '0':
      tableConfig.search.defaultRule = 0
      break
    case '1':
      tableConfig.search.defaultRule = 1
      break
    case '2':
      tableConfig.search.defaultRule = undefined
      break
  }
}

// 类型
const getAlarm = o => {
  tableConfig.search.isLeaveAlarm = 0
  tableConfig.search.isEnterAlarm = 0
  tableConfig.search.isHeightAlarm = 0
  tableConfig.search[o] = 1
}
// 等级
const gerAlarmLevel = o => {
  tableConfig.search.alarmLevelList = JSON.stringify(o)
}

const reset = () => {
  if (cityCode.value === '330600') tableConfig.cityCodeList = undefined
  tableConfig.search.ruleNameLike = undefined
  alarm.typeValue = undefined
  alarm.levelValue = undefined
  tableConfig.area = undefined
  tableConfig.search.defaultRule = undefined
  defaultRuleSearch.defaultRuleValue = '2'
  tableConfig.search.subjectNameLike = undefined
  tableConfig.search.cityCodeList = undefined
  tableConfig.search.subjectType = undefined
  tableConfig.search.isEnterAlarm = undefined
  tableConfig.search.isLeaveAlarm = undefined
  tableConfig.search.isHeightAlarm = undefined
  tableConfig.search.alarmLevelList = JSON.stringify([0])
}

/** 默认规则白名单 **/
const defaultRule = reactive({
  show: false,
  whiteListGroup: [],
  whiteListValue: [],
  tableData: [],
  ruleId: '',
})
searchWhiteList()
// 查询白名单
function searchWhiteList() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 10,
  }
  api.whitelist_aggregate(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      defaultRule.whiteListGroup = []
      d.map(item => {
        defaultRule.whiteListGroup.push({
          id: item.id,
          value: item.id,
          label: item.whitelistName,
        })
      })
    }
  })
}

const getWhiteList = o => {
  defaultRule.tableData = []
  defaultRule.tableData = defaultRule.whiteListGroup.filter(item => o.includes(item.id))
}
const defaultRuleReset = () => {
  defaultRule.tableData = []
  defaultRule.whiteListValue = []
  defaultRule.show = false
}

const whiteListDelete = o => {
  const index = defaultRule.tableData.findIndex(item => item.value === o.value)
  defaultRule.whiteListValue.splice(index, 1)
  defaultRule.tableData.splice(index, 1)
}
//
const whiteListSubmit = () => {
  let p = {
    rule_id: defaultRule.ruleId,
    rule_tags: JSON.stringify(['wCUCIxMbaNT4WihNjXOox']),
    whitelist_idList: JSON.stringify(defaultRule.whiteListValue),
  }
  api.rule_update(p).then(() => {
    ElMessage({
      message: t('message.saveSuccess'),
      type: 'success',
    })
    setTimeout(() => {
      defaultRuleReset()
      reload.value = Math.random()
    }, common.globalTime())
  })
}

/** 操作 **/
// 编辑
const edit = o => {
  if (o.defaultRule) {
    if (o.whitelistList) {
      o.whitelistList.map(item => {
        defaultRule.whiteListValue.push(item.id)
        defaultRule.tableData.push({
          id: item.id,
          label: item.whitelistName,
          value: item.id,
        })
      })
    }
    defaultRule.ruleId = o.id
    defaultRule.show = true
  } else {
    setPath(
      'rule/common/Detail.vue',
      { type: tableConfig.search.ruleType, id: o.id, status: o.status },
      { type: PathType.edit },
    )
  }
}

// 激活
const active = o => {
  let status = o.activeStatus ? 1 : 2
  api.rule_status_update({ id: o.id, status: status }).then(() => {
    if (o.activeStatus) {
      ElMessage({
        message: o.ruleName + '已激活',
        type: 'warning',
      })
    } else {
      ElMessage({
        message: o.ruleName + '已停用',
        type: 'warning',
      })
    }
  })
}
/** 新增 **/
const add = () => {
  setPath?.('rule/common/Detail.vue', { type: tableConfig.search.ruleType }, { type: PathType.add })
}
/** 删除 **/
let selectData = []
let ids = []
const selectTb = o => {
  selectData = []
  o.map(item => {
    selectData.push(item)
    ids.push(item.id)
  })
}
const del = () => {
  if (selectData.length === 0) {
    ElMessage.error(t('message.selectError'))
  } else {
    let hasDefault = false
    let hasSubject = false
    let hasWhiteList = false
    let hasBoth = false
    for (const item of selectData) {
      hasDefault = item.defaultRule === 1
      const subjectLength = item.subjectNameList ? item.subjectNameList.length : 0
      const whiteListLength = item.whitelistList ? item.whitelistList.length : 0
      if (subjectLength > 0 && whiteListLength > 0) {
        hasBoth = true
      } else if (subjectLength > 0) {
        hasSubject = true
      } else if (whiteListLength > 0) {
        hasWhiteList = true
      }
    }
    if (hasDefault) {
      ElMessage.error(t('message.defaultDeleteError'))
      return
    }
    let cn = ''
    if (hasBoth) {
      cn = '当前规则已关联主体和白名单, 是否确认删除'
    } else if (hasSubject) {
      cn = '当前规则已关联主体，是否确认删除'
    } else if (hasWhiteList) {
      cn = '当前规则已关联白名单，是否确认删除'
    } else if (!hasBoth && !hasSubject && !hasWhiteList) {
      cn = '是否确定删除当前规则'
    }
    ElMessageBox.confirm(cn, '警告', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        api.rule_delete({ idList: JSON.stringify(ids) }).then(() => {
          ElMessage({
            message: t('message.deleteSuccess'),
            type: 'success',
          })
          setTimeout(() => {
            reload.value = Math.random()
            ids = []
          }, common.globalTime())
        })
      })
      .catch(() => {})
  }
}
</script>

<style>
@import '../common/custom.css';
</style>
<style scoped lang="less">
.ignore-custom {
  background: #00b046;
  height: v-bind(height);
  width: 100%;
}
</style>
