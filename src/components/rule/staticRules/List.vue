<template>
  <div class="custom">
    <div class="navigation">
      <div class="title">{{ paintTitle }}</div>
    </div>
    <div class="search">
      <div class="card">
        <el-row>
          <el-col :span="6">
            <div class="line">
              <div class="label">规则名称</div>
              <div class="in">
                <el-input placeholder="请输入规则名称模糊查询" v-model="tableConfig.search.ruleNameLike"></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">飞行限制</div>
              <div class="in">
                <el-select v-model="tableConfig.search.flyRestrict" placeholder="请选择">
                  <el-option v-for="item in flyRestrict" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="operate">
              <div class="btn n" @click="search">查询</div>
              <div class="btn d" @click="reset">重置</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="content">
      <div class="card">
        <div class="filter">
          <div class="operate">
            <div class="btn n" @click="add">新增</div>
            <div class="btn d" @click="del">删除</div>
          </div>
        </div>
        <Table
          v-model:config="tableConfig"
          @edit="edit"
          @select="selectTb"
          @active="active"
          v-model:reload="reload"
        ></Table>
      </div>
    </div>
  </div>
</template>

<script setup>
// 组件
import Table from '@/components/common/Table.vue'
const { size } = defineProps(['size'])
import { ElMessage, ElMessageBox } from 'element-plus'
import * as common from '@/utils/common.js'
import useComponent, { PathType } from '@/hooks/useComponent.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import * as api from '@/request/api'
// 数据块尺寸
// 标题
const { pathData, setPath } = useComponent()
const paintTitle = ref(pathData.value.name)
/** 表格初始化 **/
const tableConfig = reactive({
  api: 'rule_list',
  select: true,
  pagination: true,
  active: true,
  operate: 1,
  search: {
    ruleType: 'STATIC',
    ruleNameLike: undefined,
    flyRestrict: undefined,
    defaultRule: 0,
    limit: 10,
    offset: 0,
    deleted: 0,
  },
  column: [
    {
      prop: 'ruleName',
      label: '名称',
      template: true,
    },
    {
      prop: 'flyRestrict',
      label: '飞行限制',
      template: false,
      formatter: v => {
        return common.getFlyRestrictName(v.flyRestrict)
      },
    },
    {
      prop: 'alarmTypeName',
      label: '告警类型',
      template: false,
      formatter: v => {
        let n = ''
        if (v.enterAlarmLevel > 0) {
          n = '靠近告警'
        } else if (v.leaveAlarmLevel > 0) {
          n = '离开告警'
        } else {
          n = '高度告警'
        }
        return n
      },
    },
    {
      prop: 'alarmTypeLevel',
      label: '告警等级',
      template: false,
      formatter: v => {
        let n = ''
        if (v.enterAlarmLevel > 0) {
          n = v.enterAlarmLevel
        } else if (v.leaveAlarmLevel > 0) {
          n = v.leaveAlarmLevel
        } else {
          n = v.heightAlarmLevel
        }
        return n
      },
    },
    {
      prop: 'bufferWidth',
      label: '缓冲区(米)',
      template: true,
    },
    {
      prop: 'defaultRule',
      label: '默认规则',
      template: false,
      formatter: v => {
        return v.defaultRule === 1 ? '是' : '否'
      },
    },
    {
      prop: 'active',
      label: '激活',
      template: true,
      type: 3,
    },
  ],
})
const reload = ref(0)
/** 查询 **/
const search = () => {
  reload.value = Math.random()
}
const reset = () => {
  tableConfig.search.ruleNameLike = undefined
}

// 飞行限制
const flyRestrict = ref([
  {
    label: '适飞',
    value: 'PERMITTED',
  },
  {
    label: '管制',
    value: 'CONTROLLED',
  },
  {
    label: '禁飞',
    value: 'FORBIDDEN',
  },
])

/** 操作 **/
// 编辑
const edit = o => {
  setPath('rule/common/Detail.vue', { type: tableConfig.search.ruleType, id: o.id }, PathType.edit)
}
// 激活
const active = () => {}
/** 新增 **/
const add = () => {
  setPath?.('rule/common/Detail.vue', { type: tableConfig.search.ruleType }, { type: PathType.add })
}
/** 删除 **/
let selectData = []
const selectTb = o => {
  selectData = []
  o.map(item => {
    selectData.push(item.id)
  })
  console.log(selectData)
}
const del = () => {
  let reload = ''
  if (selectData.length === 0) {
    ElMessage.error(t('message.selectError'))
  } else {
    ElMessageBox.confirm('是否确定删除当前规则', '警告', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        api.rule_delete({ idList: JSON.stringify(selectData) }).then(data => {
          ElMessage({
            message: t('message.deleteSuccess'),
            type: 'success',
          })
          setTimeout(() => {
            reload.value = Math.random()
          }, common.globalTime())
        })
      })
      .catch(() => {})
  }
}
</script>

<style>
@import '../common/custom.css';
</style>
<style scoped lang="scss">
.ignore-custom {
  background: #00b046;
  height: v-bind(height);
  width: 100%;
}
</style>
