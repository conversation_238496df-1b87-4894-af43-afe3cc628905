<template>
  <div class="detail" v-loading="formContent.loading">
    <!-- 标题-->
    <CommonTitle isBack>
      <template #default>
        <div class="opearte">
          <el-button type="primary" @click="submit">提交</el-button>
        </div>
      </template>
    </CommonTitle>

    <div class="paint">
      <div class="card">
        <el-row class="mapRow">
          <el-col :span="18">
            <!--            <div style="background: #2c405a; height: 100%;"></div>-->
            <rule-map v-model:mapData="mapData" />
          </el-col>
          <el-col :span="6">
            <div class="piece">
              <div class="ignore-scroll">
                <div class="line mbg">
                  <div class="title w">静态规则名称</div>
                  <div class="in">
                    <el-input placeholder="请输入" size="large"></el-input>
                  </div>
                </div>
                <div class="line mbg">
                  <div class="title w">飞行限制</div>
                  <div class="in">
                    <el-select
                      v-model="formContent.param.rule_flyRestrict"
                      placeholder="请选择"
                      size="large"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in flyRestrict.g"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        :disabled="item.disabled"
                      />
                    </el-select>
                  </div>
                </div>
                <div class="line mbg" v-if="ruleType.show">
                  <div class="title w">规则类型</div>
                  <div class="in mbg">
                    <div class="wrap col4 pdr">
                      <el-select
                        v-model="ruleType.typeValue"
                        placeholder="请选择规则"
                        size="large"
                        style="width: 100%"
                        clearable
                        @change="getRuleType"
                        @clear="clearRuleType"
                      >
                        <el-option
                          v-for="item in ruleType.typeGroup"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          :disabled="item.disabled"
                        />
                      </el-select>
                    </div>
                    <div class="wrap col6">
                      <el-cascader
                        v-model="formContent.subjectIds"
                        placeholder="请选择地图模型"
                        size="large"
                        :options="ruleType.geometryGroup"
                        :props="ruleType.props"
                        collapse-tags
                        @change="getGeometry"
                        :disabled="ruleType.lock"
                      />
                    </div>
                  </div>
                  <div v-for="(item, index) in ruleType.list" :key="index" class="in note">
                    <div class="wrap">{{ item.note }}</div>
                    <div class="wrap" @click="deleteRuleType(item, index)">
                      <EIcon name="CircleClose" style="height: 31px" />
                    </div>
                  </div>
                </div>
                <div class="line mbg">
                  <div class="title w">告警等级</div>
                  <div class="in">
                    <el-select
                      v-model="warning.value"
                      placeholder="请选择"
                      size="large"
                      style="width: 100%"
                      @change="getWarningLevel"
                    >
                      <el-option
                        v-for="item in warning.group"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                </div>
                <div class="line mbg">
                  <div class="title">白名单</div>
                  <div class="in mbg">
                    <el-select
                      v-model="formContent.whiteList"
                      size="large"
                      multiple
                      collapse-tags
                      style="width: 100%"
                      @change="selectWhiteList"
                      placeholder="请选择"
                    >
                      <el-option v-for="item in whiteList.g" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                  <div v-for="(item, index) in whiteList.noteGroup" :key="index" class="in note">
                    <div class="wrap">{{ item.label }}</div>
                    <div class="wrap" @click="deleteWhiteList(item, index)">
                      <EIcon name="CircleClose" style="height: 31px" />
                    </div>
                  </div>
                </div>
                <div class="line mbg">
                  <div class="title">规则简介</div>
                  <div class="in">
                    <el-input type="textarea" placeholder="请输入" :rows="5" />
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
const { t } = useI18n()
// 组件
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
import AirSpaceType from '@/components/common/AirSpaceType.vue'
import RuleMap from '@/components/map/ShowMap.vue'
const { size } = defineProps(['size'])
console.log(size.height)
const ifAirport = ref(false)
import * as api from '@/request/api'
// 尺寸
import useComponent, { PathType } from '@/hooks/useComponent.js'
import * as common from '@/utils/common'
import useOrg from '@/hooks/useOrg.js'
import { airspace_aggregate_list, obstacle_list } from '@/request/api'
// 跳转
const { setPathBack, setPath, pathData, getParam } = useComponent()
// 地图
const mapData = reactive({
  displayStyle: {},
  geometries: [],
  abnormalWidth: 0,
  geoJson: [],
})
// 表单内容
const formContent = reactive({
  param: {
    rule_ruleName: '',
    subject_subjectList: '',
    rule_flyRestrict: '',
    rule_defaultRule: 0,
    rule_enterAlarmLevel: 0,
    rule_leaveAlarmLevel: 0,
    rule_heightAlarmLevel: 0,
    rule_bufferAlarmLevel: 0,
    rule_bufferWidth: 0,
    rule_startTime: -1,
    rule_endTime: -1,
    rule_memo: '',
    rule_status: 1,
    rule_botHeight: 0,
    rule_topHeight: 0,
  },
  subjectList: [],
  loading: false,
  subjectIds: [],
  editType: 'add',
  whiteList: '',
  alarmType: '',
  alarmLock: true,
  vaildTime: '',
  defaultRule: false,
})
const height = computed(() => size.height - 100 + 'px')
/** 初始化 **/
initDate()
function initDate() {}

/** 飞行限制 **/
const flyRestrict = reactive({
  g: [
    {
      label: '适飞',
      value: 'PERMITTED',
    },
    {
      label: '管制',
      value: 'CONTROLLED',
    },
    {
      label: '禁飞',
      value: 'FORBIDDEN',
    },
  ],
})

/** 告警 **/
// 告警等级
const warning = reactive({
  group: [
    {
      value: 4,
      label: '4级',
    },
    {
      value: 3,
      label: '3级',
    },
    {
      value: 2,
      label: '2级',
    },
    {
      value: 1,
      label: '1级',
    },
  ],
  value: '',
})
// 规则对应告警等级
const getWarningLevel = o => {
  if (formContent.alarmType === 'enter') {
    formContent.param.rule_enterAlarmLevel = o
  } else if (formContent.alarmType === 'leave') {
    formContent.param.rule_leaveAlarmLevel = o
  } else if (formContent.alarmType === 'height') {
    formContent.param.rule_heightAlarmLevel = o
  }
}

/** 规则类型 **/
const ruleType = reactive({
  show: false,
  typeGroup: [
    {
      value: 0,
      label: '靠近限制',
      disabled: false,
    },
    {
      value: 1,
      label: '飞离限制',
      disabled: false,
    },
    {
      value: 2,
      label: '高度限制',
      disabled: false,
    },
  ],
  typeValue: '',
  typeNote: '',
  lock: true,
  props: { multiple: true },
  geometryGroup: [
    {
      value: 1,
      label: '空域',
      children: [],
    },
    {
      value: 2,
      label: '航路',
      children: [],
    },
    {
      value: 3,
      label: '航线',
      children: [],
    },
    {
      value: 4,
      label: '障碍物-点',
      children: [],
    },
    {
      value: 5,
      label: '障碍物-线',
      children: [],
    },
    {
      value: 6,
      label: '障碍物-面',
      children: [],
    },
  ],
  list: [],
})

searchObstacle_list()
// 查询障碍物
function searchObstacle_list() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 999,
  }
  api.obstacle_list(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      d.map(item => {
        let typeNum = 3
        if (item.kind === 'POINT') {
          typeNum = 3
        } else if (item.kind === 'LINE') {
          typeNum = 4
        } else if (item.kind === 'PLANE') {
          typeNum = 5
        }
        ruleType.geometryGroup[typeNum].children.push({
          value: item.id,
          label: item.name,
        })
      })
    }
  })
}
// 查询空域
searchAirSpace()
function searchAirSpace() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 999,
  }

  api.airspace_aggregate_list(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      d.map(item => {
        ruleType.geometryGroup[0].children.push({
          value: item.id,
          label: item.airspaceName,
        })
      })
    }
  })
}
// 查询航路
function searchLa_airway() {}
// 查询航线
searchLine()
function searchLine() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 999,
  }
  api.airline_aggregate_list(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      d.map(item => {
        ruleType.geometryGroup[2].children.push({
          value: item.id,
          label: item.airlineTitle,
        })
      })
      ruleType.show = true
    }
  })
}
// 获取规则类型
const getRuleType = o => {
  if (typeof o === 'undefined') return
  ruleType.typeGroup.map((item, index) => {
    if (index === o) {
      item.disabled = false
    } else {
      item.disabled = true
    }
  })
  let x = ruleType.typeGroup.filter(item => item.value === o)
  ruleType.typeNote = x[0].label
  if (o === 0) {
    formContent.alarmType = 'enter'
  } else if (o === 1) {
    formContent.alarmType = 'leave'
  } else if (o === 2) {
    formContent.alarmType = 'height'
  }
  ruleType.lock = false
  formContent.alarmLock = false
  console.log(ruleType.geometryGroup)
}
// 获取地点主体信息
const getGeometry = o => {
  console.log(o)
  let note = []
  ruleType.list = []
  if (o.length === 0) {
    clearRuleType(true)
  } else {
    ruleType.geometryGroup.map(item => {
      if (item.value === o[0][0]) {
        const oids = new Set(o.map(subArr => subArr[1]))
        note = item.children.filter(item => oids.has(item.value))
        item.disabled = false
      } else {
        item.disabled = true
      }
    })
    note.map(item => {
      ruleType.list.push({ note: ruleType.typeNote + ':' + item.label, id: item.value })
    })
    formContent.subjectList = []
    o.map(item => {
      // 障碍物
      if (item[0] === 4 || item[0] === 5 || item[0] === 6) {
        formContent.subjectList.push({
          subjectType: 'PLACE',
          subjectId: item[1][0],
        })
        // 空域
      } else if (item[0] === 1) {
        formContent.subjectList.push({
          subjectType: 'AIRSPACE',
          subjectId: item[1][0],
        })
        // 航路
      } else if (item[0] === 2) {
        formContent.subjectList.push({
          subjectType: 'UA_AIRLINEX',
          subjectId: item[1][0],
        })
        // 空域
      } else if (item[0] === 3) {
        formContent.subjectList.push({
          subjectType: 'UA_AIRLINE',
          subjectId: item[1][0],
        })
      }
    })
  }
}
// 清除规则类型
function clearRuleType(x) {
  ruleType.geometryGroup.map(item => {
    item.disabled = false
  })
  ruleType.typeGroup.map(item => {
    item.disabled = false
  })
  ruleType.list = []
  formContent.subjectId = ''
  formContent.subjectList = []
  formContent.subjectIds = []
  ruleType.typeValue = null
  warning.value = ''
  formContent.alarmLock = true
  ruleType.lock = true
  formContent.alarmType = ''
  formContent.param.rule_heightAlarmLevel = 0
  formContent.param.rule_leaveAlarmLevel = 0
  formContent.param.rule_enterAlarmLevel = 0
}
// 删除单项生成规则数据
const deleteRuleType = (item, index) => {
  formContent.subjectIds = formContent.subjectIds.filter(its => item.id[0] !== its[1][0])
  formContent.subjectList = formContent.subjectList.filter(its => item.id[0] !== its.subjectId)
  mapData.ids = mapData.ids.filter(its => its === item.id[1])
  ruleType.list.splice(index, 1)
  if (ruleType.list.length === 0) {
    ruleType.typeValue = null
    ruleType.lock = true
    formContent.alarmLock = true
    warning.value = ''
    ruleType.typeGroup.map(item => {
      item.disabled = false
    })
    ruleType.geometryGroup.map(item => {
      item.disabled = false
    })
  }
}

/** 白名单 **/
// 白名单
const whiteList = reactive({
  g: [],
  noteGroup: [],
})
getWhiteList()
// 查询
function getWhiteList() {}
// 选中添加
const selectWhiteList = o => {}
// 删除单项
const deleteWhiteList = (o, i) => {}

/** 作用时间 **/
const getVaildTime = () => {
  formContent.param.rule_startTime = common.newDateToFormatTime(formContent.vaildTime[0]).t2
  formContent.param.rule_endTime = common.newDateToFormatTime(formContent.vaildTime[1]).t2
}

/** 是否默认规则 **/
const getStaticRule = o => {
  formContent.defaultRule = o
}

/** 查询初始化 **/
// 查询规则
function getRuleList() {}

// 查询主体
function searchSubject(id) {}

/** 提交 **/
const submit = () => {}
</script>

<style scoped lang="scss">
.ignore-scroll {
  padding: 0 24px;
  box-sizing: border-box;
  height: v-bind(height);
  overflow-y: scroll;
}
:deep(.el-button) {
  box-shadow: var(--main-box-shadow);
}
</style>
