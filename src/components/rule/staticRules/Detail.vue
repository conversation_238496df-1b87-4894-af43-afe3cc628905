<template>
  <div class="detail">
    <!-- 标题-->
    <CommonTitle isBack>
      <template #default>
        <div class="opearte">
          <el-button type="primary" @click="submit">提交</el-button>
        </div>
      </template>
    </CommonTitle>

    <div class="paint">
      <div class="card">
        <el-row class="mapRow">
          <el-col :span="18">
            <rule-map v-model:mapData="mapData" />
          </el-col>
          <el-col :span="6">
            <!--            <FormContent v-model:size="x"></FormContent>-->
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
const { t } = useI18n()
// 组件
import CommonTitle from '@/components/common/CommonTitle/CommonTitle.vue'
/*import FormContent from '@/components/rule/staticRules/FormContent.vue'*/
import RuleMap from '@/components/map/ShowMap.vue'
const x = defineProps(['size'])
const size = ref(x)
// 尺寸
import useComponent, { PathType } from '@/hooks/useComponent.js'
import * as common from '@/utils/common'
import useOrg from '@/hooks/useOrg.js'
import { airspace_aggregate_list, obstacle_list } from '@/request/api'
// 跳转
const { setPathBack, setPath, pathData, getParam } = useComponent()
// 地图
const mapData = reactive({
  displayStyle: {},
  geometries: [],
  abnormalWidth: 0,
  geoJson: [],
})
/** 初始化 **/
initDate()
function initDate() {}

const submit = () => {}
</script>

<style>
@import 'custom.css';
</style>
<style scoped lang="scss">
:deep(.el-button) {
  box-shadow: var(--main-box-shadow);
}
</style>
