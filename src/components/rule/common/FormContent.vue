<template>
  <div v-loading="formContent.loading">
    <div class="piece">
      <div class="scroll">
        <div class="line mbg">
          <div class="title w">{{ rule_Type_Name }}规则名称</div>
          <div class="in">
            <el-input placeholder="请输入" v-model="formContent.param.rule_ruleName"></el-input>
          </div>
        </div>
        <div v-if="searchLock" class="line mbg">
          <div class="title w">规则类型</div>
          <div class="in mbg">
            <div class="wrap col4 pdr">
              <el-select
                v-model="ruleType.typeValue"
                placeholder="请选择规则"
                style="width: 100%"
                clearable
                @change="getRuleType"
                @clear="clearRuleType"
              >
                <el-option
                  v-for="item in ruleType.typeGroup"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                />
              </el-select>
            </div>
            <div class="wrap col6">
              <el-cascader
                v-model="formContent.subjectIds"
                placeholder="请选择地图模型"
                :options="ruleType.geometryGroup"
                @change="getGeometry"
                filterable
              />
            </div>
          </div>
          <div v-for="(item, index) in ruleType.list" :key="index" class="in note">
            <div class="wrap">{{ item.note }}</div>
            <div class="wrap" @click="deleteRuleType(item, index)">
              <EIcon name="CircleClose" style="height: 31px" />
            </div>
          </div>
        </div>
        <div class="line mbg">
          <div class="title w">规则告警等级</div>
          <div class="in">
            <el-select
              v-model="warning.value"
              placeholder="请选择"
              style="width: 100%"
              :disabled="formContent.alarmLock"
              @change="getWarningLevel"
            >
              <el-option v-for="item in warning.group" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>
        <div class="line mbg" v-show="formContent.param.rule_ruleType === 'DYNAMIC'">
          <div class="title w">有效时间</div>
          <div class="in">
            <el-date-picker
              v-model="formContent.vaildTime"
              type="datetimerange"
              range-separator="到"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="getVaildTime"
            />
          </div>
        </div>
        <div class="line mbg">
          <div class="title w">缓冲区距离(米)</div>
          <div class="in">
            <el-input
              placeholder="请输入"
              type="number"
              oninput="value=value.replace(/[^\d]/g,'')"
              v-model="formContent.param.rule_bufferWidth"
            ></el-input>
          </div>
        </div>
        <div class="line mbg">
          <div class="title w">缓冲区告警等级</div>
          <div class="in">
            <el-select v-model="formContent.param.rule_bufferAlarmLevel" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in warning.group" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>

        <div class="line mbg">
          <div class="title w">标签</div>
          <div class="in">
            <TypeTags v-model="formContent.param.rule_tags" :options="defaultTags" />
          </div>
        </div>
        <div class="line mbg">
          <div class="title">白名单</div>
          <div class="in mbg">
            <el-select
              v-model="formContent.whiteList"
              multiple
              collapse-tags
              style="width: 100%"
              @change="selectWhiteList"
              placeholder="请选择"
            >
              <el-option v-for="item in whiteList.g" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div v-for="(item, index) in whiteList.noteGroup" :key="index" class="in note">
            <div class="wrap">{{ item.label }}</div>
            <div class="wrap" @click="deleteWhiteList(item, index)">
              <EIcon name="CircleClose" style="height: 31px" />
            </div>
          </div>
        </div>
        <div class="line mbg">
          <div class="title">规则简介</div>
          <div class="in">
            <el-input type="textarea" placeholder="请输入" :rows="3" v-model="formContent.param.rule_memo" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineExpose } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
const { t } = useI18n()
import useComponent, { PathType } from '@/hooks/useComponent.js'
import userOrg from '@/hooks/useOrg.js'
import * as common from '@/utils/common.js'

// 组件
import TypeTags from '@/components/common/typeTags/TypeTags.vue'
import * as api from '@/request/api/index.js'
import EIcon from '@/components/common/EIcon.vue'
const { orgId, cityName } = userOrg()
// 传入的数据
const size = defineModel('size')
const rule_Type = defineModel('ruleType')
const rule_Id = defineModel('ruleId')
const rule_status = defineModel('ruleStatus')
console.log(rule_status.value)
const rule_Type_Name = rule_Type.value === 'STATIC' ? '静态' : '动态'

// 传出数据
const emit = defineEmits(['setMapData'])

// 跳转
const { setPathBack, setPath, pathData, getParam } = useComponent()
/** 表单 **/
// 表单内容
const formContent = reactive({
  param: {
    rule_orgId: '',
    rule_flyRestrict: 'PERMITTED',
    rule_ruleName: '',
    rule_ruleType: rule_Type.value,
    rule_tags: '[]',
    rule_defaultRule: 0,
    rule_enterAlarmLevel: 0,
    rule_leaveAlarmLevel: 0,
    rule_botHeight: 0,
    rule_topHeight: 0,
    rule_heightAlarmLevel: 0,
    rule_bufferWidth: undefined,
    rule_bufferAlarmLevel: undefined,
    rule_startTime: -1,
    rule_endTime: -1,
    rule_memo: '',
    rule_status: rule_status.value,
    subject_subjectList: '',
    whitelist_idList: undefined,
  },
  subjectList: [],
  loading: false,
  subjectIds: [],
  editType: 'add',
  whiteList: '',
  alarmType: '',
  alarmLock: true,
  vaildTime: '',
  defaultRule: false,
})
const height = computed(() => size.value.height - 132 + 'px')
/** 初始化 **/
initDate()
function initDate() {
  if (rule_Id.value) {
    formContent.editType = 'edit'
    getRuleList(rule_Id.value)
  } else {
    formContent.editType = 'add'
  }
}

/** 告警 **/
// 告警等级
const warning = reactive({
  group: [
    {
      value: 4,
      label: '4级',
    },
    {
      value: 3,
      label: '3级',
    },
    {
      value: 2,
      label: '2级',
    },
    {
      value: 1,
      label: '1级',
    },
  ],
  value: '',
})
// 规则对应告警等级
const getWarningLevel = o => {
  if (formContent.alarmType === 'enter') {
    formContent.param.rule_enterAlarmLevel = o
  } else if (formContent.alarmType === 'leave') {
    formContent.param.rule_leaveAlarmLevel = o
  } else if (formContent.alarmType === 'height') {
    formContent.param.rule_heightAlarmLevel = o
  }
  formContent.param.rule_bufferAlarmLevel = o
}
// 清除告警等级
const clearWarningLevel = () => {
  formContent.param.rule_enterAlarmLevel = 0
  formContent.param.rule_leaveAlarmLevel = 0
  formContent.param.rule_heightAlarmLevel = 0
}

/** 规则类型 **/
const ruleType = reactive({
  show: false,
  searchCount: 0,
  typeGroup: [
    {
      value: 0,
      label: '靠近限制',
    },
    {
      value: 1,
      label: '飞离限制',
    },
    {
      value: 2,
      label: '高度限制',
    },
  ],
  typeValue: '',
  typeNote: '',
  lock: true,
  props: { multiple: true },
  geometryGroup: [
    {
      value: 1,
      label: '空域',
      children: [],
    },
    {
      value: 2,
      label: '航路',
      children: [],
    },
    {
      value: 3,
      label: '航线',
      children: [],
    },
    {
      value: 4,
      label: '障碍物-地点',
      children: [],
    },
    {
      value: 5,
      label: '障碍物-路线',
      children: [],
    },
    {
      value: 6,
      label: '障碍物-区域',
      children: [],
    },
  ],
  list: [],
})

const searchCount = ref(0)
searchObstacle_list()
// 查询障碍物
function searchObstacle_list() {
  let p = {
    status: rule_status.value,
    deleted: 0,
    offset: 0,
    limit: 999,
  }
  api
    .obstacle_list(p)
    .then(data => {
      searchCount.value++
      let d = data.list
      if (d.length > 0) {
        d.map(item => {
          let typeNum = 3
          if (item.kind === 'POINT') {
            typeNum = 3
          } else if (item.kind === 'LINE') {
            typeNum = 4
          } else if (item.kind === 'PLANE') {
            typeNum = 5
          }
          ruleType.geometryGroup[typeNum].children.push({
            value: item.id,
            label: item.name,
            displayStyle: item.displayStyle,
            geometries: item.geometries,
            geoJson: item.geoJson,
            extra: {},
          })
        })
      }
    })
    .catch(() => {
      searchCount.value++
    })
}
// 查询空域
searchAirSpace()
function searchAirSpace() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 999,
  }
  api
    .airspace_aggregate_list(p)
    .then(data => {
      searchCount.value++
      let d = data.list
      if (d.length > 0) {
        d.map(item => {
          ruleType.geometryGroup[0].children.push({
            value: item.id,
            label: item.airspaceName,
            displayStyle: item.displayStyle,
            geometries: item.geometries,
            geoJson: item.geoJson,
            extra: {},
          })
        })
      }
    })
    .catch(() => {
      searchCount.value++
    })
}
searchLa_airway()
// 查询航路
function searchLa_airway() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 999,
  }
  api
    .la_airway_list(p)
    .then(data => {
      searchCount.value++
      let d = data.list
      if (d.length > 0) {
        d.map(item => {
          ruleType.geometryGroup[1].children.push({
            value: item.id,
            label: item.name,
            displayStyle: item.displayStyle,
            geometries: [
              { coordinateList: item.airwayPoints.map(point => [point.longitude, point.latitude]), type: 'LINE' },
            ],
            geoJson: {},
            extra: {
              protectedAreaWidth: item.protectedAreaWidth,
              airports: item.airports,
              emergencyAirports: item.emergencyAirports,
            },
          })
        })
      }
    })
    .catch(() => {
      searchCount.value++
    })
}
// 查询航线
searchLine()
function searchLine() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 999,
  }
  api
    .airline_aggregate_list(p)
    .then(data => {
      searchCount.value++
      let d = data.list
      if (d.length > 0) {
        d.map(item => {
          ruleType.geometryGroup[2].children.push({
            value: item.id,
            label: item.airlineTitle,
            displayStyle: item.displayStyle,
            geometries: item.geometries,
            geoJson: item.geoJson,
            extra: {},
          })
        })
      }
    })
    .catch(() => {
      searchCount.value++
    })
}
// 获取规则类型
const getRuleType = o => {
  if (typeof o === 'undefined') return
  clearWarningLevel()
  let x = ruleType.typeGroup.filter(item => item.value === o)
  ruleType.typeNote = x[0].label
  if (o === 0) {
    formContent.alarmType = 'enter'
  } else if (o === 1) {
    formContent.alarmType = 'leave'
  } else if (o === 2) {
    formContent.alarmType = 'height'
  }
  formContent.alarmLock = false
}
// 获取地点主体信息
const getGeometry = o => {
  const subject = ruleType.geometryGroup[o[0] - 1].children.filter(item => item.value === o[1])
  console.log(subject)
  let note = []
  ruleType.list = []
  if (o.length === 0) {
    formContent.subjectList = []
  } else {
    emit('setMapData', subject[0])
    ruleType.geometryGroup.map(item => {
      if (item.value === o[0]) {
        note = item.children.filter(item => item.value === o[1])
      } else {
      }
    })
    note.map(item => {
      ruleType.list.push({ note: item.label, id: item.value })
    })
    formContent.subjectList = []
    // 障碍物
    if (o[0] === 4 || o[0] === 5 || o[0] === 6) {
      formContent.subjectList.push({
        subjectType: 'OBSTACLE',
        subjectId: o[1],
      })
      // 空域
    } else if (o[0] === 1) {
      formContent.subjectList.push({
        subjectType: 'AIRSPACE',
        subjectId: o[1],
      })
      // 航路
    } else if (o[0] === 2) {
      formContent.subjectList.push({
        subjectType: 'LA_AIRWAY',
        subjectId: o[1],
      })
      // 航线
    } else if (o[0] === 3) {
      formContent.subjectList.push({
        subjectType: 'UA_AIRLINE',
        subjectId: o[1],
      })
    }
  }
}

const searchLock = computed(() => {
  if (searchCount.value >= 4) return true
})

const clearMapData = () => {
  const subject = {
    displayStyle: {},
    geometries: [],
    geoJson: [],
    extra: {},
    value: '',
  }
  emit('setMapData', subject)
}

// 清除规则类型
function clearRuleType() {
  ruleType.list = []
  formContent.subjectId = ''
  formContent.subjectList = []
  formContent.subjectIds = []
  ruleType.typeValue = null
  warning.value = ''
  formContent.alarmLock = true
  formContent.alarmType = ''
  formContent.param.rule_heightAlarmLevel = 0
  formContent.param.rule_leaveAlarmLevel = 0
  formContent.param.rule_enterAlarmLevel = 0
  clearMapData()
}

// 删除单项生成规则数据
const deleteRuleType = () => {
  ruleType.list = []
  ruleType.typeValue = null
  formContent.alarmLock = true
  warning.value = ''
  formContent.subjectIds = ''
  clearMapData()
}

/** 标签 **/
const defaultTags = ref([])
// 查询
searchDic()
function searchDic() {
  let p = {
    usage: 'TAG',
    dicId: 'TCd9ZAECbN6XGv39bRGQh',
  }
  api.dic_list(p).then(data => {
    const d = data.list[0]
    d.itemList.map(item => {
      defaultTags.value.push({ value: item.id, label: item.itemValue })
    })
    console.log(defaultTags)
  })
}

/** 白名单 **/
// 白名单
const whiteList = reactive({
  g: [],
  noteGroup: [],
})
getWhiteList()
// 查询
function getWhiteList() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 10,
  }
  api.whitelist_aggregate(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      whiteList.g = []
      d.map(item => {
        whiteList.g.push({
          value: item.id,
          label: item.whitelistName,
        })
      })
    }
  })
}
// 选中添加
const selectWhiteList = o => {
  if (typeof o === 'undefined') return
  formContent.param.whitelist_idList = JSON.stringify(o)
  const d1 = new Set(o)
  whiteList.noteGroup = whiteList.g.filter(item => d1.has(item.value))
}
// 删除单项
const deleteWhiteList = (o, i) => {
  for (let i in formContent.whiteList) {
    if (formContent.whiteList[i] === o.value) {
      formContent.whiteList.splice(i, 1)
    }
  }
  whiteList.noteGroup.splice(i, 1)
}

/** 作用时间 **/
const getVaildTime = () => {
  formContent.param.rule_startTime = common.newDateToFormatTime(formContent.vaildTime[0]).t2
  formContent.param.rule_endTime = common.newDateToFormatTime(formContent.vaildTime[1]).t2
}

/** 编辑 **/
// 查询规则
function getRuleList(id) {
  let p = {
    id: id,
    status: rule_status.value,
    alarmLevelList: JSON.stringify([0]),
    deleted: 0,
    offset: 0,
    limit: 1,
  }
  api
    .rule_aggregate_list(p)
    .then(data => {
      let d = data.list[0]
      formContent.param.rule_id = d.id
      formContent.param.rule_ruleName = d.ruleName
      formContent.param.rule_flyRestrict = d.flyRestrict
      formContent.param.rule_orgId = d.orgId
      if (d.enterAlarmLevel > 0) {
        warning.value = d.enterAlarmLevel
        formContent.alarmType = 'enter'
        formContent.param.rule_enterAlarmLevel = d.enterAlarmLevel
      } else if (d.leaveAlarmLevel > 0) {
        warning.value = d.leaveAlarmLevel
        formContent.alarmType = 'leave'
        formContent.param.rule_leaveAlarmLevel = d.leaveAlarmLevel
      } else if (d.heightAlarmLevel > 0) {
        warning.value = d.heightAlarmLevel
        formContent.alarmType = 'height'
        formContent.param.rule_heightAlarmLevel = d.heightAlarmLevel
        formContent.param.rule_botHeight = d.botHeight
        formContent.param.rule_topHeight = d.topHeight
      }
      formContent.param.rule_bufferAlarmLevel = d.bufferAlarmLevel
      formContent.param.rule_bufferWidth = d.bufferWidth
      formContent.param.rule_tags = JSON.stringify(d.ruleTags)
      if (d.whitelistList) {
        let wl = []
        d.whitelistList.map(item => {
          if (item.status === 1 && item.deleteTime === 0) {
            whiteList.g.push({
              value: item.id,
              label: item.whitelistName,
            })
            whiteList.noteGroup.push({
              value: item.id,
              label: item.whitelistName,
            })
            wl.push(item.id)
          }
        })
        console.log(wl)
        formContent.whiteList = wl
      }
      formContent.param.rule_startTime = d.startTime
      formContent.param.rule_endTime = d.endTime
      formContent.vaildTime = [new Date(common.formatTime(d.startTime).t1), new Date(common.formatTime(d.endTime).t1)]
      formContent.param.rule_memo = d.memo
      if (d.enterAlarmLevel > 0) {
        ruleType.typeValue = 0
        ruleType.typeNote = '靠近限制'
      } else if (d.leaveAlarmLevel > 0) {
        ruleType.typeValue = 1
        ruleType.typeNote = '飞离限制'
      } else if (d.heightAlarmLevel > 0) {
        ruleType.typeValue = 2
        ruleType.typeNote = '高度限制'
      }
      formContent.alarmLock = false
      searchSubject(d.id)
    })
    .catch(() => {})
}

// 查询主体
function searchSubject(id) {
  let p = {
    ruleId: id,
    offset: 0,
    limit: 20,
  }
  api
    .rule_list_subject(p)
    .then(data => {
      let d = data.list
      if (d.length > 0) {
        if (d[0].subjectType === 'OBSTACLE') {
          let num = 4
          if (d[0].subject.kind === 'POINT') {
            num = 4
          } else if (d[0].subject.kind === 'LINE') {
            num = 5
          } else if (d[0].subject.kind === 'PLANE') {
            num = 6
          }
          d.map(item => {
            if (item.subject.deleteTime === 0 && item.subject.status === 1) {
              // 当前只会存在一条主体信息，选择器变为单选格式
              formContent.subjectIds = [num, item.subject.id]
              ruleType.list.push({
                note: item.subject.name,
                id: item.subjectId,
              })
              formContent.subjectList.push({
                subjectType: 'OBSTACLE',
                subjectId: item.subjectId,
              })
              // ruleType.geometryGroup[num].children.push({
              //   value: item.id,
              //   label: item.name,
              //   displayStyle: item.displayStyle,
              //   geometries: item.geometries,
              //   geoJson: item.geoJson,
              //   extra: {}
              // })
              // mapData.ids.push(item.subject.airspaceModelId)
            }
          })
        } else if (d[0].subjectType === 'AIRSPACE') {
          d.map(item => {
            if (item.subject.deleteTime === 0 && item.subject.status === 1) {
              ruleType.list.push({
                note: item.subject.airspaceName,
                id: item.subjectId,
              })
              formContent.subjectIds = [1, item.subject.id]
              formContent.subjectList.push({
                subjectType: 'AIRSPACE',
                subjectId: item.subjectId,
              })
              // mapData.ids.push(item.subject.airspaceModelId)
            }
          })
        } else if (d[0].subjectType === 'LA_AIRWAY') {
          d.map(item => {
            if (item.subject.deleteTime === 0 && item.subject.status === 1) {
              ruleType.list.push({
                note: item.subject.name,
                id: item.subjectId,
              })
              formContent.subjectIds = [2, item.subject.id]
              formContent.subjectList.push({
                subjectType: 'LA_AIRWAY',
                subjectId: item.subjectId,
              })
              console.log('ruleType')
              console.log(ruleType)
              // mapData.ids.push(item.subject.airspaceModelId)
            }
          })
        } else if (d[0].subjectType === 'UA_AIRLINE') {
          d.map(item => {
            if (item.subject.deleteTime === 0 && item.subject.status === 1) {
              ruleType.list.push({
                note: item.subject.airlineTitle,
                id: item.subjectId,
              })
              formContent.subjectIds = [3, item.subject.id]
              formContent.subjectList.push({
                subjectType: 'UA_AIRLINE',
                subjectId: item.subjectId,
              })
            }
          })
        }
        let subject
        if (d[0].subjectType === 'LA_AIRWAY') {
          const c = JSON.parse(d[0].subject.airwayPoints)
          console.log(d[0].subject.airwayPoints)
          const v = d[0].subject.airports ? JSON.parse(d[0].subject.airports) : ''
          subject = {
            value: d[0].id,
            displayStyle: d[0].subject.displayStyle,
            geometries: [{ coordinateList: c.map(point => [point.longitude, point.latitude]), type: 'LINE' }],
            geoJson: {},
            extra: {
              protectedAreaWidth: d[0].subject.protectedAreaWidth,
              airports: v,
              emergencyAirports: JSON.parse(d[0].subject.emergencyAirports),
            },
          }
        } else {
          subject = {
            value: d[0].id,
            displayStyle: JSON.parse(d[0].subject.displayStyle),
            geometries: JSON.parse(d[0].subject.geometries),
            geoJson: JSON.parse(d[0].subject.geoJson),
            extra: {},
          }
        }
        console.log('subject')
        console.log(subject)
        emit('setMapData', subject)
      }
    })
    .catch(() => {})
}

/** 提交 **/
const submit = () => {
  formContent.param.subject_subjectList = JSON.stringify(formContent.subjectList)
  if (formContent.param.rule_ruleName === '') {
    ElMessage.error(t('message.ruleNameLack'))
  } else if (formContent.param.rule_flyRestrict === '') {
    ElMessage.error(t('message.flyRestrictLack'))
  } else if (ruleType.typeValue === '') {
    ElMessage.error(t('message.ruleTypeLack'))
  } else if (formContent.subjectList.length === 0) {
    ElMessage.error(t('message.modelLack'))
  } else if (
    formContent.param.rule_enterAlarmLevel === 0 &&
    formContent.param.rule_leaveAlarmLevel === 0 &&
    formContent.param.rule_heightAlarmLevel === 0
  ) {
    ElMessage.error(t('message.warningLevelLack'))
  } else if (
    (formContent.param.rule_ruleType === 'DYNAMIC' && formContent.vaildTime === '') ||
    formContent.vaildTime === null
  ) {
    ElMessage.error(t('message.vaildTimeLack'))
  } else if (!formContent.param.rule_bufferWidth || formContent.param.rule_bufferWidth === '') {
    ElMessage.error(t('message.bufferWidthLack'))
  } else if (formContent.param.rule_tags === '[]' || formContent.param.rule_tags === '') {
    ElMessage.error(t('message.tagsLack'))
  } else {
    formContent.param.rule_orgId = orgId.value
    const url = formContent.editType === 'add' ? 'rule_create' : 'rule_update'
    formContent.loading = true
    api[url](formContent.param)
      .then(() => {
        ElMessage({
          message: t('message.saveSuccess'),
          type: 'success',
        })
        setTimeout(() => {
          formContent.loading = false
          const url = rule_Type.value === 'STATIC' ? 'rule/staticRules/list.vue' : 'rule/dynamicRules/list.vue'
          setPath?.(url, null, { type: PathType.successBack })
        }, common.globalTime())
      })
      .catch(() => {
        formContent.loading = false
      })
  }
}

// 新增默认规则
const submitDefault = () => {
  const url = formContent.editType === 'add' ? 'rule_single_create' : 'rule_single_update'
  const p = {
    id: formContent.param.rule_id,
    defaultRule: 1,
    ruleName: formContent.param.rule_ruleName,
    flyRestrict: 'PERMITTED',
    ruleType: 'STATIC',
    enterAlarmLevel: formContent.param.rule_enterAlarmLevel,
    leaveAlarmLevel: formContent.param.rule_leaveAlarmLevel,
    heightAlarmLevel: formContent.param.rule_heightAlarmLevel,
    bufferWidth: formContent.param.rule_bufferWidth,
    bufferAlarmLevel: formContent.param.rule_bufferAlarmLevel,
    botHeight: 0,
    topHeight: 0,
    startTime: -1,
    endTime: -1,
    status: 1,
  }
  api[url](p)
    .then(() => {
      ElMessage({
        message: t('message.saveSuccess'),
        type: 'success',
      })
      setTimeout(() => {
        formContent.loading = false
        const url = rule_Type.value === 'STATIC' ? 'rule/staticRules/list.vue' : 'rule/dynamicRules/list.vue'
        setPath?.(url, null, { type: PathType.successBack })
      }, common.globalTime())
    })
    .catch(() => {
      formContent.loading = false
    })
}

defineExpose({
  submit,
  // submitDefault,
})
</script>

<style scoped lang="less">
.scroll {
  padding: 0 24px;
  box-sizing: border-box;
  height: v-bind(height);
  overflow-y: auto;
}
:deep(.el-button) {
  box-shadow: var(--main-box-shadow);
}
</style>
