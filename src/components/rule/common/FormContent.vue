<template>
  <div v-loading="formContent.loading" class="detail">
    <div class="piece">
      <div class="ignore-scroll">
        <div class="line mbg">
          <div class="title w">{{ rule_Type_Name }}规则名称</div>
          <div class="in">
            <el-input placeholder="请输入" v-model="formContent.param.rule_ruleName"></el-input>
          </div>
        </div>
        <div class="line mbg">
          <div class="title w">飞行限制</div>
          <div class="in">
            <el-select v-model="formContent.param.rule_flyRestrict" placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in flyRestrict.g"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              />
            </el-select>
          </div>
        </div>
        <div v-if="ruleType.show" class="line mbg">
          <div class="title w">规则类型</div>
          <div class="in mbg">
            <div class="wrap col4 pdr">
              <el-select
                v-model="ruleType.typeValue"
                placeholder="请选择规则"
                style="width: 100%"
                clearable
                @change="getRuleType"
                @clear="clearRuleType"
              >
                <el-option
                  v-for="item in ruleType.typeGroup"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                />
              </el-select>
            </div>
            <div class="wrap col6">
              <el-cascader
                v-model="formContent.subjectIds"
                placeholder="请选择地图模型"
                :options="ruleType.geometryGroup"
                :props="ruleType.props"
                collapse-tags
                :disabled="ruleType.lock"
                @change="getGeometry"
              />
            </div>
          </div>
          <div v-for="(item, index) in ruleType.list" :key="index" class="in note">
            <div class="wrap">{{ item.note }}</div>
            <div class="wrap" @click="deleteRuleType(item, index)">
              <EIcon name="CircleClose" style="height: 31px" />
            </div>
          </div>
        </div>
        <div class="line mbg">
          <div class="title w">规则告警等级</div>
          <div class="in">
            <el-select
              v-model="warning.value"
              placeholder="请选择"
              style="width: 100%"
              :disabled="formContent.param.alarmLock"
              @change="getWarningLevel"
            >
              <el-option v-for="item in warning.group" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>
        <div class="line mbg" v-show="formContent.param.rule_ruleType === 'DYNAMIC'">
          <div class="title w">有效时间</div>
          <div class="in">
            <el-date-picker
              v-model="formContent.vaildTime"
              type="datetimerange"
              range-separator="到"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="getVaildTime"
            />
          </div>
        </div>
        <div class="line mbg">
          <div class="title w">缓冲区距离(米)</div>
          <div class="in">
            <el-input
              placeholder="请输入"
              type="number"
              oninput="value=value.replace(/[^\d]/g,'')"
              v-model="formContent.param.rule_bufferWidth"
            ></el-input>
          </div>
        </div>
        <div class="line mbg">
          <div class="title w">缓冲区告警等级</div>
          <div class="in">
            <el-select v-model="formContent.param.rule_bufferAlarmLevel" placeholder="请选择" style="width: 100%">
              <el-option v-for="item in warning.group" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>

        <div class="line mbg">
          <div class="title w">标签</div>
          <div class="in">
            <TypeTags v-model="formContent.param.rule_tags" :options="defaultTags" />
          </div>
        </div>
        <div class="line mbg">
          <div class="title">白名单</div>
          <div class="in mbg">
            <el-select
              v-model="formContent.whiteList"
              multiple
              collapse-tags
              style="width: 100%"
              @change="selectWhiteList"
              placeholder="请选择"
            >
              <el-option v-for="item in whiteList.g" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div v-for="(item, index) in whiteList.noteGroup" :key="index" class="in note">
            <div class="wrap">{{ item.label }}</div>
            <div class="wrap" @click="deleteWhiteList(item, index)">
              <EIcon name="CircleClose" style="height: 31px" />
            </div>
          </div>
        </div>
        <div class="line mbg">
          <div class="title">规则简介</div>
          <div class="in">
            <el-input type="textarea" placeholder="请输入" :rows="3" v-model="formContent.param.rule_memo" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineExpose } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
const { t } = useI18n()
import useComponent, { PathType } from '@/hooks/useComponent.js'
import * as common from '@/utils/common.js'

// 组件
import TypeTags from '@/components/common/typeTags/TypeTags.vue'
import * as api from '@/request/api/index.js'
import EIcon from '@/components/common/EIcon.vue'
// 传入的数据
const size = defineModel('size')
const rule_Type = defineModel('ruleType')
const rule_Id = defineModel('ruleId')
const rule_Type_Name = rule_Type.value === 'STATIC' ? '静态' : '动态'

// 跳转
const { setPathBack, setPath, pathData, getParam } = useComponent()
/** 表单 **/
// 表单内容
const formContent = reactive({
  param: {
    rule_flyRestrict: '',
    rule_ruleName: '',
    rule_ruleType: rule_Type.value,
    rule_tags: '[]',
    rule_defaultRule: 0,
    rule_enterAlarmLevel: 0,
    rule_leaveAlarmLevel: 0,
    rule_botHeight: 0,
    rule_topHeight: 0,
    rule_heightAlarmLevel: 0,
    rule_bufferWidth: undefined,
    rule_bufferAlarmLevel: undefined,
    rule_startTime: -1,
    rule_endTime: -1,
    rule_memo: '',
    rule_status: 1,
    subject_subjectList: '',
    whitelist_idList: undefined,
  },
  subjectList: [],
  loading: false,
  subjectIds: [],
  editType: 'add',
  whiteList: '',
  alarmType: '',
  alarmLock: true,
  vaildTime: '',
  defaultRule: false,
})
const height = computed(() => size.value.height - 88 + 'px')
/** 初始化 **/
initDate()
function initDate() {
  if (rule_Id.value) {
    formContent.editType = 'edit'
    getRuleList(rule_Id.value)
  } else {
    formContent.editType = 'add'
  }
}

/** 飞行限制 **/
const flyRestrict = reactive({
  g: [
    {
      label: '适飞',
      value: 'PERMITTED',
    },
    {
      label: '管制',
      value: 'CONTROLLED',
    },
    {
      label: '禁飞',
      value: 'FORBIDDEN',
    },
  ],
})

/** 告警 **/
// 告警等级
const warning = reactive({
  group: [
    {
      value: 4,
      label: '4级',
    },
    {
      value: 3,
      label: '3级',
    },
    {
      value: 2,
      label: '2级',
    },
    {
      value: 1,
      label: '1级',
    },
  ],
  value: '',
})
// 规则对应告警等级
const getWarningLevel = o => {
  if (formContent.alarmType === 'enter') {
    formContent.param.rule_enterAlarmLevel = o
  } else if (formContent.alarmType === 'leave') {
    formContent.param.rule_leaveAlarmLevel = o
  } else if (formContent.alarmType === 'height') {
    formContent.param.rule_heightAlarmLevel = o
  }
  formContent.param.rule_bufferAlarmLevel = o
}

/** 规则类型 **/
const ruleType = reactive({
  show: false,
  typeGroup: [
    {
      value: 0,
      label: '靠近限制',
      disabled: false,
    },
    {
      value: 1,
      label: '飞离限制',
      disabled: false,
    },
    {
      value: 2,
      label: '高度限制',
      disabled: false,
    },
  ],
  typeValue: '',
  typeNote: '',
  lock: true,
  props: { multiple: true },
  geometryGroup: [
    {
      value: 1,
      label: '空域',
      children: [],
    },
    {
      value: 2,
      label: '航路',
      children: [],
    },
    {
      value: 3,
      label: '航线',
      children: [],
    },
    {
      value: 4,
      label: '障碍物-地点',
      children: [],
    },
    {
      value: 5,
      label: '障碍物-路线',
      children: [],
    },
    {
      value: 6,
      label: '障碍物-空域',
      children: [],
    },
  ],
  list: [],
})

searchObstacle_list()
// 查询障碍物
function searchObstacle_list() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 999,
  }
  api.obstacle_list(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      d.map(item => {
        let typeNum = 3
        if (item.kind === 'POINT') {
          typeNum = 3
        } else if (item.kind === 'LINE') {
          typeNum = 4
        } else if (item.kind === 'PLANE') {
          typeNum = 5
        }
        ruleType.geometryGroup[typeNum].children.push({
          value: item.id,
          label: item.name,
        })
      })
    }
  })
}
// 查询空域
searchAirSpace()
function searchAirSpace() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 999,
  }

  api.airspace_aggregate_list(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      d.map(item => {
        ruleType.geometryGroup[0].children.push({
          value: item.id,
          label: item.airspaceName,
        })
      })
    }
  })
}
searchLa_airway()
// 查询航路
function searchLa_airway() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 999,
  }
  api.la_airway_list(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      d.map(item => {
        ruleType.geometryGroup[1].children.push({
          value: item.id,
          label: item.name,
        })
      })
      ruleType.show = true
    }
  })
}
// 查询航线
searchLine()
function searchLine() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 999,
  }
  api.airline_aggregate_list(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      d.map(item => {
        ruleType.geometryGroup[2].children.push({
          value: item.id,
          label: item.airlineTitle,
        })
      })
      ruleType.show = true
    }
  })
}
// 获取规则类型
const getRuleType = o => {
  if (typeof o === 'undefined') return
  ruleType.typeGroup.map((item, index) => {
    item.disabled = index !== o
  })
  let x = ruleType.typeGroup.filter(item => item.value === o)
  ruleType.typeNote = x[0].label
  if (o === 0) {
    formContent.alarmType = 'enter'
  } else if (o === 1) {
    formContent.alarmType = 'leave'
  } else if (o === 2) {
    formContent.alarmType = 'height'
  }
  ruleType.lock = false
  formContent.alarmLock = false
}
// 获取地点主体信息
const getGeometry = o => {
  let note = []
  ruleType.list = []
  if (o.length === 0) {
    ruleType.typeGroup.map(item => (item.disabled = false))
  } else {
    ruleType.geometryGroup.map(item => {
      if (item.value === o[0][0]) {
        const oids = new Set(o.map(subArr => subArr[1]))
        note = item.children.filter(item => oids.has(item.value))
        item.disabled = false
      } else {
        item.disabled = true
      }
    })
    note.map(item => {
      ruleType.list.push({ note: ruleType.typeNote + ':' + item.label, id: item.value })
    })
    formContent.subjectList = []
    o.map(item => {
      // 障碍物
      if (item[0] === 4 || item[0] === 5 || item[0] === 6) {
        formContent.subjectList.push({
          subjectType: 'OBSTACLE',
          subjectId: item[1],
        })
        // 空域
      } else if (item[0] === 1) {
        formContent.subjectList.push({
          subjectType: 'AIRSPACE',
          subjectId: item[1],
        })
        // 航路
      } else if (item[0] === 2) {
        formContent.subjectList.push({
          subjectType: 'LA_AIRWAY',
          subjectId: item[1],
        })
        // 航线
      } else if (item[0] === 3) {
        formContent.subjectList.push({
          subjectType: 'UA_AIRLINE',
          subjectId: item[1],
        })
      }
    })
  }
}
// 清除规则类型
function clearRuleType() {
  ruleType.geometryGroup.map(item => {
    item.disabled = false
  })
  ruleType.typeGroup.map(item => {
    item.disabled = false
  })
  ruleType.list = []
  formContent.subjectId = ''
  formContent.subjectList = []
  formContent.subjectIds = []
  ruleType.typeValue = null
  warning.value = ''
  formContent.alarmLock = true
  // ruleType.lock = true
  formContent.alarmType = ''
  formContent.param.rule_heightAlarmLevel = 0
  formContent.param.rule_leaveAlarmLevel = 0
  formContent.param.rule_enterAlarmLevel = 0
}
// 删除单项生成规则数据
const deleteRuleType = (item, index) => {
  formContent.subjectIds = formContent.subjectIds.filter(its => item.id !== its[1])
  formContent.subjectList = formContent.subjectList.filter(its => item.id !== its.subjectId)
  // mapData.ids = mapData.ids.filter(its => its === item.id[1])
  ruleType.list.splice(index, 1)
  if (ruleType.list.length === 0) {
    ruleType.typeValue = null
    ruleType.lock = true
    formContent.alarmLock = true
    warning.value = ''
    ruleType.typeGroup.map(item => {
      item.disabled = false
    })
    ruleType.geometryGroup.map(item => {
      item.disabled = false
    })
  }
}
/** 标签 **/
const defaultTags = ref([])
// 查询
searchDic()
function searchDic() {
  let p = {
    usage: 'TAG',
    dicId: 'TCd9ZAECbN6XGv39bRGQh',
  }
  api.dic_list(p).then(data => {
    const d = data.list[0]
    d.itemList.map(item => {
      defaultTags.value.push({ value: item.id, label: item.itemValue })
    })
    console.log(defaultTags)
  })
}

/** 白名单 **/
// 白名单
const whiteList = reactive({
  g: [],
  noteGroup: [],
})
getWhiteList()
// 查询
function getWhiteList() {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 10,
  }
  api.whitelist_aggregate(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      whiteList.g = []
      d.map(item => {
        whiteList.g.push({
          value: item.id,
          label: item.whitelistName,
        })
      })
    }
  })
}
// 选中添加
const selectWhiteList = o => {
  if (typeof o === 'undefined') return
  formContent.param.whitelist_idList = JSON.stringify(o)
  const d1 = new Set(o)
  whiteList.noteGroup = whiteList.g.filter(item => d1.has(item.value))
}
// 删除单项
const deleteWhiteList = (o, i) => {
  for (let i in formContent.whiteList) {
    if (formContent.whiteList[i] === o.value) {
      formContent.whiteList.splice(i, 1)
    }
  }
  whiteList.noteGroup.splice(i, 1)
}

/** 作用时间 **/
const getVaildTime = () => {
  formContent.param.rule_startTime = common.newDateToFormatTime(formContent.vaildTime[0]).t2
  formContent.param.rule_endTime = common.newDateToFormatTime(formContent.vaildTime[1]).t2
}

/** 编辑 **/
// 查询规则
function getRuleList(id) {
  let p = {
    id: id,
    status: 1,
    alarmLevelList: JSON.stringify([0]),
    deleted: 0,
    offset: 0,
    limit: 1,
  }
  api
    .rule_aggregate_list(p)
    .then(data => {
      let d = data.list[0]
      formContent.param.rule_id = d.id
      formContent.param.rule_ruleName = d.ruleName
      formContent.param.rule_flyRestrict = d.flyRestrict
      if (d.enterAlarmLevel > 0) {
        warning.value = d.enterAlarmLevel
        formContent.alarmType = 'enter'
        formContent.param.rule_enterAlarmLevel = d.enterAlarmLevel
      } else if (d.leaveAlarmLevel > 0) {
        warning.value = d.leaveAlarmLevel
        formContent.alarmType = 'leave'
        formContent.param.rule_leaveAlarmLevel = d.leaveAlarmLevel
      } else if (d.heightAlarmLevel > 0) {
        warning.value = d.heightAlarmLevel
        formContent.alarmType = 'height'
        formContent.param.rule_heightAlarmLevel = d.heightAlarmLevel
        formContent.param.rule_botHeight = d.botHeight
        formContent.param.rule_topHeight = d.topHeight
      }
      formContent.param.rule_bufferAlarmLevel = d.bufferAlarmLevel
      formContent.param.rule_bufferWidth = d.bufferWidth
      formContent.param.rule_tags = JSON.stringify(d.ruleTags)
      if (d.whitelistList) {
        let wl = []
        d.whitelistList.map(item => {
          if (item.status === 1 && item.deleteTime === 0) {
            whiteList.g.push({
              value: item.id,
              label: item.whitelistName,
            })
            whiteList.noteGroup.push({
              value: item.id,
              label: item.whitelistName,
            })
            wl.push(item.id)
          }
        })
        console.log(wl)
        formContent.whiteList = wl
      }
      formContent.param.rule_startTime = d.startTime
      formContent.param.rule_endTime = d.endTime
      formContent.vaildTime = [new Date(common.formatTime(d.startTime).t1), new Date(common.formatTime(d.endTime).t1)]
      formContent.param.rule_memo = d.memo
      if (d.enterAlarmLevel > 0) {
        ruleType.typeValue = 0
        ruleType.typeNote = '靠近限制'
      } else if (d.leaveAlarmLevel > 0) {
        ruleType.typeValue = 1
        ruleType.typeNote = '飞离限制'
      } else if (d.heightAlarmLevel > 0) {
        ruleType.typeValue = 2
        ruleType.typeNote = '高度限制'
      }
      formContent.alarmLock = false
      searchSubject(d.id)
    })
    .catch(() => {})
}

// 查询主体
function searchSubject(id) {
  let p = {
    ruleId: id,
    offset: 0,
    limit: 20,
  }
  api
    .rule_list_subject(p)
    .then(data => {
      let d = data.list
      if (d.length > 0) {
        ruleType.lock = false
        if (d[0].subjectType === 'OBSTACLE') {
          let num = 4
          if (d[0].subject.kind === 'POINT') {
            num = 4
          } else if (d[0].subject.kind === 'LINE') {
            num = 5
          } else if (d[0].subject.kind === 'PLANE') {
            num = 6
          }
          console.log(d)
          d.map(item => {
            if (item.subject.deleteTime === 0 && item.subject.status === 1) {
              formContent.subjectIds.push([num, item.subject.id])
              ruleType.list.push({
                note: ruleType.typeNote + ':' + item.subject.name,
                id: item.subjectId,
              })
              formContent.subjectList.push({
                subjectType: 'OBSTACLE',
                subjectId: item.subjectId,
              })
              // mapData.ids.push(item.subject.airspaceModelId)
            }
          })
          console.log('formContent.subjectIds')
          console.log(formContent.subjectIds)
        } else if (d[0].subjectType === 'AIRSPACE') {
          d.map(item => {
            if (item.subject.deleteTime === 0 && item.subject.status === 1) {
              ruleType.list.push({
                note: ruleType.typeNote + ':' + item.subject.airspaceName,
                id: item.subjectId,
              })
              formContent.subjectIds.push([1, item.subject.id])
              formContent.subjectList.push({
                subjectType: 'AIRSPACE',
                subjectId: item.subjectId,
              })
              // mapData.ids.push(item.subject.airspaceModelId)
            }
          })
        } else if (d[0].subjectType === 'LA_AIRWAY') {
          d.map(item => {
            if (item.subject.deleteTime === 0 && item.subject.status === 1) {
              ruleType.list.push({
                note: ruleType.typeNote + ':' + item.subject.name,
                id: item.subjectId,
              })
              formContent.subjectIds.push([2, item.subject.id])
              formContent.subjectList.push({
                subjectType: 'LA_AIRWAY',
                subjectId: item.subjectId,
              })
              // mapData.ids.push(item.subject.airspaceModelId)
            }
          })
        } else if (d[0].subjectType === 'UA_AIRLINE') {
          d.map(item => {
            if (item.subject.deleteTime === 0 && item.subject.status === 1) {
              ruleType.list.push({
                note: ruleType.typeNote + ':' + item.subject.airlineTitle,
                id: item.subjectId,
              })
              formContent.subjectIds.push([3, item.subject.id])
              formContent.subjectList.push({
                subjectType: 'UA_AIRLINE',
                subjectId: item.subjectId,
              })
              // mapData.ids.push(item.subject.airspaceModelId)
            }
          })
        }
      }
    })
    .catch(() => {})
}

/** 提交 **/
const submit = () => {
  formContent.param.subject_subjectList = JSON.stringify(formContent.subjectList)
  console.log(formContent.param)
  if (formContent.param.rule_ruleName === '') {
    ElMessage.error(t('message.ruleNameLack'))
  } else if (formContent.param.rule_flyRestrict === '') {
    ElMessage.error(t('message.flyRestrictLack'))
  } else if (ruleType.typeValue === '') {
    ElMessage.error(t('message.ruleTypeLack'))
  } else if (formContent.subjectList.length === 0) {
    ElMessage.error(t('message.modelLack'))
  } else if (
    formContent.param.rule_enterAlarmLevel === 0 &&
    formContent.param.rule_leaveAlarmLevel === 0 &&
    formContent.param.rule_heightAlarmLevel === 0
  ) {
    ElMessage.error(t('message.warningLevelLack'))
  } else if (formContent.param.rule_ruleType === 'DYNAMIC' && formContent.vaildTime === '') {
    ElMessage.error(t('message.vaildTimeLack'))
  } else if (!formContent.param.rule_bufferWidth || formContent.param.rule_bufferWidth === '') {
    ElMessage.error(t('message.bufferWidthLack'))
  } else if (formContent.param.rule_tags === '[]' || formContent.param.rule_tags === '') {
    ElMessage.error(t('message.tagsLack'))
  } else {
    const url = formContent.editType === 'add' ? 'rule_create' : 'rule_update'
    formContent.loading = true
    api[url](formContent.param)
      .then(() => {
        ElMessage({
          message: t('message.saveSuccess'),
          type: 'success',
        })
        setTimeout(() => {
          formContent.loading = false
          const url = rule_Type.value === 'STATIC' ? 'rule/staticRules/list.vue' : 'rule/dynamicRules/list.vue'
          setPath?.(url, null, { type: PathType.successBack })
        }, common.globalTime())
      })
      .catch(() => {
        formContent.loading = false
      })
  }
}

defineExpose({
  submit,
})
</script>

<style scoped lang="scss">
.ignore-scroll {
  padding: 0 24px;
  box-sizing: border-box;
  height: v-bind(height);
  overflow-y: scroll;
}
:deep(.el-button) {
  box-shadow: var(--main-box-shadow);
}
</style>
