/** 规则/黑名单 **/
:root {
  --main-color: #f7712f;
  --main-box-shadow: 0 12px 12px 0 rgba(247, 113, 47, 0.16);
  --main-box-color4: rgba(0, 0, 0, 0.04);
  --main-font-color4: rgba(0, 0, 0, 0.4);
  --main-font-color6: rgba(0, 0, 0, 0.6);
  --main-font-color9: rgba(0, 0, 0, 0.9);
}
/** 隐藏滚动条 **/
::-webkit-scrollbar {
  width: 0;
}
.custom,
.detail {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.card {
  background: #fff;
}
.custom .navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  height: 48px;
}
.custom .navigation .title {
  font-size: 24px;
}
.custom .search {
  padding: 24px 0;
  height: 152px;
}
.custom .search .card {
  background: #fff;
  padding: 12px;
  box-sizing: border-box;
  border-radius: 8px;
}
.custom .search .card .in {
  display: flex;
  justify-content: space-between;
  line-height: 40px;
  height: 40px;
}
.custom .search .card .in .w5 {
  width: 50%;
}
.custom .search .card .in .w4 {
  width: 40%;
}
.custom .search .card .line {
  padding: 12px;
  display: flex;
  align-items: center;
}
.custom .search .card .line .label {
  width: 30%;
  text-align: right;
  padding-right: 12px;
  box-sizing: border-box;
}
.container .search .card .line .in {
  width: 70%;
}
.custom .search .card .operate {
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.operate .btn {
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  border-radius: 8px;
  margin-left: 12px;
  font-size: 16px;
  color: var(--main-font-color6);
  cursor: pointer;
}
.custom .operate .btn.n {
  color: #fff;
  background: var(--main-color);
  box-shadow: var(--main-box-shadow);
}
.custom .operate .btn.d {
  height: 38px;
  line-height: 38px;
  padding: 0 15px;
  color: var(--main-font-color6);
  border: 1px solid rgba(0, 0, 0, 0.2);
  background: #fff;
  box-shadow: 0 12px 12px 0 rgba(0, 0, 0, 0.06);
}
.custom .content {
  flex: 1;
}
.custom .content .card {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 5px;
  box-shadow: 0 8px 20px 0 rgba(0, 0, 0, 0.02);
  background: #fff;
}
.custom .content .card .filter {
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  display: flex;
  justify-content: flex-end;
  padding: 0 24px;
}
.custom .content .card .filter .operate {
  display: flex;
  box-sizing: border-box;
  height: 72px;
  padding: 12px 0;
  align-items: center;
}
.custom .content .wrap,
.detail .paint {
  flex: 1;
}
.detail .card,
.detail .mapRow {
  height: 100%;
}
.detail .opearte {
  display: flex;
  align-items: center;
}
.detail .opearte .btn {
  height: 38px;
  line-height: 38px;
  padding: 0 16px;
  border-radius: 8px;
  margin-left: 12px;
  font-size: 16px;
  color: var(--main-font-color6);
  cursor: pointer;
}
.detail .opearte .btn.y {
  color: #fff;
  background: var(--main-color);
  border: 1px solid var(--main-color);
  box-shadow: var(--main-box-shadow);
}
.detail .scroll {
}
.detail .piece {
  height: 100%;
  box-sizing: border-box;
  padding: 24px 0;
  background: #fff;
}
.detail .piece .title {
  position: relative;
  margin-bottom: 12px;
}
.detail .piece .title.w:after {
  position: absolute;
  content: '*';
  color: var(--el-color-danger);
  top: 2px;
  left: -10px;
}
.detail .piece .pdr {
  padding-right: 12px;
}
.detail .piece .line {
  width: 100%;
}
.detail .piece .mbg {
  margin-bottom: 24px;
}
.detail .piece .line .in {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detail .piece .line .in .col4 {
  width: 40%;
}
.detail .piece .line .in .col6 {
  width: 60%;
}
.detail .piece .line .in .note {
  height: 30px;
  line-height: 30px;
}

.management {
  border-top: 1px solid var(--main-box-color4);
  padding: 24px 24px 0 24px;
}
.management .line {
  margin-bottom: 24px;
}
.management .line .title {
  font-size: 16px;
  color: var(--main-font-color9);
  height: 30px;
  position: relative;
}
.management .line .title.w:after {
  position: absolute;
  content: '*';
  color: var(--el-color-danger);
  top: 2px;
  left: -10px;
}
.management .line.r {
  margin-right: 12px;
}
.management .line.l {
  margin-left: 12px;
}
.management .delete {
  width: 40px;
  height: 28px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-shadow: 0 12px 12px 0 rgba(0, 0, 0, 0.06);
  color: var(--main-font-color6);
  text-align: center;
  font-size: 14px;
  line-height: 28px;
  cursor: pointer;
}
.tableData {
  width: 100%;
  margin-bottom: 24px;
}
.dialog-footer .btn {
  display: inline-block;
  width: 64px;
  height: 38px;
  line-height: 38px;
  border-radius: 6px;
  text-align: center;
  font-size: 14px;
  cursor: pointer;
}
.dialog-footer .btn.p {
  background: var(--main-color);
  color: #fff;
  box-shadow: var(--main-box-shadow);
  border: 1px solid var(--main-color);
  margin-left: 8px;
}
.dialog-footer .btn.c {
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-shadow: 0 12px 12px 0 rgba(0, 0, 0, 0.06);
  color: var(--main-font-color6);
}
.container .content .card .tag .title {
  display: flex;
  padding: 8px 0;
}
.container .content .card .tag .title .t {
  display: flex;
}
.container .content .card .tag .title .t .n {
  height: 40px;
  line-height: 40px;
}
.container .content .card .tag .title .arrow {
  cursor: pointer;
}
.container .content .card .tag .title .btn {
  margin-left: 12px;
  cursor: pointer;
}

.container .content .card .tag .title i:before {
  font-size: 18px;
}
.container .content .card .tag .piece {
  margin-bottom: 12px;
  overflow: hidden;
}
.container .content .card .tag .piece .paint {
  overflow: hidden;
}
.custom svg path {
  stroke-width: 50px;
  stroke: black;
  fill: black;
}
.custom .content .card .tag .tagButton {
  float: left;
  font-size: 14px;
  border-color: rgb(247, 113, 47);
  display: flex;
  align-items: center;
  border-radius: 5px;
  height: 40px;
  margin: 0 15px 2px 0;
  border-width: 1px;
  border-style: solid;
}
.custom .content .card .tag .tagButton.more {
  cursor: pointer;
}
.custom .content .card .tag .tagButton .n {
  color: #fff;
  background: rgb(247, 113, 47);
  height: 38px;
  line-height: 38px;
  font-size: 16px;
  padding: 3px 8px;
  margin-right: 3px;
}
.custom .content .card .tag .tagButton i {
  margin: 0 3px;
  cursor: pointer;
}
.custom .content .card .tag .tagButton .edit,
.custom .content .card .tag .tagButton .close {
  padding: 0 4px;
}
