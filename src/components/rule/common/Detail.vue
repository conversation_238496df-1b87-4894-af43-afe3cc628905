<template>
  <div class="detail">
    <!-- 标题-->
    <CommonTitle is-back>
      <template #default>
        <div class="opearte">
          <el-button type="primary" @click="submit">提交</el-button>
        </div>
      </template>
    </CommonTitle>

    <div class="paint">
      <div class="card">
        <el-row class="mapRow">
          <el-col :span="18">
            <ShowMap v-model:map-data="mapData" />
          </el-col>
          <el-col :span="6">
            <FormContent ref="formSubmit" :size :rule-type :rule-id></FormContent>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
// 组件
import CommonTitle from '@/components/common/commonTitle/CommonTitle.vue'
import FormContent from '@/components/rule/common/FormContent.vue'
import ShowMap from '@/components/map/ShowMap.vue'
const { size } = defineProps(['size'])
// 尺寸
import useComponent from '@/hooks/useComponent.js'
// 跳转
const { setPath, pathData, getParam } = useComponent()
// 地图 // protectedAreaWidth: 300, airports: [], emergencyAirports: []
const mapData = reactive({
  displayStyle: {},
  geometries: [],
  geoJson: [],
  extra: {},
  id: '',
})
const formSubmit = useTemplateRef('formSubmit')
const ruleType = ref(getParam?.().data.type)
const ruleId = ref(getParam?.().data.id ? getParam?.().data.id : null)

/** 初始化 **/
initDate()
function initDate() {}

// 提交
const submit = () => formSubmit.value?.submit()
</script>

<style>
@import 'custom.css';
</style>
<style scoped lang="less">
:deep(.el-button) {
  box-shadow: var(--main-box-shadow);
}
</style>
