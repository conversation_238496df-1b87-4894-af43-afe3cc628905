<template>
  <div class="custom">
    <div class="navigation">
      <div class="title">{{ paintTitle }}</div>
    </div>
    <div class="search">
      <div class="card">
        <el-row>
          <el-col :span="6">
            <div class="line">
              <div class="label">规则名称</div>
              <div class="in">
                <el-input v-model="tableConfig.search.ruleNameLike" placeholder="请输入规则名称模糊查询"></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">飞行限制</div>
              <div class="in">
                <el-select v-model="tableConfig.search.flyRestrict" placeholder="请选择">
                  <el-option v-for="item in flyRestrict" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">告警类型</div>
              <div class="in">
                <el-select v-model="alarm.typeValue" placeholder="请选择" @change="getAlarm">
                  <el-option
                    v-for="item in alarm.typeGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">告警等级</div>
              <div class="in">
                <el-select v-model="alarm.levelValue" multiple clearable @change="gerAlarmLevel" placeholder="请选择">
                  <el-option
                    v-for="item in alarm.levelGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">所属地区</div>
              <div class="in">
                <el-select
                  v-model="tableConfig.area"
                  placeholder="请选择所属地区"
                  style="width: 100%"
                  @change="getCity"
                >
                  <el-option
                    v-for="item in areaOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="isAreaOptionDisabled(item)"
                  />
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">主体类型</div>
              <div class="in">
                <el-select
                  v-model="tableConfig.search.subjectType"
                  placeholder="请选择规则主体类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in subject.typeGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="line">
              <div class="label">主体名称</div>
              <div class="in">
                <el-input v-model="tableConfig.search.subjectNameLike" placeholder="请输入主体名称模糊查询"></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="operate">
              <div class="btn n" @click="search">查询</div>
              <div class="btn d" @click="reset">重置</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="content">
      <div class="card">
        <div class="filter">
          <div class="operate">
            <div class="btn n" @click="add">新增</div>
            <div class="btn d" @click="del">删除</div>
          </div>
        </div>
        <Table
          v-model:config="tableConfig"
          v-model:reload="reload"
          @edit="edit"
          @select="selectTb"
          @active="active"
        ></Table>
      </div>
    </div>
  </div>
</template>

<script setup>
import userOrg from '@/hooks/useOrg.js'
// 组件
import Table from '@/components/common/Table.vue'
const { size } = defineProps(['size'])
import { ElMessage, ElMessageBox } from 'element-plus'
import * as common from '@/utils/common.js'
import useComponent, { PathType } from '@/hooks/useComponent.js'
const { orgId, cityName } = userOrg()
import { useDictionary } from '@/hooks/useDictionary.js'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
import * as api from '@/request/api'
// 数据块尺寸
// 标题
const { pathData, setPath } = useComponent()
const paintTitle = ref(pathData.value.name)
/** 表格初始化 **/
const tableConfig = reactive({
  api: 'rule_aggregate_list',
  select: true,
  pagination: true,
  active: true,
  operate: 1,
  search: {
    ruleType: 'DYNAMIC',
    cityCodeList: undefined,
    ruleNameLike: undefined,
    flyRestrict: undefined,
    subjectType: undefined,
    subjectNameLike: undefined,
    isEnterAlarm: undefined,
    isLeaveAlarm: undefined,
    isHeightAlarm: undefined,
    alarmLevelList: JSON.stringify([0]),
    defaultRule: 0,
  },
  column: [
    {
      prop: 'ruleName',
      label: '名称',
      template: true,
    },
    {
      prop: 'cityName',
      label: '地区',
      template: true,
    },
    {
      prop: 'flyRestrict',
      label: '飞行限制',
      template: false,
      formatter: v => {
        return common.getFlyRestrictName(v.flyRestrict)
      },
    },
    {
      prop: 'subjectType',
      label: '规则主体类型',
      template: false,
      formatter: v => {
        return v.subjectNameList[0]
      },
    },
    {
      prop: 'subjectType',
      label: '规则主体',
      template: false,
      formatter: v => {
        return common.getSubjectTypeName(v.subjectTypeList[0])
      },
    },
    {
      prop: 'alarmTypeName',
      label: '告警类型',
      template: false,
      formatter: v => {
        let n
        if (v.enterAlarmLevel > 0) {
          n = '靠近告警'
        } else if (v.leaveAlarmLevel > 0) {
          n = '飞离告警'
        } else {
          n = '高度告警'
        }
        return n
      },
    },
    {
      prop: 'alarmTypeLevel',
      label: '规则告警等级',
      template: false,
      formatter: v => {
        let n
        if (v.enterAlarmLevel > 0) {
          n = v.enterAlarmLevel
        } else if (v.leaveAlarmLevel > 0) {
          n = v.leaveAlarmLevel
        } else {
          n = v.heightAlarmLevel
        }
        return n + '级'
      },
    },
    {
      prop: 'bufferWidth',
      label: '缓冲区(米)',
      template: true,
    },
    {
      prop: 'defaultRule',
      label: '默认规则',
      template: false,
      formatter: v => {
        return v.defaultRule === 1 ? '是' : '否'
      },
    },
    {
      prop: 'active',
      label: '激活',
      template: true,
      type: 3,
    },
  ],
})
const reload = ref(0)

/** 查询 **/
const search = () => {
  reload.value = Math.random()
}

// 飞行限制
const flyRestrict = ref([
  {
    label: '适飞',
    value: 'PERMITTED',
  },
  {
    label: '管制',
    value: 'CONTROLLED',
  },
  {
    label: '禁飞',
    value: 'FORBIDDEN',
  },
])

/** 主体 **/
const subject = reactive({
  typeGroup: [
    {
      label: '空域',
      value: 'AIRSPACE',
    },
    {
      label: '航路',
      value: 'LA_AIRWAY',
    },
    {
      label: '无人机航线',
      value: 'UA_AIRLINE',
    },
    {
      label: '障碍物',
      value: 'OBSTACLE',
    },
  ],
})

/** 告警 **/
const alarm = reactive({
  typeGroup: [
    {
      label: '靠近告警',
      value: 'isEnterAlarm',
    },
    {
      label: '飞离告警',
      value: 'isLeaveAlarm',
    },
    {
      label: '高度告警',
      value: 'isHeightAlarm',
    },
  ],
  typeValue: undefined,
  levelGroup: [
    {
      label: '1级',
      value: 1,
    },
    {
      label: '2级',
      value: 2,
    },
    {
      label: '3级',
      value: 3,
    },
    {
      label: '4级',
      value: 4,
    },
  ],
  levelValue: undefined,
})

/** 所属区县 **/
const areaOptions = useDictionary('所属地区')

// 判断区域选项是否禁用
const isAreaOptionDisabled = option => {
  // 绍兴市可以选择所有选项
  if (orgId.value === 'cb8upAzuM07uQyNpzuj05') {
    return false
  }
  // 其他机构只能选择与cityName匹配的选项
  return option.label !== cityName.value
}

const getCity = o => {
  const area = [o]
  tableConfig.search.cityCodeList = JSON.stringify(area)
}

// 类型
const getAlarm = o => {
  tableConfig.search.isLeaveAlarm = 0
  tableConfig.search.isEnterAlarm = 0
  tableConfig.search.isHeightAlarm = 0
  tableConfig.search[o] = 1
}
// 等级
const gerAlarmLevel = o => {
  tableConfig.search.alarmLevelList = JSON.stringify(o)
}

const reset = () => {
  tableConfig.search.ruleNameLike = undefined
  tableConfig.search.flyRestrict = undefined
  tableConfig.search.subjectNameLike = undefined
  tableConfig.search.cityCodeList = undefined
  tableConfig.search.subjectType = undefined
  alarm.typeValue = undefined
  alarm.levelValue = undefined
  tableConfig.search.isEnterAlarm = undefined
  tableConfig.search.isLeaveAlarm = undefined
  tableConfig.search.isHeightAlarm = undefined
  tableConfig.search.alarmLevelList = JSON.stringify([0])
}

/** 操作 **/
// 编辑
const edit = o => {
  setPath('rule/common/Detail.vue', { type: tableConfig.search.ruleType, id: o.id }, { type: PathType.edit })
}
// 激活
const active = o => {
  let status = o.activeStatus ? 1 : 2
  api.rule_status_update({ id: o.id, status: status }).then(() => {
    if (o.activeStatus) {
      ElMessage({
        message: o.ruleName + '已激活',
        type: 'warning',
      })
    } else {
      ElMessage({
        message: o.ruleName + '已停用',
        type: 'warning',
      })
    }
  })
}
/** 新增 **/
const add = () => {
  setPath?.('rule/common/Detail.vue', { type: tableConfig.search.ruleType }, { type: PathType.add })
}
/** 删除 **/
let selectData = []
const selectTb = o => {
  selectData = []
  o.map(item => {
    selectData.push(item.id)
  })
  console.log(selectData)
}
const del = () => {
  if (selectData.length === 0) {
    ElMessage.error(t('message.selectError'))
  } else {
    ElMessageBox.confirm('是否确定删除当前规则', '警告', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        api.rule_delete({ idList: JSON.stringify(selectData) }).then(() => {
          ElMessage({
            message: t('message.deleteSuccess'),
            type: 'success',
          })
          setTimeout(() => {
            reload.value = Math.random()
          }, common.globalTime())
        })
      })
      .catch(() => {})
  }
}
</script>

<style>
@import '../common/custom.css';
</style>
<style scoped lang="less">
.ignore-custom {
  background: #00b046;
  height: v-bind(height);
  width: 100%;
}
</style>
