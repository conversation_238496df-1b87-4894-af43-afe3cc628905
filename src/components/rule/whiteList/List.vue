<template>
  <div class="custom">
    <div class="navigation">
      <div class="title">{{ paintTitle }}</div>
    </div>

    <div class="search">
      <div class="card">
        <el-row>
          <el-col :span="6">
            <div class="line">
              <div class="label">名称</div>
              <div class="in">
                <el-input v-model="tableConfig.search.whitelistNameLike" placeholder="请输入"></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="line">
              <div class="label">有效期</div>
              <div class="in">
                <DateRange
                  v-model="tableConfig.timeSearch"
                  :default-props="{
                    format: 'YYYY-MM-DD HH:mm:ss',
                    valueFormat: 'YYYYMMDDHHmmss',
                    clearable: true,
                    rangeSeparator: '至',
                    startPlaceholder: '开始日期',
                    endPlaceholder: '结束日期',
                  }"
                  @update:modelValue="getChangeDate"
                  :show-radio-group="true"
                />
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <div class="line">
              <div class="label">引用规则</div>
              <div class="in">
                <el-input v-model="tableConfig.search.rule_ruleNameLike" placeholder="输入规则名称模糊查询"></el-input>
              </div>
            </div>
          </el-col>

          <el-col :span="12">
            <div class="line">
              <div class="label">管理对象</div>
              <div class="in">
                <div class="w5">
                  <el-select
                    v-model="tableConfig.search.subjectType"
                    placeholder="请选择管理对象"
                    @change="checkSubject"
                  >
                    <el-option
                      v-for="item in subject.group"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div class="w4">
                  <el-input
                    v-model="subject.nameValue"
                    :placeholder="subject.placeholder"
                    :disabled="subject.nameLock"
                  ></el-input>
                </div>
              </div>
            </div>
          </el-col>

          <el-col :span="6">
            <div class="operate">
              <div class="btn n" @click="search">查询</div>
              <div class="btn d" @click="reset">重置</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="content">
      <div class="card">
        <div class="filter">
          <div class="operate">
            <div class="btn n" @click="add">新增</div>
            <div class="btn d" @click="del">删除</div>
          </div>
        </div>
        <Table
          v-model:config="tableConfig"
          v-model:reload="tableConfigReload"
          @edit="edit"
          @select="selectTb"
          @active="active"
        ></Table>
      </div>
    </div>

    <el-dialog
      v-model="management.visible"
      :title="management.edit ? '编辑' : '新增'"
      width="640"
      top="1vh"
      @close="dataReset"
    >
      <div class="management dialog">
        <el-row>
          <el-col :span="24">
            <div class="line">
              <div class="title w">白名单名称</div>
              <div class="in">
                <el-input v-model="management.param.whitelistName" size="large" placeholder="请输入"></el-input>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="line r">
              <div class="title w">有效期开始时间</div>
              <div class="in">
                <el-date-picker
                  v-model="management.time.start"
                  type="date"
                  placeholder="请选择"
                  size="large"
                  @change="getStart"
                />
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="line l">
              <div class="title w">有效期结束时间</div>
              <div class="in">
                <el-date-picker
                  v-model="management.time.end"
                  type="date"
                  placeholder="∞"
                  size="large"
                  @change="getEnd"
                />
              </div>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="line">
              <div class="title">简介</div>
              <div class="in">
                <el-input v-model="management.param.memo" size="large" placeholder="请输入"></el-input>
              </div>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="line">
              <div class="title w">
                <el-radio-group v-model="management.bindObj" style="margin-bottom: 30px" @change="bindShift">
                  <el-radio-button value="organ">企业</el-radio-button>
                  <el-radio-button value="uav">无人机</el-radio-button>
                </el-radio-group>
              </div>
            </div>

            <div v-show="management.bindObj === 'organ'" class="line">
              <div class="in">
                <div>
                  <el-select
                    v-model="orgs.select"
                    multiple
                    collapse-tags
                    placeholder="请选择"
                    style="width: 100%"
                    size="large"
                    @change="getOrgs"
                  >
                    <el-option
                      v-for="item in orgs.selectGroup"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <div class="tableData">
                    <el-table style="width: 100%; height: 200px" :data="orgs.tableGroup">
                      <el-table-column prop="unitName" label="企业名称" />
                      <el-table-column label="操作">
                        <template #default="scope">
                          <div class="delete" @click="orgsDelete(scope.row)">删除</div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
            <div v-show="management.bindObj === 'uav'" class="line">
              <div class="in">
                <div>
                  <el-select
                    v-model="uav.select"
                    multiple
                    collapse-tags
                    placeholder="请选择"
                    style="width: 100%"
                    size="large"
                    @change="getUAV"
                  >
                    <el-option
                      v-for="item in uav.selectGroup"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <div class="tableData">
                    <el-table style="width: 100%; height: 200px" :data="uav.tableGroup">
                      <el-table-column prop="sn" label="SN码" width="120" />
                      <el-table-column prop="uavName" label="无人机名称" width="120" />
                      <el-table-column prop="uavPersonName" label="主体" width="120" />
                      <el-table-column prop="uavPersonPhone" label="手机号码" width="120" />
                      <el-table-column label="操作">
                        <template #default="scope">
                          <div class="delete" @click="uavDelete(scope.row)">删除</div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="line">
              <div class="title">规则</div>
              <div class="in">
                <el-select
                  v-model="rule.select"
                  multiple
                  collapse-tags
                  placeholder="请选择"
                  style="width: 100%"
                  size="large"
                  @change="getRule"
                >
                  <el-option
                    v-for="item in rule.selectGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </el-col>
          <div class="tableData">
            <el-table style="width: 100%; height: 200px" :data="rule.tableGroup">
              <el-table-column prop="ruleName" label="名称" />
              <el-table-column prop="ruleType" label="类型" />
              <el-table-column label="操作">
                <template #default="scope">
                  <div class="delete" @click="ruleDelete(scope.row)">删除</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-row>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn c" @click="dataReset">取消</div>
          <div class="btn p" @click="submit">确定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
const { proxy } = getCurrentInstance()
import * as common from '@/utils/common.js'
import * as api from '@/request/api'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const { size } = defineProps(['size'])
// 组件
import Table from '@/components/common/Table.vue'
const tabPosition = ref(null)
import DateRange from '@/components/common/dateRange/DateRange.vue'
// 表格
const timeSearch = ref([new Date(2025, 0, 23, 12, 0), new Date(2025, 1, 20, 12, 0)])
const tableConfig = reactive({
  select: true,
  pagination: true,
  active: true,
  api: 'whitelist_aggregate',
  timeSearch: null,
  operate: 1,
  search: {
    whitelistNameLike: undefined,
    subjectType: undefined,
    unit_unitNameLike: undefined,
    uav_snLike: undefined,
    rule_ruleNameLike: undefined,
    startTime: undefined,
    endTime: undefined,
    deleted: 0,
    offset: 0,
    limit: 10,
  },
  column: [
    {
      prop: 'whitelistName',
      label: '名称',
      width: 200,
      template: true,
    },
    {
      prop: 'subject',
      label: '管理对象类型',
      width: 120,
      template: false,
      formatter: v => {
        return v.uavList.length === 0 ? '企业' : '无人机'
      },
    },
    {
      prop: 'subject',
      label: '管理对象',
      template: false,
      formatter: v => {
        let n = []
        if (v.uavList.length > 0) {
          v.uavList.map(item => {
            n.push(item.sn)
          })
        } else {
          v.unitList.map(item => {
            n.push(item.unitName)
          })
          console.log()
        }
        return n.join(',')
      },
    },
    {
      prop: 'rule',
      label: '引用规则',
      template: false,
      formatter: v => {
        let n = []
        if (v.ruleList.length > 0) {
          v.ruleList.map(item => {
            n.push(item.ruleName)
          })
        }
        return n.join(',')
      },
    },
    {
      prop: 'startTime',
      label: '开始有效期',
      template: false,
      formatter: v => {
        return v.startTime === -1 ? '长期有效' : common.formatTime(v.startTime).t6
      },
    },
    {
      prop: 'endTime',
      label: '结束有效期',
      template: false,
      formatter: v => {
        return v.endTime === -1 ? '长期有效' : common.formatTime(v.endTime).t6
      },
    },
    {
      prop: 'active',
      label: '激活',
      template: true,
      type: 3,
    },
  ],
})
const tableConfigReload = ref(0)

// 标题
import useComponent from '@/hooks/useComponent.js'
const { pathData } = useComponent()
const paintTitle = ref(pathData.value.name)

/** 查询 **/
// 时间
let today = new Date()
let yesterday = new Date()

getInitDate()
function getInitDate() {
  today.setHours(23, 59, 59, 999)
  yesterday.setDate(today.getDate() - 1)
  yesterday.setHours(0, 0, 0, 0)
}

const getChangeDate = o => {
  if (o.beginTime === -1) {
    tableConfig.search.startTime = undefined
    tableConfig.search.endTime = undefined
  } else {
    tableConfig.search.startTime = o.beginTime
    tableConfig.search.endTime = o.endTime
  }
}

// 管理对象
const subject = reactive({
  group: [
    {
      label: '机构',
      value: 'UNIT',
    },
    {
      label: '无人机',
      value: 'UAV',
    },
  ],
  placeholder: '请选择管理对象',
  checkValue: '',
  nameValue: '',
  nameLock: true,
})

const checkSubject = o => {
  console.log(o)
  if (o === 'UNIT') {
    tableConfig.search.unit_unitNameLike = subject.nameValue
    tableConfig.search.uav_snLike = undefined
    subject.placeholder = '请输入企业名称模糊查询'
  } else {
    tableConfig.search.uav_snLike = subject.nameValue
    tableConfig.search.unit_unitNameLike = undefined
    subject.placeholder = '请输入SN码模糊查询'
  }
  subject.nameLock = false
}

/** 激活 **/
const active = o => {
  let status = o.activeStatus ? 1 : 2
  api.whitelist_update({ id: o.id, status: status }).then(() => {
    if (o.activeStatus) {
      ElMessage({
        message: o.whitelistName + '已激活',
        type: 'warning',
      })
    } else {
      ElMessage({
        message: o.whitelistName + '已停用',
        type: 'warning',
      })
    }
  })
}

/** 查询 **/
const search = () => {
  if (tableConfig.search.subjectType === 'UNIT') {
    tableConfig.search.unit_unitNameLike = subject.nameValue
    tableConfig.search.uav_snLike = undefined
  } else {
    tableConfig.search.unit_unitNameLike = subject.nameValue
    tableConfig.search.unit_unitNameLike = undefined
  }
  console.log(tableConfig.search)
  tableConfigReload.value = Math.random()
}
/** 重置 **/
const reset = () => {
  tableConfig.search.whitelistNameLike = undefined
  tableConfig.timeSearch = null
  tableConfig.search.startTime = undefined
  tableConfig.search.endTime = undefined
  tableConfig.search.rule_ruleNameLike = undefined
  tableConfig.search.uav_snLike = undefined
  tableConfig.search.unit_unitNameLike = undefined
  tableConfig.search.subjectType = undefined
  subject.placeholder = '请选择管理对象'
  subject.nameValue = ''
  subject.nameLock = true
}

/** 编辑 **/
const management = reactive({
  visible: false,
  edit: false,
  lock: false,
  loading: false,
  bindObj: 'organ',
  param: {
    whitelistName: '',
    uavIds: '',
    unitIds: '',
    startTime: '',
    endTime: -1,
    memo: '',
    status: 1,
  },
  time: {
    start: '',
    end: '',
  },
})
const add = () => {
  management.visible = true
  management.time.start = common.newDateToFormatTime().t3
  management.param.startTime = common.newDateToFormatTime().t1
  management.edit = false
  searchRule('')
  searchUAV('')
  searchOrgs('')
}
const edit = o => {
  management.param.whitelistName = o.whitelistName
  management.param.memo = o.memo
  management.time.start = o.startTime === -1 ? '' : common.formatTime(o.startTime).t6
  management.param.startTime = o.startTime
  management.time.end = o.endTime === -1 ? '' : common.formatTime(o.endTime).t6
  management.param.endTime = o.endTime
  management.param.id = o.id
  management.edit = true
  management.visible = true
  if (o.ruleList && o.ruleList.length > 0) {
    let rule_ids = []
    o.ruleList.map(item => {
      if (item.status === 1) {
        rule_ids.push(item.id)
      }
    })
    searchRule(JSON.stringify(rule_ids))
  } else {
    searchRule()
  }
  if (o.uavList && o.uavList.length > 0) {
    let uav_ids = []
    o.uavList.map(item => {
      uav_ids.push(item.id)
    })
    management.bindObj = 'uav'
    searchUAV(JSON.stringify(uav_ids))
  } else {
    searchUAV()
  }
  if (o.unitIds && o.unitIds !== '[]') {
    management.bindObj = 'organ'
    searchOrgs(o.unitIds)
  } else {
    searchOrgs()
  }
}
const getStart = o => {
  management.param.startTime = common.newDateToFormatTime(o).t1
}
const getEnd = o => {
  management.param.endTime = common.newDateToFormatTime(o).t1
}

// 机构查询
const orgs = reactive({
  selectGroup: [],
  tableGroup: [],
  select: [],
})

function searchOrgs(ids) {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 99,
  }
  api.unit_list(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      d.map(item => {
        orgs.selectGroup.push({
          id: item.id,
          unitName: item.unitName,
          value: item.id,
          label: item.unitName,
        })
      })
    }
    if (ids) {
      orgs.select = JSON.parse(ids)
      getOrgs(orgs.select)
    }
  })
}

const getOrgs = o => {
  uav.select = []
  uav.tableGroup = []
  orgs.tableGroup = orgs.selectGroup.filter(item => o.includes(item.id))
}

const orgsDelete = o => {
  const index = orgs.tableGroup.findIndex(item => item.id === o.id)
  orgs.select.splice(index, 1)
  orgs.tableGroup.splice(index, 1)
}

// 无人机查询
const uav = reactive({
  selectGroup: [],
  tableGroup: [],
  select: [],
})
function searchUAV(ids) {
  let p = {
    status: 1,
    deleted: 0,
    offset: 0,
    limit: 99,
  }
  api
    .uav_list(p)
    .then(data => {
      const d = data.list
      if (d.length > 0) {
        d.map(item => {
          uav.selectGroup.push({
            id: item.id,
            uavName: item.uavName,
            sn: item.sn,
            uavPersonName: item.uavPersonName,
            uavPersonPhone: item.uavPersonPhone,
            value: item.id,
            label: item.sn,
          })
        })
      }
      if (ids) {
        uav.select = JSON.parse(ids)
        console.log('you')
        getUAV(uav.select)
      }
    })
    .catch(() => {})
}

const getUAV = o => {
  orgs.select = []
  orgs.tableGroup = []
  uav.tableGroup = uav.selectGroup.filter(item => o.includes(item.id))
}
const uavDelete = o => {
  const index = uav.tableGroup.findIndex(item => item.id === o.id)
  uav.select.splice(index, 1)
  uav.tableGroup.splice(index, 1)
}

// 机构和无人机切换
const bindShift = () => {}

// 规则
const rule = reactive({
  selectGroup: [],
  tableGroup: [],
  select: [],
})

function searchRule(ids) {
  let p = {
    status: 1,
    deleted: 0,
    defaultRule: 0,
    alarmLevelList: JSON.stringify([0]),
    offset: 0,
    limit: 99,
  }
  api
    .rule_list(p)
    .then(data => {
      const d = data.list
      if (d.length > 0) {
        d.map(item => {
          rule.selectGroup.push({
            id: item.id,
            ruleName: item.ruleName,
            ruleType: item.ruleType === 'STATIC' ? '静态规则' : '动态规则',
            status: item.status === 1 ? '启用' : '停用',
            value: item.id,
            label: item.ruleName,
          })
        })
      }
      if (ids) {
        rule.select = JSON.parse(ids)
        getRule(rule.select)
      }
    })
    .catch(() => {})
}
const getRule = o => {
  rule.tableGroup = rule.selectGroup.filter(item => o.includes(item.id))
}
const ruleDelete = o => {
  const index = rule.tableGroup.findIndex(item => item.id === o.id)
  rule.select.splice(index, 1)
  rule.tableGroup.splice(index, 1)
}

const submit = () => {
  if (management.lock) {
    ElMessage.error(t('message.lockError'))
    return
  }
  management.param.ruleIds = JSON.stringify(rule.select)
  management.param.unitIds = JSON.stringify(orgs.select)
  management.param.uavIds = JSON.stringify(uav.select)
  if (management.param.whitelistName === '') {
    ElMessage.error(t('message.whitelistNameLack'))
  } else if (management.param.startTime === '' || management.param.startTime === '19700101000000') {
    ElMessage.error(t('message.whitelistStartTimeLack'))
  } else if (management.param.uavIds === '[]' && management.param.unitIds === '[]') {
    ElMessage.error(t('message.uavLack'))
  } else {
    let url
    let p = {}
    for (let i in management.param) {
      p[i] = management.param[i]
    }
    if (management.edit) {
      p.id = management.param.id
      url = 'whitelist_update'
    } else {
      url = 'whitelist_add'
    }
    management.lock = true
    api[url](p)
      .then(() => {
        ElMessage({
          message: t('message.saveSuccess'),
          type: 'success',
        })
        setTimeout(() => {
          tableConfigReload.value = Math.random()
          dataReset()
        }, common.globalTime())
      })
      .catch(() => {
        management.lock = false
      })
  }
}

/**  删除  **/
let selectData = []
const selectTb = o => {
  selectData = []
  o.map(item => {
    selectData.push(item.id)
  })
}
const del = () => {
  if (selectData.length === 0) {
    ElMessage.error(t('message.selectError'))
  } else {
    ElMessageBox.confirm('是否确定删除当前白名单', '警告', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        api.whitelist_delete({ idList: JSON.stringify(selectData) }).then(() => {
          ElMessage({
            message: t('message.deleteSuccess'),
            type: 'success',
          })
          setTimeout(() => {
            tableConfigReload.value = Math.random()
            dataReset()
          }, common.globalTime())
        })
      })
      .catch(() => {
        ElMessage.error(data.msg)
      })
  }
}

function dataReset() {
  management.lock = false
  management.visible = false
  management.edit = false
  management.param.id = ''
  management.param.whitelistName = ''
  management.param.ruleIds = ''
  management.param.uavIds = ''
  management.param.startTime = ''
  management.param.endTime = -1
  management.param.memo = ''
  management.param.status = 1
  management.time.start = ''
  management.time.end = ''
  for (let i in rule) {
    rule[i] = []
  }
  for (let i in uav) {
    uav[i] = []
  }
  for (let i in orgs) {
    orgs[i] = []
  }
  selectData = []
}
</script>

<style>
@import '../common/custom.css';
</style>
