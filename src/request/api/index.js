import { get, post } from '@/request/common/request'
export const getCaptcha = params => {
  return get('/system-mgr-api/profile/captcha/get', params)
}

// 登录
export const login = params => {
  return post('/system-mgr-api/profile/login', params)
}

export const getSession = params => {
  return post('/system-mgr-api/profile/get_session', params)
}

// 配置数据查询
export const listConfig = params => {
  return post('/business-mgr-api/admin/system/listConfig', params)
}
// 空域模型-查询
export const airspace_model_list = params => {
  return post('/business-mgr-api/admin/business/airspace_model/list', params)
}

// 空域模型-保存
export const airspace_model_save = params => {
  return post('/business-mgr-api/admin/business/airspace_model/create', params)
}

/** 飞行计划 **/
// 查询
export const flight_plan = params => {
  return post('/business-mgr-api/admin/business/flight_plan/list', params)
}
// 备案
export const filing = params => {
  return post('/business-mgr-api/admin/business/filing/list', params)
}
// 无人驾驶航空器-查询
export const uav_list = params => {
  return post('/business-mgr-api/admin/business/uav/list', params)
}
// 无人驾驶员-查询
export const pilot_list = params => {
  return post('/business-mgr-api/admin/business/uav_pilot/list', params)
}
// 计划空域-查询
export const plan_airspace_list = params => {
  return post('/business-mgr-api/admin/business/plan_airspace/list', params)
}
// 计划审批
export const plan_update_status = params => {
  return post('/business-mgr-api/admin/business/flight_plan/update_status', params)
}

/** 规则 **/
// 单表查询
export const rule_list = params => {
  return post('/business-mgr-api/admin/business/rule/list', params)
}
// 聚合查询
export const rule_aggregate_list = params => {
  return post('/business-mgr-api/admin/business/rule/aggregate_list', params)
}
// 新增
export const rule_single_create = params => {
  return post('/business-mgr-api/admin/business/rule/create', params)
}
// 修改
export const rule_single_update = params => {
  return post('/business-mgr-api/admin/business/rule/update', params)
}
// 聚合新增
export const rule_create = params => {
  return post('/business-mgr-api/admin/business/rule/aggregate_create', params)
}
// 聚合编辑
export const rule_update = params => {
  return post('/business-mgr-api/admin/business/rule/aggregate_update', params)
}
// 删除
export const rule_delete = params => {
  return post('/business-mgr-api/admin/business/rule/aggregate_delete', params)
}
// 规则单表修改-启用/停用 状态
export const rule_status_update = params => {
  return post('/business-mgr-api/admin/business/rule/update', params)
}
// 规则-查询主体
export const rule_list_subject = params => {
  return post('/business-mgr-api/admin/business/rule_subject/list_subject', params)
}
// 规则-绑定主体
export const bind_subject = params => {
  return post('/business-mgr-api/admin/business/rule_subject/bind', params)
}
// 测试
export const get_captcha = params => {
  return post('/business-mgr-api/app/system/get_captcha', params)
}
/** 白名单 **/
// 单表查询
export const whitelist = params => {
  return post('/business-mgr-api/admin/business/whitelist/list', params)
}
// 聚合查询
export const whitelist_aggregate = params => {
  return post('/business-mgr-api/admin/business/whitelist/aggregate_list', params)
}
// 新增
export const whitelist_add = params => {
  return post('/business-mgr-api/admin/business/whitelist/create', params)
}
// 修改
export const whitelist_update = params => {
  return post('/business-mgr-api/admin/business/whitelist/update', params)
}
// 删除
export const whitelist_delete = params => {
  return post('/business-mgr-api/admin/business/whitelist/delete', params)
}
// 单位-查询
export const unit_list = params => {
  return post('/business-mgr-api/admin/business/unit/list', params)
}

/** 字典 **/
// 查询
export const dic_list = params => {
  return post('/system-mgr-api/dic/list', params)
}
// 新增
export const dic_add = params => {
  return post('/system-mgr-api/dic/create_item', params)
}
// 修改
export const dic_edit = params => {
  return post('/system-mgr-api/dic/update_item', params)
}
// 删除
export const dic_delete = params => {
  return post('/system-mgr-api/dic/delete_item', params)
}

/** 航空信息资料 **/
// 查询
export const info_list = params => {
  return post('/business-mgr-api/admin/business/info/list', params)
}
/** 空域管理 **/
// 查询
export const airspace_aggregate_list = params => {
  return post('/business-mgr-api/admin/business/airspace/aggregate_list', params)
}
// 新增
export const airspace_aggregate_add = params => {
  return post('/business-mgr-api/admin/business/airspace/aggregate_create', params)
}
// 修改
export const airspace_aggregate_update = params => {
  return post('/business-mgr-api/admin/business/airspace/aggregate_update', params)
}
// 删除
export const airspace_aggregate_delete = params => {
  return post('/business-mgr-api/admin/business/airspace/delete', params)
}
// 高度层查询
export const flight_level_list = params => {
  return post('/business-mgr-api/admin/business/flight_level/list', params)
}
/** 航线管理 **/
// 查询
export const airline_aggregate_list = params => {
  return post('/business-mgr-api/admin/business/ua_airline/aggregate_list', params)
}
// 新增
export const airline_aggregate_add = params => {
  return post('/business-mgr-api/admin/business/ua_airline/aggregate_create', params)
}
// 修改
export const airline_aggregate_update = params => {
  return post('/business-mgr-api/admin/business/ua_airline/aggregate_update', params)
}
// 删除
export const airline_aggregate_delete = params => {
  return post('/business-mgr-api/admin/business/ua_airline/delete', params)
}
/** 地图管理 **/
// 标记单表查询
export const mark_place_list = params => {
  return post('/business-mgr-api/admin/business/place/list', params)
}
// 标记聚合查询
export const mark_aggregate_list = params => {
  return post('/business-mgr-api/admin/business/place/aggregate_list', params)
}

// 标记聚合新增
export const mark_aggregate_create = params => {
  return post('/business-mgr-api/admin/business/place/aggregate_create', params)
}

// 标记聚合修改
export const mark_aggregate_update = params => {
  return post('/business-mgr-api/admin/business/place/aggregate_update', params)
}

// 标记聚合删除
export const mark_aggregate_delete = params => {
  return post('/business-mgr-api/admin/business/place/aggregate_delete', params)
}

//poi列表查询
export const poi_list = params => {
  return post('/business-mgr-api/admin/air/poi/list', params)
}

//poi点删除
export const poi_delete = params => {
  return post('/business-mgr-api/admin/air/poi/delete', params)
}

//poi新增
export const poi_create = params => {
  return post('/business-mgr-api/admin/air/poi/create', params)
}

//poi编辑
export const poi_update = params => {
  return post('/business-mgr-api/admin/air/poi/update', params)
}

//障碍物列表查询
export const obstacle_list = params => {
  return post('/business-mgr-api/admin/air/obstacle/list', params)
}

//障碍物删除
export const obstacle_delete = params => {
  return post('/business-mgr-api/admin/air/obstacle/delete', params)
}

//障碍物新增
export const obstacle_create = params => {
  return post('/business-mgr-api/admin/air/obstacle/create', params)
}

//障碍物编辑
export const obstacle_update = params => {
  return post('/business-mgr-api/admin/air/obstacle/update', params)
}

//航路列表查询
export const la_airway_list = params => {
  return post('/business-mgr-api/admin/air/la_airway/list', params)
}

//航路新增
export const la_airway_create = params => {
  return post('/business-mgr-api/admin/air/la_airway/create', params)
}

//航路编辑
export const la_airway_update = params => {
  return post('/business-mgr-api/admin/air/la_airway/update', params)
}

//航路删除
export const la_airway_delete = params => {
  return post('/business-mgr-api/admin/air/la_airway/delete', params)
}

//低空机场查询
export const la_airport_list = params => {
  return post('/business-mgr-api/web/business/la_airport/list', params)
}

/** 飞行活动 **/
// 查询
export const flight_activity_list = params => {
  return post('/business-mgr-api/admin/business/flight_activity/list', params)
}

// 查询明细
export const flight_activity_detail = params => {
  return post('/business-mgr-api/admin/business/flight_activity/get', params)
}

// 修改状态
export const flight_activity_update_status = params => {
  return post('/business-mgr-api/admin/business/flight_activity/update_status', params)
}
