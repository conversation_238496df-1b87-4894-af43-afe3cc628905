import { get, post, postUrl } from '@/request/common/request'

// 查询
export function list(data) {
  return post('/business-mgr-api/admin/business/account/list', data)
}

// 查询
export function getUser() {
  return post('/system-mgr-api/profile/base_info/get', {})
}

// 修改信息
export function updateUser(data) {
  return post('/system-mgr-api/profile/base_info/update', data)
}

// 修改密码
export function changePassword(data) {
  return post('/system-mgr-api/profile/change_password', data)
}
