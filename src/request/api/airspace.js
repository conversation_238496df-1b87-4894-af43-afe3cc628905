import request from '@/request/common/request.js'

// 查询
export function list(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/list',
    method: 'post',
    data: data,
  })
}

// 查询
export function planList(data) {
  return request({
    url: '/business-mgr-api/admin/business/flight_activity_airspace/list',
    method: 'post',
    data: data,
  })
}

// 聚合查询
export function aggregateList(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/aggregate_list',
    method: 'post',
    data: data,
  })
}

// 新增
export function add(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/create',
    method: 'post',
    data: data,
  })
}

// 编辑
export function update(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/update',
    method: 'post',
    data: data,
  })
}

// 删除
export function updateStatus(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/update_status',
    method: 'post',
    data: data,
  })
}

// 同意
export function agreeSuggestion(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/agree_suggestion',
    method: 'post',
    data: data,
  })
}

// 恢复
export function recover(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/recover',
    method: 'post',
    data: data,
  })
}

// 删除
export function del(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/delete',
    method: 'post',
    data: data,
  })
}

// 统计
export function count(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/count',
    method: 'post',
    data: data,
  })
}

// 查询
export function modelList(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace_model/list',
    method: 'post',
    data: data,
  })
}

// 空域申请、审批查询
export function approvalList(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/approval/list',
    method: 'post',
    data: data,
  })
}

// 空域申请
export function approvalCreate(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/approval/create',
    method: 'post',
    data: data,
  })
}

// 空域申请修改
export function approvalUpdate(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/approval/update',
    method: 'post',
    data: data,
  })
}

// 空域审批提交
export function approvalCommit(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/approval/commit',
    method: 'post',
    data: data,
  })
}

// 空域审批撤销
// 只允许撤销本人创建的记录
export function approvalApplicantCancel(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/approval/applicant_cancel',
    method: 'post',
    data: data,
  })
}

// 空域审批接收
export function approvalAccept(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/approval/accept',
    method: 'post',
    data: data,
  })
}

// 空域审批审批
export function approvalApprove(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/approval/approve',
    method: 'post',
    data: data,
  })
}

//空域 > 审批 > 申请人删除
export function approvalDelete(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/approval/applicant_delete',
    method: 'post',
    data: data,
  })
}
