import request from '@/request/common/request.js'

// 查询
export function list(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/list',
    method: 'post',
    data: data,
  })
}

// 查询
export function planList(data) {
  return request({
    url: '/business-mgr-api/admin/business/flight_activity_airspace/list',
    method: 'post',
    data: data,
  })
}

// 聚合查询
export function aggregateList(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/aggregate_list',
    method: 'post',
    data: data,
  })
}

// 新增
export function add(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/create',
    method: 'post',
    data: data,
  })
}

// 编辑
export function update(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/update',
    method: 'post',
    data: data,
  })
}

// 删除
export function updateStatus(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/update_status',
    method: 'post',
    data: data,
  })
}

// 同意
export function agreeSuggestion(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/agree_suggestion',
    method: 'post',
    data: data,
  })
}

// 恢复
export function recover(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/recover',
    method: 'post',
    data: data,
  })
}

// 删除
export function del(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/delete',
    method: 'post',
    data: data,
  })
}

// 统计
export function count(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace/count',
    method: 'post',
    data: data,
  })
}

// 查询
export function modelList(data) {
  return request({
    url: '/business-mgr-api/admin/business/airspace_model/list',
    method: 'post',
    data: data,
  })
}
