import { get, post, postUrl } from '@/request/common/request'

// 查询
export function configList(data) {
  return post('/business-mgr-api/admin/system/listConfig', data)
}

// 系统管理 > 文件 > 上传
export function uploadFile(data) {
  return post('/business-mgr-api/admin/system/file/upload', data)
}

// 系统管理 > 文件 > 下载
export function downloadFile(data) {
  return post('/business-mgr-api/admin/system/file/download', data)
}

// 系统管理 > 文件 > 查询
export function fileList(data) {
  return post('/business-mgr-api/admin/system/file/list', data)
}
