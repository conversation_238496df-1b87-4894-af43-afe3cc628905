import { get, post, postUrl } from '@/request/common/request'

// 查询
export function planList(data) {
  return post('/business-mgr-api/admin/business/plan_airway/list', data)
}

// 航路 > 审批 > 查询
export function airwayApprovalList(data) {
  return post('/business-mgr-api/admin/air/la_airway/approval/list', data)
}

// 航路申请
export function airwayApprovalCreate(data) {
  return post('/business-mgr-api/admin/air/la_airway/approval/create', data)
}

// 修改航路申请
export function airwayApprovalUpdate(data) {
  return post('/business-mgr-api/admin/air/la_airway/approval/update', data)
}

// 航路提交
export function airwayApprovalCommit(data) {
  return post('/business-mgr-api/admin/air/la_airway/approval/commit', data)
}

// 航路撤销
export function airwayApprovalCancel(data) {
  return post('/business-mgr-api/admin/air/la_airway/approval/applicant_cancel', data)
}

// 航路接收
export function airwayApprovalAccept(data) {
  return post('/business-mgr-api/admin/air/la_airway/approval/accept', data)
}

// 航路审批
export function airwayApprovalApprove(data) {
  return post('/business-mgr-api/admin/air/la_airway/approval/approve', data)
}
