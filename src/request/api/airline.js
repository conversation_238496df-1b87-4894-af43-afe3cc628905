import { get, post, postUrl } from '@/request/common/request'

// 查询
export function planList(data) {
  return post('/business-mgr-api/admin/business/plan_airline/list', data)
}

// 航线 > 审批 > 查询
export function airlineApprovalList(data) {
  return post('/business-mgr-api/admin/business/ua_airline/approval/list', data)
}

// 航线申请
export function airlineApprovalCreate(data) {
  return post('/business-mgr-api/admin/business/ua_airline/approval/create', data)
}

// 修改航线申请
export function airlineApprovalUpdate(data) {
  return post('/business-mgr-api/admin/business/ua_airline/approval/update', data)
}

// 航线提交
export function airlineApprovalCommit(data) {
  return post('/business-mgr-api/admin/business/ua_airline/approval/commit', data)
}

// 航线撤销
export function airlineApprovalCancel(data) {
  return post('/business-mgr-api/admin/business/ua_airline/approval/applicant_cancel', data)
}

// 航线接收
export function airlineApprovalAccept(data) {
  return post('/business-mgr-api/admin/business/ua_airline/approval/accept', data)
}

// 航线审批
export function airlineApprovalApprove(data) {
  return post('/business-mgr-api/admin/business/ua_airline/approval/approve', data)
}
