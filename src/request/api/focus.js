import request from '@/request/common/request.js'

// 查询
export function list(data) {
  return request({
    url: '/business-mgr-api/admin/business/aircraft_watch/list',
    method: 'post',
    data: data,
  })
}

// 新增
export function addFocus(data) {
  return request({
    url: '/business-mgr-api/admin/business/aircraft_watch/create',
    method: 'post',
    data: data,
  })
}

// 编辑
export function update(data) {
  return request({
    url: '/business-mgr-api/admin/business/aircraft_watch/update',
    method: 'post',
    data: data,
  })
}

// 删除
export function updateStatus(data) {
  return request({
    url: '/business-mgr-api/admin/business/aircraft_watch/update_status',
    method: 'post',
    data: data,
  })
}

// 恢复
export function recover(data) {
  return request({
    url: '/business-mgr-api/admin/business/aircraft_watch/recover',
    method: 'post',
    data: data,
  })
}

// 删除
export function del(data) {
  return request({
    url: '/business-mgr-api/admin/business/aircraft_watch/delete',
    method: 'post',
    data: data,
  })
}

// 统计
export function count(data) {
  return request({
    url: '/business-mgr-api/admin/business/aircraft_watch/count',
    method: 'post',
    data: data,
  })
}
