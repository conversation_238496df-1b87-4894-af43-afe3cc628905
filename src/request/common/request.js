import axios from 'axios'
import { BaseUrl } from '@/request/common/baseUrl.js'
import { ElNotification } from 'element-plus'
import useUser from '@/store/user.js'
import router from '@/router'

const service = axios.create({
  baseURL: BaseUrl(),
  withCredentials: true,
  timeout: 30000,
})

async function interceptorsReq(config) {
  const user = useUser()
  if (user.getAccessId()) config.headers['Access-ID'] = user.getAccessId()
  config.headers['X-Requested-With'] = 'XMLHttpRequest'
  config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
  // config.headers['Content-Type'] = 'application/json'
  return config
}

service.interceptors.request.use(interceptorsReq, error => {
  return Promise.error(error)
})

// 响应拦截器配置
service.interceptors.response.use(
  response => {
    if (response.status === 200) {
      if (response.data?.code === 0 || response.data?.success) return response.data
      if (response.data?.code === '00000') return response.data?.data
      if (response.data?.errorCode === '0' && response.data) return response.data
      if (['A0103', 'A0102', 'A0116', 'A0117', 'A0118', 'A0119'].includes(response.data?.code)) {
        const user = useUser()
        user.clearUserInfo()
        router?.replace({ path: '/login' }).then()
      }
      ElNotification.error({ title: response.data.msg })
      return Promise.reject(response.data)
    } else {
      return Promise.reject(response)
    }
  },
  error => {
    ElNotification.error({ title: error.response.data.msg })
    return Promise.reject(error)
  },
)

export function get(url, params) {
  return service.get(url, {
    params,
  })
}

export function post(url, data) {
  return service.post(url, data)
}

export function postUrl(url, params = {}) {
  // if (params) params.accessId = useUserStore().accessId
  // return service.post(url, {}, { params: generateSignature(params) })

  return service.post(url, {}, { params })
}

export default service
