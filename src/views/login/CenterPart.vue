<template>
  <div class="login-center-part">
    <div class="card">
      <img :src="getImg('company.png')" class="logo" alt="" />
      <div class="title">
        <p>欢迎登录</p>
        <p>空中交通系统</p>
      </div>
    </div>
    <div class="content" :style="{ paddingTop: resetStatus ? '20px' : '36px' }">
      <div class="title" :style="{ paddingBottom: resetStatus ? '16px' : '40px' }">
        {{ resetStatus ? '重置密码并登录' : '用户登录' }}
      </div>
      <CenterPartCommon v-if="!resetStatus" :login-in="loginIn" />
      <CenterPartReset v-else :login-in="loginIn" />
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import useUser from '@/store/user.js'
import { login } from '@/request/api/index.js'
import { getImg } from '@/utils/getAsset.js'
import CenterPartCommon from '@/views/login/CenterPartCommon.vue'
import CenterPartReset from '@/views/login/CenterPartReset.vue'
import useOrg from '@/hooks/useOrg.js'
import useDict from '@/hooks/useDict.js'

const userRouter = useRouter()

const user = useUser()
const resetStatus = ref(false) // 重置密码的状态
const loginIn = (params, password) => {
  login(params)
    .then(res => {
      user.setUserInfo(res.userSession)
      user.setUsername(params.username)
      user.setPassword(password)
      useOrg()
      useDict()
      ElMessage.success('登录成功')
      userRouter.replace('/')
    })
    .catch(err => {
      if (err?.code === 'A0113') {
        user.setUsername(params.username)
        user.setPassword(password)
        resetStatus.value = true
      }
    })
}
</script>

<style scoped lang="less">
.login-center-part {
  width: 720px;
  height: 440px;
  display: flex;
  .card {
    width: 320px;
    height: 440px;
    background: url('../../assets/img/loginPaint.png');
    border-top-left-radius: 9px;
    border-bottom-left-radius: 8px;
    padding-top: 50px;
    padding-left: 50px;
    box-sizing: border-box;
    position: relative;
    .logo {
      margin-bottom: 40px;
    }
    p {
      font-size: 36px;
      margin-bottom: 8px;
      color: #fff;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    width: 400px;
    box-sizing: border-box;
    justify-content: space-between;
    padding-left: 48px;
    padding-right: 48px;
    background: #fff;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    .title {
      font-weight: bold;
      font-size: 24px;
    }
  }
}
</style>
