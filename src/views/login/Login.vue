<template>
  <div class="login">
    <div class="header">
      <img :src="getImg('huasky_logo.png')" alt="" />
      <div style="font-size: 20px">空中交通系统</div>
    </div>
    <CenterPart />
  </div>
</template>

<script setup name="home">
import { getImg } from '@/utils/getAsset.js'
import CenterPart from '@/views/login/CenterPart.vue'
</script>

<style scoped lang="less">
.login {
  width: 100vw;
  height: 100vh;
  background-image: url('../../assets/img/loginBg.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  .header {
    width: 100%;
    height: 72px;
    background: #fff;
    position: absolute;
    top: 0px;
    left: 0px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    img {
      height: 36px;
      margin-right: 12px;
    }
  }
}
</style>
