<template>
  <el-form ref="loginRef" :model="formData" :rules="CommonRule()" class="form">
    <el-form-item prop="username">
      <el-input v-model="formData.username" size="large" placeholder="请输入用户名"></el-input>
    </el-form-item>
    <el-form-item prop="password">
      <el-input v-model="formData.password" size="large" placeholder="请输入密码" type="password"></el-input>
    </el-form-item>
    <el-form-item prop="captchaValue">
      <el-input
        v-model="formData.captchaValue"
        size="large"
        placeholder="请输入验证码"
        style="width: 150px"
        @keyup.enter.native="postLogin"
      ></el-input>
      <img :src="formData.captchaUrl" style="cursor: pointer" @click="freshCaptcha" />
    </el-form-item>
  </el-form>
  <div class="btn" @click="postLogin">登录</div>
</template>

<script setup>
import useUser from '@/store/user.js'
import { CommonRule } from '@/data/Login.js'
import { getCaptcha } from '@/request/api/index.js'
import { hexToBase64 } from '@/utils/helper.js'
import { sm3 } from 'sm-crypto'

const user = useUser()
// 表单
const formData = reactive({
  username: user.username,
  password: user.password,
  captchaId: '',
  captchaValue: '',
  captchaUrl: '',
})
const loginRef = useTemplateRef('loginRef')
const { loginIn } = defineProps(['loginIn'])

const freshCaptcha = () =>
  getCaptcha().then(res => {
    formData.captchaUrl = res.captchaBase64
    formData.captchaId = res.captchaId
  })

const postLogin = () => {
  loginRef.value.validate(valid => {
    if (valid) {
      const params = {
        username: formData.username,
        password: hexToBase64(sm3(formData.password.trim())),
        captchaId: formData.captchaId,
        captchaValue: formData.captchaValue,
      }
      loginIn?.(params, formData.password.trim())
      freshCaptcha()
    }
  })
}
onMounted(freshCaptcha)
</script>

<style scoped lang="less">
.form {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  flex: 1;
  width: 100%;
  :deep(.el-input--large) {
    height: 48px;
  }
  :deep(.el-input__inner::placeholder) {
    color: rgba(0, 0, 0, 0.2);
    font-size: 16px;
  }
  img {
    width: 130px;
    height: 48px;
    margin-left: 24px;
  }
  .el-form-item {
    margin-bottom: 24px;
  }
}
.btn {
  width: 100%;
  text-align: center;
  line-height: 48px;
  height: 48px;
  border-radius: 8px;
  background: var(--el-color-primary);
  color: #fff;
  font-size: 18px;
  border: 0;
  outline: none;
  cursor: pointer;
  box-shadow: 0 12px 12px 0 rgba(247, 113, 47, 0.16);
  margin-bottom: 36px;
}
</style>
