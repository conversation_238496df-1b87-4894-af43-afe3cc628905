<template>
  <div class="header">
    <div class="logo"><img :src="getImg('huasky_icon.png')" alt="" /></div>
    <transition name="fade">
      <div v-if="isExpense" class="title">空中交通系统</div>
    </transition>
    <div class="content">
      <EIcon :name="isExpense ? 'Fold' : 'Expand'" size="24" @click="onToggleSideBar" />
      <div style="display: flex; align-items: center">
        <el-dropdown style="margin-right: 12px">
          <div class="el-dropdown-link" style="min-width: 50px; margin-right: 8px">
            <span class="span-title">{{ orgName }}</span>
            <EIcon name="ArrowDown" size="18" color="black" />
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="changeCity">切换机构</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown>
          <div class="el-dropdown-link">
            <EIcon
              name="User"
              size="18"
              color="white"
              style="width: 28px; height: 28px; border-radius: 28px; background: #f7712f"
            />
            <span class="span-title">{{ user.userInfo?.userName ?? '' }}</span>
            <EIcon name="ArrowDown" size="18" color="black" />
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="userCenter">个人中心</el-dropdown-item>
              <el-dropdown-item @click="logout">退出</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
  <OrgChange ref="cityChangeRef" />
</template>

<script setup>
import { getImg } from '@/utils/getAsset.js'
import { useEventBus } from '@vueuse/core'
import EIcon from '@/components/common/EIcon.vue'
import useUser from '@/store/user.js'
import OrgChange from '@/components/common/OrgChange.vue'
import useOrg from '@/hooks/useOrg.js'
const { orgId, orgName, orgList, init } = useOrg()

const isExpense = ref(true)
const toggleSideBarBus = useEventBus('ToggleSideBar')
const onToggleSideBar = () => {
  isExpense.value = !isExpense.value
  toggleSideBarBus.emit(isExpense.value)
}

// 操作
const userRouter = useRouter()
const user = useUser()
const logout = () => {
  user.clearUserInfo()
  userRouter.replace('/login')
  init.value = false
}

const userCenter = () => {
  setPath('userCenter/UserCenter.vue')
}

const cityChangeRef = useTemplateRef('cityChangeRef')
const changeCity = () => {
  cityChangeRef.value?.onOpen()
}

watch(
  () => orgList.length,
  () => {
    if (orgList.length && !orgId.value)
      nextTick(() => {
        cityChangeRef.value?.onOpen(false)
      })
  },
  {
    immediate: true,
  },
)
</script>

<style scoped lang="less">
.header {
  height: 64px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  display: flex;
  flex-shrink: 0;
  align-items: center;
  .logo {
    padding: 0 16px;
    height: 100%;
    display: flex;
    align-items: center;
    img {
      width: 40px;
      height: 40px;
    }
  }
  .title {
    width: 182px;
    font-size: 18px;
    line-height: 24px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: 600;
    white-space: nowrap;
  }
  .fade-enter-active,
  .fade-leave-active {
    transition: width 0.5s ease;
    overflow: hidden;
  }
  .fade-enter-from,
  .fade-leave-to {
    width: 0 !important;
  }
  .content {
    display: flex;
    flex: 1;
    height: 100%;
    justify-content: space-between;
    padding-right: 16px;
    align-items: center;
    .el-dropdown-link {
      display: flex;
      min-width: 110px;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
    }
    .span-title {
      font-size: 15px;
      font-weight: 300;
      color: rgba(0, 0, 0, 0.9);
      margin: 0 10px;
    }
  }
}
</style>
