<template>
  <div class="home">
    <Header :showUserCenter="showUserCenter" />
    <div class="layout">
      <Menu />
      <div class="container">
        <Breadcrumb />
        <div ref="component" class="component">
          <keep-alive v-if="!freshOption.forceRefresh" :max="3">
            <component :is="currentComponent" :key="freshOption.componentKey" :size="{ width, height }"></component>
          </keep-alive>
        </div>
      </div>
    </div>
    <UserCenter ref="userCenter" />
  </div>
</template>

<script setup>
import { useTemplateRef } from 'vue'
import Header from '@/views/home/<USER>'
import Menu from '@/views/home/<USER>'
import Breadcrumb from '@/views/home/<USER>'
import { useElementSize } from '@vueuse/core'
import useComponent from '@/hooks/useComponent.js'
import useDict from '@/hooks/useDict.js'
import UserCenter from '@/views/home/<USER>'

const comContainer = useTemplateRef('component')
const { width, height } = useElementSize(comContainer)
const { currentComponent, freshOption } = useComponent()
const userCenter = useTemplateRef('userCenter')
const showUserCenter = () => userCenter.value?.toggle?.()
useDict()
</script>

<style scoped lang="less">
.home {
  background-color: #f5f5f5;
  background-image: url('../../assets/img/header.png');
  background-repeat: no-repeat;
  background-size: contain;
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  position: fixed;
  .layout {
    display: flex;
    flex: 1;
    width: 100%;
    .container {
      display: flex;
      flex: 1;
      flex-direction: column;
      border-left: rgb(228, 228, 228) 1px solid;
      min-width: 0; /* 允许子元素收缩 */
      .component {
        display: flex;
        width: 100%;
        flex: 1;
        padding: 6px 24px 24px 24px;
        box-sizing: border-box;
      }
    }
  }
}
</style>
