<template>
  <div class="user-center-item">
    <div class="content">
      <div class="label">{{ label }}</div>
      <el-input
        style="width: 440px; margin-top: 10px"
        placeholder="请输入"
        v-if="showInfo[index] && index !== 4"
        v-model="form[keys[index]]"
      />
      <el-input
        style="width: 440px; margin-top: 10px"
        placeholder="请输入原密码"
        v-if="showInfo[index] && index == 4"
        v-model="form.oldPassword"
        type="password"
        show-password
      />
      <el-input
        style="width: 440px; margin-top: 10px"
        placeholder="请输入新密码"
        v-if="showInfo[index] && index == 4"
        v-model="form.newPassword"
        type="password"
        show-password
      />
      <el-input
        style="width: 440px; margin-top: 10px"
        placeholder="请确认新密码"
        v-if="showInfo[index] && index == 4"
        v-model="form.confirmPassword"
        type="password"
        show-password
      />
      <div class="text" v-if="!showInfo[index]">{{ form[keys[index]] }}</div>
    </div>
    <div class="buttons" v-show="label !== '登陆名'">
      <el-button v-if="editMode" @click="showInfo[index] = false">取消</el-button>
      <el-button type="primary" v-if="editMode" @click="updateUserInfo(index)">确定</el-button>
      <el-button type="primary" v-if="!editMode" @click="showInfo[index] = true">修改</el-button>
    </div>
  </div>
</template>

<script setup>
const { label, index, updateUserInfo } = defineProps(['label', 'index', 'updateUserInfo'])
const showInfo = defineModel('showInfo')
const form = defineModel('form')

const keys = ['loginName', 'userName', 'mobile', 'email', 'password']
const editMode = computed(() => (index === 0 ? false : showInfo.value[index]))
const itemHeight = computed(() => (showInfo.value[index] && index === 4 ? '158px' : '96px'))
</script>

<style scoped lang="less">
.user-center-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: v-bind(itemHeight);
  border-top: 1px solid rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
  .content {
    display: flex;
    flex-direction: column;
    flex: 1;
    .label {
      color: #000000e6;
      font-size: 18px;
      font-weight: 300;
    }
    .text {
      margin-top: 30px;
      font-size: 18px;
      font-weight: 400;
      color: #00000099;
    }
  }

  .buttons {
    display: flex;
    margin-top: 30px;
  }
}
</style>
