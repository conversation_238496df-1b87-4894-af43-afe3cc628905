<template>
  <el-dialog v-model="centerShow" title="个人中心" width="640" align-center :before-close="onClose">
    <div class="user-center">
      <UserCenterItem
        v-for="(label, index) in labels"
        :label="label"
        :index="index"
        :key="index"
        v-model:showInfo="showInfo"
        v-model:form="form"
        v-model:tips="tips"
        :updateUserInfo="updateUserInfo"
        :cancelInfo="cancelInfo"
      />
      <div class="tip">* 密码修改后需重新登录</div>
    </div>
  </el-dialog>
</template>

<script setup>
import UserCenterItem from '@/views/home/<USER>'
import { changePassword, getUser, updateUser } from '@/request/api/user.js'
import { ElMessage } from 'element-plus'
import useUser from '@/store/user.js'
import { hexToBase64 } from '@/utils/helper.js'
import { sm3 } from 'sm-crypto'
import { validEmail, validPassword, validPhone } from '@/utils/validate.js'

const centerShow = ref(false)
defineExpose({
  toggle: () => (centerShow.value = !centerShow.value),
})
const labels = ['登录名', '用户名', '手机号', '邮箱', '密码']

const tips = reactive(['', '', '', ''])

const showInfo = ref([false, false, false, false])
const userRouter = useRouter()
const form = ref({})
const originForm = ref({})

const getUserInfo = () => {
  getUser().then(res => {
    originForm.value = structuredClone(res.user)
    form.value = res.user
    form.value.password = '********'
    form.value.oldPassword = ''
    form.value.newPassword = ''
    form.value.confirmPassword = ''
  })
}

const user = useUser()
const cancelInfo = index => {
  showInfo.value[index] = false
  tips[index - 1] = ''
  if (index === 1) form.value.userName = originForm.value.userName
  if (index === 2) form.value.mobile = originForm.value.mobile
  if (index === 3) form.value.email = originForm.value.email
  if (index === 4) {
    form.value.oldPassword = ''
    form.value.newPassword = ''
    form.value.confirmPassword = ''
  }
}
const updateUserInfo = index => {
  if (index !== 4) {
    if (index === 1 && !form.value.userName.trim()) {
      tips[0] = '用户名不能为空'
      return
    }
    if (index === 2 && !validPhone(form.value.mobile)) {
      tips[1] = '请输入正确的手机号'
      return
    }
    if (index === 3 && !validEmail(form.value.email)) {
      tips[2] = '请输入正确的邮箱'
      return
    }
    showInfo.value[index] = false
    const { password, oldPassword, newPassword, confirmPassword, loginName, ...data } = form.value
    updateUser(data).then(() => {
      ElMessage({
        message: '修改成功',
        type: 'success',
      })
      if (index === 1) user.setUserInfoName(data.userName)
    })
  } else {
    if (!form.value.oldPassword || !form.value.newPassword || !form.value.confirmPassword) {
      tips[3] = '密码不能为空'
      return
    }
    if (form.value.newPassword !== form.value.confirmPassword) {
      tips[3] = '密码不一致'
      return
    }
    const errorMsg = validPassword(form.value.newPassword)
    if (errorMsg) {
      tips[3] = errorMsg
      return
    }
    showInfo.value[index] = false
    const data = {
      password: hexToBase64(sm3(form.value.oldPassword.trim())),
      newPassword: hexToBase64(sm3(form.value.newPassword.trim())),
    }
    changePassword(data).then(() => {
      ElMessage({
        message: '修改成功',
        type: 'success',
      })
      setTimeout(() => {
        user.clearUserInfo()
        userRouter.replace('/login')
      }, 200)
    })
  }
}

const onClose = () => {
  showInfo.value = [false, false, false, false]
  centerShow.value = false
}
onMounted(getUserInfo)
</script>

<style scoped lang="less">
.user-center {
  width: 100%;
  .tip {
    color: red;
    font-size: 13px;
    margin-top: 20px;
  }
}
</style>
