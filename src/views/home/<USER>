<template>
  <div class="menu">
    <el-menu
      :collapse-transition="true"
      :collapse="!isExpense"
      :default-active="currentMenuActive"
      class="menu-content"
    >
      <template v-for="(nodeZero, indexZero) in MenusData" :key="'template-zero-' + indexZero">
        <el-sub-menu
          v-if="(nodeZero.nodeList ?? []).filter(node => !node.isHidden).length > 0 && !nodeZero.isHidden"
          :key="indexZero + '-' + nodeZero?.url ?? ''"
          :index="indexZero.toString()"
        >
          <template #title>
            <EIcon v-if="nodeZero.icon" :name="nodeZero.icon" />
            <span>{{ nodeZero.name }}</span>
          </template>
          <template
            v-for="(nodeOne, indexOne) in (nodeZero.nodeList ?? [])?.filter(node => !node.isHidden)"
            :key="'template-one-' + indexOne"
          >
            <el-sub-menu
              v-if="(nodeOne.nodeList ?? []).filter(node => !node.isHidden).length > 0 && !nodeOne.isHidden"
              :index="indexZero + '-' + indexOne"
            >
              <template #title>
                <span>{{ nodeOne.name }}</span>
              </template>
              <el-menu-item
                v-for="(nodeTwo, indexTwo) in nodeOne.nodeList?.filter(node => !node.isHidden)"
                :key="indexZero + '-' + nodeOne?.url ?? '' + '-' + indexOne + '-' + nodeTwo.url ?? ''"
                :index="indexZero + '-' + indexOne + '-' + indexTwo"
                @click="setPath(nodeTwo.url)"
              >
                <template #title>
                  <span>{{ nodeTwo.name }}</span>
                </template>
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item
              v-if="!(nodeOne.nodeList ?? []).filter(node => !node.isHidden).length"
              :key="indexOne + '-' + nodeOne?.url ?? ''"
              :index="indexZero + '-' + indexOne"
              @click="setPath(nodeOne.url)"
            >
              <template #title>
                <span>{{ nodeOne.name }}</span>
              </template>
            </el-menu-item>
          </template>
        </el-sub-menu>
        <el-menu-item
          v-if="!(nodeZero.nodeList ?? []).filter(node => !node.isHidden).length"
          :key="indexZero + '-' + nodeZero?.url ?? ''"
          :index="indexZero.toString()"
          @click="setPath(nodeZero.url)"
        >
          <EIcon v-if="nodeZero.icon" :name="nodeZero.icon" />
          <template #title>
            <span>{{ nodeZero.name }}</span>
          </template>
        </el-menu-item>
      </template>
    </el-menu>
    <div v-show="isExpense" class="logo">
      <img :src="getImg('huasky_logo.png')" alt="" />
      <span>| 技术支持</span>
    </div>
  </div>
</template>

<script setup>
import useComponent from '@/hooks/useComponent.js'
import { getImg } from '@/utils/getAsset.js'
import MenusData from '@/data/MenusData.js'
import { ref } from 'vue'
import { useEventBus } from '@vueuse/core'
import EIcon from '@/components/common/EIcon.vue'

const isExpense = ref(true)
const toggleSideBarBus = useEventBus('ToggleSideBar')
toggleSideBarBus.on(status => (isExpense.value = status))
const menuWidth = computed(() => (isExpense.value ? '240px' : '64px'))

// 跳转 / 当前激活菜单
const { setPath, currentMenuActive } = useComponent()
</script>
<style scoped lang="less">
.menu {
  transition: width 0.5s ease;
  width: v-bind(menuWidth);
  height: 100%;
  background: #f5f5f5;
  position: relative;
  overflow-x: hidden;
  .menu-content {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 75px;
    width: 100%;
    height: auto;
    overflow-y: auto;
  }
  .logo {
    position: absolute;
    left: 0;
    bottom: 0;
    background: #f5f5f5;
    transition: width 0.5s ease;
    width: 240px;
    display: flex;
    height: 75px;
    align-items: center;
    padding-left: 16px;
    box-sizing: border-box;
    border-top: 1px solid rgb(228, 228, 228);
    img {
      height: 28px;
    }
    span {
      margin-left: 8px;
      color: rgb(32, 78, 151);
    }
  }
}
.menu-content::-webkit-scrollbar {
  width: 0;
}
</style>
<style scoped lang="less">
:deep(.el-menu) {
  --el-menu-item-font-size: 16px;
  background-color: transparent;
  border-right: none;
}
:deep(.el-menu span) {
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.6);
}
</style>
<style scoped lang="less">
:deep(.el-menu-item) {
  background-color: transparent;
  margin: 4px 24px 4px 0px;
}
:deep(.el-menu-item:hover) {
  background-color: var(--el-color-primary-light-8);
  border-radius: 8px;
  margin: 4px 24px 4px 0px;
}
:deep(.el-menu:not(.el-menu--collapse) .el-menu-item.is-active) {
  background-color: var(--el-color-primary);
  border-radius: 8px;
  margin: 4px 24px 4px 0px;
  box-shadow: 0 6px 6px 0 var(--el-color-primary-light-8);
}
:deep(.el-menu:not(.el-menu--collapse) .el-menu-item.is-active span) {
  color: #ffffff;
}
:deep(.el-menu:not(.el-menu--collapse) .el-menu-item.is-active .el-icon svg) {
  color: #ffffff !important;
}
</style>
