<template>
  <el-dialog v-model="tag.show" title="标签管理" width="1080" align-center :before-close="onClose">
    <div class="custom">
      <div class="content">
        <div class="card">
          <div class="tag">
            <div class="piece" v-for="(item, index) in tag.g.children" :key="index">
              <div class="title">
                <div class="t">
                  <div class="n">{{ item.dicName }}</div>
                  <div class="btn n" @click="add(item)"><EIcon name="FolderAdd" style="height: 40px" /></div>
                </div>
              </div>
              <div class="paint" v-if="!item.long">
                <div class="tagButton" v-for="(itemc, indexc) in item.itemList" :key="item.dicId + indexc">
                  <div class="n">{{ itemc.itemValue }}</div>
                  <div class="edit" @click="edit(itemc)"><EIcon name="Edit" style="height: 40px" /></div>
                  <div class="close" @click="del(itemc)"><EIcon name="Close" style="height: 40px" /></div>
                </div>
              </div>
              <div class="paint" v-if="item.long">
                <div v-if="!item.show">
                  <div
                    class="tagButton"
                    v-for="(itemc, indexc) in item.itemList"
                    :key="item.dicId + indexc"
                    v-show="indexc < 10"
                  >
                    <div class="n">{{ itemc.itemValue }}</div>
                    <div class="edit" @click="edit(itemc)"><EIcon name="Edit" style="height: 40px" /></div>
                    <div class="close" @click="del(itemc)"><EIcon name="Close" style="height: 40px" /></div>
                  </div>
                  <div class="tagButton more" @click="getMore(item)">
                    <div class="n">更多</div>
                    <div class="">...</div>
                  </div>
                </div>
                <div v-if="item.show">
                  <div class="tagButton" v-for="(itemc, indexc) in item.itemList" :key="item.dicId + indexc">
                    <div class="n">{{ itemc.itemValue }}</div>
                    <div class="edit" @click="edit(itemc)"><EIcon name="Edit" style="height: 40px" /></div>
                    <div class="close" @click="del(itemc)"><EIcon name="Close" style="height: 40px" /></div>
                  </div>
                  <div class="tagButton more" @click="getMore(item)">
                    <div class="n">收起</div>
                    <div class=""><i class="ri-arrow-up-s-line"></i></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <el-dialog
        v-model="management.visible"
        :title="management.edit ? '编辑' : '新增'"
        width="640"
        top="1vh"
        @close="dataReset"
      >
        <div class="management dialog" v-loading="management.loading">
          <el-row>
            <el-col :span="24">
              <div class="line">
                <div class="title w">标签名称</div>
                <div class="in">
                  <el-input size="large" placeholder="请输入" v-model="management.param.itemValue"></el-input>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <div class="btn c" @click="dataReset">关闭</div>
            <div class="btn p" @click="submit">确定</div>
          </div>
        </template>
      </el-dialog>
    </div>
  </el-dialog>
</template>

<script setup>
import * as common from '@/utils/common.js'
import { useI18n } from 'vue-i18n'
import * as api from '@/request/api'
import EIcon from '@/components/common/EIcon.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
const { t } = useI18n()

const tag = reactive({ g: [], tableData: [], titleG: [], show: false })
defineExpose({
  toggle: () => (tag.show = !tag.show),
})

/** 查询 **/
searchDic()
function searchDic() {
  let p = {
    usage: 'TAG',
  }
  api.dic_list(p).then(data => {
    let d = data.list
    if (d.length > 0) {
      tag.g = common.groupByBelongTo(d)[0]
    }
    console.log(tag.g)
  })
}

const getMore = o => {
  o.show = !o.show
}

/** 编辑操作 **/
const management = reactive({
  title: '',
  visible: false,
  edit: false,
  lock: false,
  loading: false,
  param: {
    itemValue: '',
  },
})

// 新增
const add = o => {
  management.param.dicId = o.dicId
  management.edit = false
  management.visible = true
}
// 编辑
const edit = o => {
  management.param.itemValue = o.itemValue
  management.param.itemSequence = o.itemSequence
  management.param.id = o.id
  management.edit = true
  management.visible = true
}
// 提交
const submit = () => {
  if (management.lock) {
    ElMessage.error(t('message.lockError'))
    return
  }
  if (management.param.itemValue === '') {
    ElMessage.error(t('message.itemValueNone'))
  } else {
    let url
    let p = {}
    if (management.edit) {
      p.id = management.param.id
      url = 'dic_edit'
    } else {
      url = 'dic_add'
    }
    p.dicId = management.param.dicId
    p.itemValue = management.param.itemValue
    p.itemSequence = 0
    management.lock = true
    management.loading = true
    api[url](p)
      .then(() => {
        ElMessage({
          message: t('message.saveSuccess'),
          type: 'success',
        })
        management.loading = false
        searchDic()
        setTimeout(() => {
          dataReset()
        }, common.globalTime())
      })
      .catch(() => {
        management.loading = false
        setTimeout(() => {
          dataReset()
        }, common.globalTime())
      })
  }
}

/**删除**/
const del = o => {
  const c = []
  c.push(o.id)
  ElMessageBox.confirm('是否确定删除当前信息', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      api.dic_delete({ idList: JSON.stringify(c) }).then(() => {
        ElMessage({
          message: t('message.deleteSuccess'),
          type: 'success',
        })
        setTimeout(() => {
          searchDic()
          dataReset()
        }, common.globalTime())
      })
    })
    .catch(() => {})
}
// 取消
const dataReset = () => {
  for (let i in management.param) {
    management.param[i] = ''
  }
  management.lock = false
  management.loading = false
  management.visible = false
}

const onClose = () => {
  tag.show = false
}
</script>

<style>
@import '../../components/rule/common/custom.css';
</style>
<style lang="scss" scoped>
.tag {
  padding: 24px;
  overflow: scroll;
  box-sizing: border-box;
}

.management {
  border-top: 1px solid var(--main-box-color4);
  padding: 12px 0 0 0;
}
.management .title {
  font-size: 16px;
  color: var(--main-font-color9);
  height: 30px;
  position: relative;
}
.management .line .title.w:after {
  position: absolute;
  content: '*';
  color: var(--el-color-danger);
  top: 2px;
  left: -10px;
}
.management .line {
  padding: 6px;
}
.management .line.r {
  margin-right: 12px;
}
.management .line.l {
  margin-left: 12px;
}
</style>
