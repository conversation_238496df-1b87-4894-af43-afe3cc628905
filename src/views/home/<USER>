<template>
  <div class="breadcrumb">
    <div v-for="(item, index) in pathsData.path" :key="index" class="piece">
      <span
        class="name"
        :class="[index < pathsData.path.length - 1 ? 'link' : 'active']"
        :style="{ cursor: item.url && index !== pathsData.path.length - 1 ? 'pointer' : 'default' }"
        @click="toJump(item, index)"
        >{{ item.name }}</span
      >
      <span v-show="index < pathsData.path.length - 1" class="spread">/</span>
    </div>
  </div>
</template>

<script setup>
import useComponent, { PathType } from '@/hooks/useComponent.js'
const { pathsData, setPath } = useComponent()
const toJump = (item, i) => {
  if (!item.url || i === pathsData.value.path.length - 1) return
  setPath?.(item.url, null, { type: PathType.successBack })
}
</script>

<style scoped lang="less">
.breadcrumb {
  padding: 24px 24px 0 24px;
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  .piece {
    .name {
      font-size: 16px;
      margin: 0 3px;
    }
    .name.link {
      font-weight: lighter;
      cursor: default;
      color: rgba(0, 0, 0, 0.6);
    }
    .name.active {
      font-weight: 400 !important;
    }
    .spread {
      color: rgba(0, 0, 0, 0.12);
    }
  }
}
</style>
