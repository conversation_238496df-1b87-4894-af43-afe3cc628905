import { DrawType, MapType } from '@/data/MapConstant.js'
import * as turf from '@turf/turf'

export const LON_MIN = 103.99918
export const LON_MAX = 137.21573
export const LAT_MIN = 18.56204
export const LAT_MAX = 41.449

export const getMapType = type => {
  switch (type) {
    case MapType.POINT:
      return [DrawType.Point]
    case MapType.LINE:
      return [DrawType.Line]
    case MapType.PLANE:
      return [DrawType.Polygon, DrawType.Circle, DrawType.Rectangle]
    default:
  }
  return []
}

// 获取多边形合法性 传入的geos格式为 [[lng,lat],[lng,lat],[lng,lat],...]
export const checkPolygon = geos => {
  if (!geos || !Array.isArray(geos) || geos.length === 0) return { valid: false, message: '坐标不能为空' }
  if (geos.some(geo => geo?.length !== 2 || typeof geo[0] !== 'number' || typeof geo[1] !== 'number'))
    return { valid: false, message: '坐标格式不正确' }
  if (geos.length < 4) return { valid: false, message: '坐标数量不能少于4个' }
  if (geos[0][0] !== geos.at(-1)[0] || geos[0][1] !== geos.at(-1)[1]) return { valid: false, message: '坐标首尾不闭合' }
  const kinks = turf.kinks(turf.lineString(geos))
  if (kinks.features.length > 0) return { valid: false, message: '坐标存在自交叉' }
  return { valid: true, message: '' }
}

export const validateGeometry = geometry => {
  if (!geometry) return { valid: true, message: '' }

  let coordinates = []
  let typeName = ''

  switch (geometry.type) {
    case 'POINT':
      coordinates.push({ lon: geometry.coordinate[0], lat: geometry.coordinate[1] })
      typeName = '点'
      break
    case 'LINE':
      geometry.coordinateList.forEach(c => coordinates.push({ lon: c[0], lat: c[1] }))
      typeName = '线路'
      break
    case 'POLYGON':
      const polygonCheck = checkPolygon(geometry.shellCoordinateList)
      if (!polygonCheck.valid) return polygonCheck
      geometry.shellCoordinateList.forEach(c => coordinates.push({ lon: c[0], lat: c[1] }))
      typeName = '多边形'
      break
    case 'CIRCLE':
      coordinates.push({ lon: geometry.centerCoordinate[0], lat: geometry.centerCoordinate[1] })
      typeName = '圆形'
      break
    case 'RECTANGLE':
      coordinates.push({ lon: geometry.northEastCoordinate[0], lat: geometry.northEastCoordinate[1] })
      coordinates.push({ lon: geometry.southWestCoordinate[0], lat: geometry.southWestCoordinate[1] })
      typeName = '矩形'
      break
  }

  for (const [index, coord] of coordinates.entries()) {
    const pointName = coordinates.length > 1 ? `第 ${index + 1} 个点` : `${typeName}`
    if (coord.lon < LON_MIN || coord.lon > LON_MAX) {
      return { valid: false, message: `${pointName}的经度超出范围 (${LON_MIN} ~ ${LON_MAX})` }
    }
    if (coord.lat < LAT_MIN || coord.lat > LAT_MAX) {
      return { valid: false, message: `${pointName}的纬度超出范围 (${LAT_MIN} ~ ${LAT_MAX})` }
    }
  }

  return { valid: true, message: '' }
}
