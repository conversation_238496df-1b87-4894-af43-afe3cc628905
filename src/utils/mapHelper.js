import { DrawType, MapType } from '@/data/MapConstant.js'
import * as turf from '@turf/turf'

export const getMapType = type => {
  switch (type) {
    case MapType.POINT:
      return [DrawType.Point]
    case MapType.LINE:
      return [DrawType.Line]
    case MapType.PLANE:
      return [DrawType.Polygon, DrawType.Circle, DrawType.Rectangle]
    default:
  }
  return []
}

// 获取多边形合法性 传入的geos格式为 [[lng,lat],[lng,lat],[lng,lat],...]
export const checkPolygon = geos => {
  if (!geos || !Array.isArray(geos) || geos.length === 0) return { valid: false, message: '坐标不能为空' }
  if (geos.some(geo => geo?.length !== 2 || typeof geo[0] !== 'number' || typeof geo[1] !== 'number'))
    return { valid: false, message: '坐标格式不正确' }
  if (geos.length < 4) return { valid: false, message: '坐标数量不能少于4个' }
  if (geos[0][0] !== geos.at(-1)[0] || geos[0][1] !== geos.at(-1)[1]) return { valid: false, message: '坐标首尾不闭合' }
  const kinks = turf.kinks(turf.lineString(geos))
  if (kinks.features.length > 0) return { valid: false, message: '坐标存在自交叉' }
  return { valid: true, message: '' }
}
