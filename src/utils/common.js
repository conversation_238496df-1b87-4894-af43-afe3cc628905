export const formatTime = val => {
  const date = val.toString()
  const year = date.substring(0, 4)
  const month = date.substring(4, 6)
  const day = date.substring(6, 8)
  const hour = date.substring(8, 10)
  const minute = date.substring(10, 12)
  const second = date.substring(12, 14)
  return {
    t1: year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second,
    t2: hour + ':' + minute + ':' + second,
    t3: year + month + day,
    t4: hour + ':' + minute,
    t5: year + '.' + month + '.' + day + ' ' + hour + ':' + minute,
    t6: year + '-' + month + '-' + day,
  }
}

export const newDateToFormatTime = val => {
  const date = typeof val === 'undefined' ? new Date() : new Date(val)
  const year = date.getFullYear()
  const day = date.getDate() > 9 ? date.getDate() : '0' + date.getDate()
  const month = date.getMonth() + 1 > 9 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1)
  const hour = date.getHours() > 9 ? date.getHours() : '0' + date.getHours()
  const minute = date.getMinutes() > 9 ? date.getMinutes() : '0' + date.getMinutes()
  const second = date.getSeconds() > 9 ? date.getSeconds() : '0' + date.getSeconds()
  return {
    t1: year.toString() + month.toString() + day.toString() + '000000',
    t2: year.toString() + month.toString() + day.toString() + hour.toString() + minute.toString() + second.toString(),
    t3: year.toString() + '-' + month.toString() + '-' + day.toString(),
    t4: year.toString() + month.toString() + day.toString(),
  }
}

export const globalTime = () => {
  return 1500
}

// 根据飞行器类型获取飞行器类型名称
export const getAircraftTypeName = val => {
  let name = ''
  switch (val) {
    case 'UAV':
      name = '无人机'
      break
    case 'NO_UAV':
      name = '非无人机'
      break
  }
  return name
}

// 根据规则主体类型获取规则名称
export const getSubjectTypeName = val => {
  let name = ''
  switch (val) {
    case 'AIRSPACE':
      name = '空域'
      break
    case 'UA_AIRLINE':
      name = '无人机航线'
      break
    case 'OBSTACLE':
      name = '障碍物'
      break
    case 'PLACE':
      name = '障碍物'
      break
    case 'LA_AIRWAY':
      name = '航路'
      break
  }
  return name
}

// 根据规则获取规则名称
export const getFlyRestrictName = val => {
  let name = ''
  switch (val) {
    case 'PERMITTED':
      name = '适飞'
      break
    case 'CONTROLLED':
      name = '管制'
      break
    case 'FORBIDDEN':
      name = '禁飞'
      break
  }
  return name
}
export const isJSON = str => {
  if (typeof str !== 'string') return false
  try {
    const result = JSON.parse(str)
    return (typeof result === 'object' && result !== null) || Array.isArray(result)
  } catch (e) {
    return false
  }
}
