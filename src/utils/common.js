export const formatTime = val => {
  const date = val.toString()
  const year = date.substring(0, 4)
  const month = date.substring(4, 6)
  const day = date.substring(6, 8)
  const hour = date.substring(8, 10)
  const minute = date.substring(10, 12)
  const second = date.substring(12, 14)
  return {
    t1: year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second,
    t2: hour + ':' + minute + ':' + second,
    t3: year + month + day,
    t4: hour + ':' + minute,
    t5: year + '.' + month + '.' + day + ' ' + hour + ':' + minute,
    t6: year + '-' + month + '-' + day,
    t7: year + '-' + month + '-' + day + ' ' + hour + ':' + minute,
  }
}

export const newDateToFormatTime = val => {
  const date = typeof val === 'undefined' ? new Date() : new Date(val)
  const year = date.getFullYear()
  const day = date.getDate() > 9 ? date.getDate() : '0' + date.getDate()
  const month = date.getMonth() + 1 > 9 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1)
  const hour = date.getHours() > 9 ? date.getHours() : '0' + date.getHours()
  const minute = date.getMinutes() > 9 ? date.getMinutes() : '0' + date.getMinutes()
  const second = date.getSeconds() > 9 ? date.getSeconds() : '0' + date.getSeconds()
  return {
    t1: year.toString() + month.toString() + day.toString() + '000000',
    t2: year.toString() + month.toString() + day.toString() + hour.toString() + minute.toString() + second.toString(),
    t3: year.toString() + '-' + month.toString() + '-' + day.toString(),
    t4: year.toString() + month.toString() + day.toString(),
  }
}

export const globalTime = () => {
  return 1500
}

// 根据飞行器类型获取飞行器类型名称
export const getAircraftTypeName = val => {
  let name = ''
  switch (val) {
    case 'UAV':
      name = '无人机'
      break
    case 'NO_UAV':
      name = '非无人机'
      break
  }
  return name
}

// 根据规则主体类型获取规则名称
export const getSubjectTypeName = val => {
  let name = ''
  switch (val) {
    case 'AIRSPACE':
      name = '空域'
      break
    case 'UA_AIRLINE':
      name = '无人机航线'
      break
    case 'OBSTACLE':
      name = '障碍物'
      break
    case 'PLACE':
      name = '障碍物'
      break
    case 'LA_AIRWAY':
      name = '航路'
      break
  }
  return name
}

// 根据规则获取规则名称
export const getFlyRestrictName = val => {
  let name = ''
  switch (val) {
    case 'PERMITTED':
      name = '适飞'
      break
    case 'CONTROLLED':
      name = '管制'
      break
    case 'FORBIDDEN':
      name = '禁飞'
      break
  }
  return name
}

// 返回中文字典类型名称
export const getDicName = o => {
  let name = '',
    color = ''
  switch (o) {
    case 'FLIGHT_SERVICE':
      name = '飞行服务'
      color = '#3071ff'
      break
    case 'AIRSPACE':
      name = '空中交通'
      color = '#f7712f'
      break
    case 'ALL':
      name = '通用'
      color = '#7e03fd'
      break
    case 'SYSTEM':
      name = '系统管理'
      color = '#6943ff'
      break
    case 'DATA_V':
      name = '大屏'
      color = '#02eab5'
      break
  }
  return { name: name, color: color }
}

export const groupByBelongTo = arr => {
  const grouped = arr.reduce((result, item) => {
    item.belongTo.forEach(belong => {
      if (!result[belong]) {
        result[belong] = []
      }
      result[belong].push(item)
    })
    return result
  }, {})
  const c = []
  Object.keys(grouped).map((key, index) => {
    grouped[key].map(itemc => {
      if (itemc.dicId === '0QbpEYsJJkSiTJD0228cK' || itemc.dicId === 'yfzIzQSGD76Z7C6prEgqf') {
        itemc.long = true
        itemc.show = false
      } else {
        itemc.long = false
        itemc.show = true
      }
    })
    const x = getDicName(key)
    c.push({
      belongTo: x.name,
      color: x.color,
      children: grouped[key],
      index: index,
    })
  })
  return c
}

//获取日期区段
export const getDatePeriod = (period = 0) => {
  // case = 0  为今日零时到现在
  // case = 1  为昨天一天的数据
  // case = 2  为本月的数据
  // case = 3  为上个月的数据
  // case = 4  为明日一天的数据

  let time = new Date()
  const year = time.getFullYear()
  const month = String(time.getMonth() + 1).padStart(2, '0')
  const day = String(time.getDate()).padStart(2, '0')
  let preYear, preMonth, preDay
  switch (period) {
    case 0:
      return [`${year}${month}${day}`, `${year}${month}${day}`]
    case 1:
      time.setDate(time.getDate() - 1)
      preYear = time.getFullYear()
      preMonth = String(time.getMonth() + 1).padStart(2, '0')
      preDay = String(time.getDate()).padStart(2, '0')
      return [`${preYear}${preMonth}${preDay}`, `${preYear}${preMonth}${preDay}`]
    case 2:
      return [`${year}${month}01`, `${year}${month}${day}`]
    case 3:
      time.setDate(time.getMonth() - 1)
      preYear = time.getFullYear()
      preMonth = String(time.getMonth() + 1).padStart(2, '0')
      preDay = String(time.getDate()).padStart(2, '0')
      return [`${preYear}${preMonth}01`, `${year}${month}01`]
    case 4:
      time.setDate(time.getDate() + 1)
      preYear = time.getFullYear()
      preMonth = String(time.getMonth() + 1).padStart(2, '0')
      preDay = String(time.getDate()).padStart(2, '0')
      return [`${preYear}${preMonth}${preDay}`, `${preYear}${preMonth}${preDay}`]
  }
}

export const deleteSpaceKey = obj => {
  if (!obj || typeof obj !== 'object') return
  Object.keys(obj).forEach(key => {
    if (obj[key] === '') delete obj[key]
  })
  return obj
}
