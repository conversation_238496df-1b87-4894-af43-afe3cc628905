//导入css
export const getCss = url => getAsset(url, 'css')

//导入图片
export const getImg = url => getAsset(url, 'img')

//导入lottie
export const getLottie = url => getAsset(url, 'lottie')

//导入video
export const getVideo = url => getAsset(url, 'video')

//导入字体
export const getFont = url => getAsset(url, 'font')

//导入音频
export const getAudio = url => getAsset(url, 'audio')

//导入glb
export const getGlb = url => getAsset(url, 'glb')

//导入Json
export const getJson = url => getAsset(url, 'json')

//导入其他资源
export const getOther = url => getAsset(url, 'other')

//获取资源通用入口
export const getAsset = (url, type) => {
  const urlArr = url.split('/').filter(item => !!item)
  if (urlArr.length === 1) return new URL(`../assets/${type}/${url}`, import.meta.url).href
  if (urlArr.length === 2) return new URL(`../assets/${type}/${urlArr[0]}/${urlArr[1]}`, import.meta.url).href
  if (urlArr.length === 3)
    return new URL(`../assets/${type}/${urlArr[0]}/${urlArr[1]}/${urlArr[2]}`, import.meta.url).href
  if (urlArr.length === 4)
    return new URL(`../assets/${type}/${urlArr[0]}/${urlArr[1]}/${urlArr[2]}/${urlArr[3]}`, import.meta.url).href
  if (urlArr.length === 5)
    return new URL(
      `../assets/${type}/${urlArr[0]}/${urlArr[1]}/${urlArr[2]}/${urlArr[3]}/${urlArr[4]}`,
      import.meta.url,
    ).href
  const urlStr = urlArr.join('/')
  return new URL(`../assets/${type}/${urlStr}`, import.meta.url).href
}
