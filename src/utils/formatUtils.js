export const formatDate = date => {
  if (!date) return ''
  const year = date.toString().slice(0, 4)
  const month = date.toString().slice(4, 6)
  const day = date.toString().slice(6, 8)
  const hour = date.toString().slice(8, 10)
  const minute = date.toString().slice(10, 12)
  const second = date.toString().slice(12, 14)
  if (date.toString().length > 8) return `${year}-${month}-${day} ${hour}:${minute}:${second}`
  else return `${year}-${month}-${day}`
}

export const formatObjToString = obj => {
  return typeof obj === 'object' ? JSON.stringify(obj) : obj
}
