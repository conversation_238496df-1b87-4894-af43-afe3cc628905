export const hexToBase64 = hexString => {
  const byteArr = hexString.match(/.{1,2}/g).map(byte => parseInt(byte, 16))
  return btoa(String.fromCharCode(...byteArr))
}

export const isJSON = str => {
  if (typeof str !== 'string') return false
  try {
    const result = JSON.parse(str)
    return (typeof result === 'object' && result !== null) || Array.isArray(result)
  } catch (e) {
    return false
  }
}

/**
 * 状态映射配置对象
 * 用于将后端返回的状态码转换为中文显示文本
 */
export const STATUS_MAPPINGS = {
  // POI状态映射 (1: 启用, 2: 停用)
  poi_status: {
    1: { text: '启用', type: 'success' },
    2: { text: '停用', type: 'danger' },
  },

  // 运行状态映射
  poi_runningStatus: {
    RUNNING: { text: '运行中', type: 'success' },
    PLANNING: { text: '规划中', type: 'warning' },
  },
}

/**
 * 通用状态映射函数
 * @param {any} statusValue - 状态值
 * @param {string} statusType - 状态类型 (如: 'poi_status', 'runningStatus')
 * @param {string} defaultText - 默认显示文本，当找不到映射时使用
 * @param {boolean} returnObject - 是否返回完整对象 (包含 text 和 type)
 * @returns {string|object} 映射后的文本或对象
 */
export const mapStatusToText = (statusValue, statusType, defaultText = '-', returnObject = false) => {
  // 参数验证
  if (statusValue === null || statusValue === undefined) {
    return returnObject ? { text: defaultText, type: 'info' } : defaultText
  }

  // 获取对应的状态映射
  const statusMapping = STATUS_MAPPINGS[statusType]
  if (!statusMapping) {
    return returnObject ? { text: String(statusValue), type: 'info' } : String(statusValue)
  }

  // 查找状态值对应的映射
  const mappedStatus = statusMapping[statusValue]
  if (!mappedStatus) {
    return returnObject ? { text: defaultText, type: 'info' } : defaultText
  }

  return returnObject ? mappedStatus : mappedStatus.text
}

// 分组
export const groupBy = (arr, key) => {
  const grouped = Object.values(
    arr.reduce((result, item) => {
      const groupKey = item[key]
      if (!result[groupKey]) {
        result[groupKey] = []
      }
      result[groupKey].push(item)

      return result
    }, {}),
  )
  return grouped
}

export function checkBeforeDelete(row) {
  if (!row || !Array.isArray(row.ruleList) || row.ruleList.length === 0) {
    return false
  }
  return row.ruleList.some(rule => rule.defaultRule !== 1)
}
