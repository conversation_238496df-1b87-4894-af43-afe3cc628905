export const hexToBase64 = hexString => {
  const byteArr = hexString.match(/.{1,2}/g).map(byte => parseInt(byte, 16))
  return btoa(String.fromCharCode(...byteArr))
}

export const isJSON = str => {
  if (typeof str !== 'string') return false
  try {
    const result = JSON.parse(str)
    return (typeof result === 'object' && result !== null) || Array.isArray(result)
  } catch (e) {
    return false
  }
}

// 得到当前的时间字符串， 以'yyyyMMddHHmmss'为格式
export const getCurrentTime = () => {
  const date = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}${month}${day}${hours}${minutes}${seconds}`
}

export const getValidParam = cases => {
  if (!cases?.length) {
    throw new Error('cases is empty')
    return
  }
  if (cases.length >= 2) return {}
  const time = getCurrentTime()
  if (cases[0] === 0) {
    return {
      startTime: time,
      endTime: time,
    }
  }
  if (cases[0] === 1) {
    return {
      invalidStartTime: time,
      invalidEndTime: time,
    }
  }
}

/**
 * 状态映射配置对象
 * 用于将后端返回的状态码转换为中文显示文本
 */
export const STATUS_MAPPINGS = {
  // POI状态映射 (1: 启用, 2: 停用)
  poi_status: {
    1: { text: '启用', type: 'success' },
    2: { text: '停用', type: 'danger' },
  },

  // 运行状态映射
  poi_runningStatus: {
    RUNNING: { text: '运行中', type: 'success' },
    PLANNING: { text: '规划中', type: 'warning' },
  },
}

/**
 * 通用状态映射函数
 * @param {any} statusValue - 状态值
 * @param {string} statusType - 状态类型 (如: 'poi_status', 'runningStatus')
 * @param {string} defaultText - 默认显示文本，当找不到映射时使用
 * @param {boolean} returnObject - 是否返回完整对象 (包含 text 和 type)
 * @returns {string|object} 映射后的文本或对象
 */
export const mapStatusToText = (statusValue, statusType, defaultText = '-', returnObject = false) => {
  // 参数验证
  if (statusValue === null || statusValue === undefined) {
    return returnObject ? { text: defaultText, type: 'info' } : defaultText
  }

  // 获取对应的状态映射
  const statusMapping = STATUS_MAPPINGS[statusType]
  if (!statusMapping) {
    return returnObject ? { text: String(statusValue), type: 'info' } : String(statusValue)
  }

  // 查找状态值对应的映射
  const mappedStatus = statusMapping[statusValue]
  if (!mappedStatus) {
    return returnObject ? { text: defaultText, type: 'info' } : defaultText
  }

  return returnObject ? mappedStatus : mappedStatus.text
}

// 分组
export const groupBy = (arr, key) => {
  const grouped = Object.values(
    arr.reduce((result, item) => {
      const groupKey = item[key]
      if (!result[groupKey]) {
        result[groupKey] = []
      }
      result[groupKey].push(item)

      return result
    }, {}),
  )
  return grouped
}

export function checkBeforeDelete(row) {
  if (!row || !Array.isArray(row.ruleList) || row.ruleList.length === 0) {
    return false
  }
  return row.ruleList.some(rule => rule.defaultRule !== 1)
}
