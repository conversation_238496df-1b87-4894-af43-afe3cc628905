/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const reg =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * @param {string} idCard
 * @returns {Boolean}
 */
export function validIdCard(idCard) {
  const reg = /^[1-9]\d{5}(18|19|20|21|22)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|[Xx])$/
  return reg.test(idCard)
}

/**
 * @param {string} phone
 * @returns {Boolean}
 */
export function validPhone(phone) {
  const reg = /^1[3456789]\d{9}$/
  return reg.test(phone)
}

/**
 * @param {string} password
 * @returns {String}
 */
export function validPassword(password) {
  let errorMsg = ''
  if (password.length < 8) errorMsg += '密码长度不能少于8位;'
  if (!/[a-z]/.test(password) || !/[A-Z]/.test(password)) errorMsg += '密码必须包含大小写字母;'
  if (!/[0-9]/.test(password)) errorMsg += '密码必须包含数字;'
  // 包含特殊字符
  if (!/[^a-zA-Z0-9]/.test(password)) errorMsg += '密码必须包含特殊字符;'
  return errorMsg
}
