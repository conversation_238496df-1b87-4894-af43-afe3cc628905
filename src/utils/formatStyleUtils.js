// fillColor 填充色 空表示没有
// strokeColor 边线色 空表示没有
// strokeWidth 边线粗细 空或0表示没有
// strokeDasharray 虚线样式：4 2表示 4像素实线+2像素间隔
// ellipseHeight 椭圆高度
// fontSize 字体大小
// fontColor 字体颜色
const themeColor = '#f7712f'

export const formatDrawStyle = displayStyle => {
  return {
    //多边形的样式
    polygon: {
      options: {
        zIndex: 10,
      },
      style: {
        opacity: 0.3,
      },
      normal: {
        color: displayStyle.fillColor || themeColor,
      },
    },
    //文本的样式
    text: {
      options: {
        zIndex: 10,
      },
      normal: {
        color: displayStyle.fontColor || themeColor,
        size: displayStyle.fontSize,
      },
    },
    //线的样式
    line: {
      options: {
        zIndex: 10,
      },
      style: {
        opacity: 1,
      },
      normal: {
        color: displayStyle.strokeColor || themeColor,
        size: displayStyle.strokeWidth,
      },
    },
    //虚线的样式
    dashLine: {
      options: {
        zIndex: 10,
      },
      style: {
        opacity: 1,
        dashArray: displayStyle.strokeDasharray,
      },
      normal: {
        color: displayStyle.strokeColor || themeColor,
        size: displayStyle.strokeWidth,
      },
    },
    //点的样式
    // point: {
    //     options: {
    //         zIndex: 10,
    //     },
    //     style: {
    //         opacity: 1,
    //     },
    //     normal: {
    //         color: '#f7712f',
    //             size: 2,
    //     },
    // },
  }
}
