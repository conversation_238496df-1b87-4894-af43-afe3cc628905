import { createApp } from 'vue'
import '@/assets/css/reset.css'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/el-icon.css'
import router from '@/router'
import store from '@/store'
import i18n from '@/lang/index'

import App from './App.vue'

createApp(App).use(store).use(router).use(i18n).use(ElementPlus, { locale: zhCn }).mount('#app')
