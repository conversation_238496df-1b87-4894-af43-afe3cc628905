export default {
  message: {
    ruleTypeLack: '规则类型不能为空',
    userNameLack: '请输入您的账号',
    passwordLack: '请输入您的密码',
    validCodeLack: '请输入验证码',
    loginSuccess: '登录成功',
    approveSuccess: '审批成功',
    approveReject: '审批已驳回',
    whitelistNameLack: '白名单名称不能为空',
    whitelistStartTimeLack: '白名单有效期开始日期不能为空',
    whitelistEndTimeLack: '白名单有效期结束日期不能为空',
    ruleLack: '规则不能为空',
    uavLack: '请选择单位或是无人机中的一项',
    saveSuccess: '保存成功',
    lockError: '请勿重复提交',
    selectError: '请至少选中一项数据删除',
    defaultDeleteError: '默认规则不能删除',
    deleteSuccess: '删除成功',
    ruleNameLack: '规则名称不能为空',
    warningLevelLack: '规则告警等级不能为空',
    whitelistLack: '白名单不能为空',
    vaildTimeLack: '有效时间不能为空',
    bufferWidthLack: '缓冲区距离不能为空',
    bufferWidthlimit: '缓冲区距离不能超过1000米',
    whitelistActive: '当前白名单已启用',
    whiteListClose: '当前白名单已关闭',
    markNameLack: '标记名称不能为空',
    airspaceNameLack: '空域名称不能为空',
    tagsLack: '标签不能为空',
    airspaceNameLongError: '空域名称不能超过16个字',
    lineNameLongError: '航线名称不能超过16个字',
    markNameLongError: '标记名称不能超过16个字',
    memoLongError: '简介不能超过200个字',
    addressLongError: '地址不能超过64个字',
    airspaceRuleLack: '飞行规则不能为空',
    airspaceSourceLack: '空域来源不能为空',
    lineNameLack: '航线名称不能为空',
    lineTypeLack: '航线类型不能为空',
    lineKindLack: '航线性质不能为空',
    lineHeight: '巡航高度不能为空',
    lineWidth: '航线宽度不能为空',
    levelHeightLack: '高度层不能为空',
    flyRestrictLack: '飞行限制不能为空',
    ruleIdLack: '基准规则不能为空',
    modelLack: '地图模型不能为空',
    abnormalWidthLack: '异常区不能为空',
    exportInSuccess: '导入成功',
    exportInFalse: '导入失败',
    exportInObjectFalse: '当前不支持这些类型导入',
    itemIdNone: '编号不能为空',
    itemValueNone: '名称不能为空',
  },
}
