import { defineStore } from 'pinia'

// 用户信息存储
const useUser = defineStore('user', {
  state: () => {
    return {
      userInfo: {},
      username: '',
      password: '',
      userId: '',
    }
  },
  actions: {
    setUserInfo(info) {
      this.userInfo = info
      this.userId = info.userId ?? ''
    },
    setUsername(name) {
      this.username = name
    },
    setPassword(pwd) {
      this.password = pwd
    },
    getPassword() {
      return this.password
    },
    clearUserInfo() {
      this.userInfo = {}
    },
    getAccessId() {
      return this.userInfo?.accessId ?? ''
    },
    getUserInfo() {
      return this.userInfo
    },
    setUserInfoName(name) {
      this.userInfo.userName = name
    },
  },

  // 持久化配置
  persist: {
    // 存储的键名
    key: 'air-space-user',
    // 需要持久化的数据
    pick: ['userInfo', 'username', 'password'],
    // 存储方式，默认是localStorage
    storage: localStorage,
  },
})

export default useUser
