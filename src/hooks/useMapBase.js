import { ref, render } from 'vue'
import { Mapbox } from '@antv/l7-maps'
import { useResizeObserver } from '@vueuse/core'
import * as turf from '@turf/turf'
import MapboxStyle from '@/data/MapboxStyle'
import { Scene } from '@antv/l7'
import { BaseOptions, DrawType } from '@/data/MapConstant.js'
import { CircleDrawer, DrawEvent, DrawRect, LineDrawer, PointDrawer, PolygonDrawer } from '@antv/l7-draw'
import Menu from '@/components/common/map/menu/Menu.vue'
import { getMapType } from '@/utils/mapHelper.js'
import { ElMessageBox } from 'element-plus'

const mountId = ref()
const mapContainer = ref()
const scene = ref()
const map = ref()
const allDrawers = reactive({})
const drawers = computed(() => {
  if (!type.value) return allDrawers
  const menus = getMapType(type.value)
  const nowDrawers = {}
  menus.forEach(menu => (nowDrawers[menu] = allDrawers[menu]))
  return nowDrawers
})

const type = ref()
const currentMenu = ref()
watch(currentMenu, (value, oldValue) => {
  drawers.value[oldValue]?.clear()
  drawerChangeCb?.(oldValue)
  drawers.value[oldValue]?.disable()
  drawers.value[value]?.enable()
})

let drawerChangeCb = null
let resizeTimeout

const clear = () => {
  type.value = null
  currentMenu.value = null
  drawerChangeCb = null
  scene.value && scene.value.destroy()
  allDrawers[DrawType.Point] && allDrawers[DrawType.Point].destroy()
  allDrawers[DrawType.Line] && allDrawers[DrawType.Line].destroy()
  allDrawers[DrawType.Polygon] && allDrawers[DrawType.Polygon].destroy()
  allDrawers[DrawType.Circle] && allDrawers[DrawType.Circle].destroy()
  allDrawers[DrawType.Rectangle] && allDrawers[DrawType.Rectangle].destroy()
  allDrawers[DrawType.Point] = null
  allDrawers[DrawType.Line] = null
  allDrawers[DrawType.Polygon] = null
  allDrawers[DrawType.Circle] = null
  allDrawers[DrawType.Rectangle] = null
  scene.value = null
  map.value = null
  delete allDrawers[DrawType.Point]
  delete allDrawers[DrawType.Line]
  delete allDrawers[DrawType.Polygon]
  delete allDrawers[DrawType.Circle]
  delete allDrawers[DrawType.Rectangle]
  mountId.value = null
  mapContainer.value = null
  clearTimeout(resizeTimeout)
}

watch(mountId, value => {
  if (!value) return
  mapContainer.value = document.getElementById(value)
})
useResizeObserver(mapContainer, entries => {
  if (!mapContainer.value) return
  clearTimeout(resizeTimeout)
  resizeTimeout = setTimeout(() => {
    scene.value?.map.resize()
  }, 50)
})

const initDrawers = () => {
  const allMenus = [DrawType.Point, DrawType.Line, DrawType.Polygon, DrawType.Circle, DrawType.Rectangle]
  const generateDrawer = menu => {
    switch (menu) {
      case DrawType.Polygon:
        const { ...PolygonOptions } = BaseOptions
        const polygonDrawer = new PolygonDrawer(scene.value, PolygonOptions)
        polygonDrawer.on(DrawEvent.Change, list => synchronization(menu, list))
        return polygonDrawer
      case DrawType.Point:
        const { distanceOptions, areaOptions: areaOptionsPoint, ...PointOptions } = BaseOptions
        const pointDrawer = new PointDrawer(scene.value, PointOptions)
        pointDrawer.on(DrawEvent.Change, list => synchronization(menu, list))
        return pointDrawer
      case DrawType.Line:
        const { areaOptions: areaOptionsLine, ...LineOptions } = BaseOptions
        const lineDrawer = new LineDrawer(scene.value, LineOptions)
        lineDrawer.on(DrawEvent.Change, list => synchronization(menu, list))
        return lineDrawer
      case DrawType.Circle:
        const { ...CircleOptions } = BaseOptions
        const circleDrawer = new CircleDrawer(scene.value, CircleOptions)
        circleDrawer.on(DrawEvent.Change, list => synchronization(menu, list))
        return circleDrawer
      case DrawType.Rectangle:
        const { ...RectOptions } = BaseOptions
        const rectDrawer = new DrawRect(scene.value, RectOptions)
        rectDrawer.on(DrawEvent.Change, list => synchronization(menu, list))
        return rectDrawer
      default:
        return null
    }
  }
  allMenus.forEach(menu => (allDrawers[menu] = generateDrawer(menu)))
}

const synchronization = (menu, list) => {
  if (!list?.length) {
    drawerChangeCb?.(menu)
    return
  }
  switch (menu) {
    case DrawType.Point:
      const coordinate = list[0]?.geometry?.coordinates
      drawerChangeCb?.(menu, [{ type: menu.toUpperCase(), coordinate }], list)
      break
    case DrawType.Line:
      const coordinateList = list[0]?.geometry?.coordinates
      drawerChangeCb?.(menu, [{ type: menu.toUpperCase(), coordinateList }], list)
      break
    case DrawType.Circle:
      const centerCoordinate = list[0]?.properties?.nodes?.[0].geometry?.coordinates ?? []
      const radius = turf.distance(
        turf.point(centerCoordinate),
        turf.point(list[0]?.properties?.nodes?.[1].geometry?.coordinates),
        { units: 'meters' },
      )
      drawerChangeCb?.(menu, [{ type: menu.toUpperCase(), centerCoordinate, radius }], list)
      break
    case DrawType.Rectangle:
      const nodeList = list[0]?.properties?.nodes?.map(i => i.geometry?.coordinates) ?? [
        [0, 0],
        [0, 0],
      ]
      const southWestCoordinate = [Math.min(nodeList[0][0], nodeList[1][0]), Math.min(nodeList[0][1], nodeList[1][1])]
      const northEastCoordinate = [Math.max(nodeList[0][0], nodeList[1][0]), Math.max(nodeList[0][1], nodeList[1][1])]
      drawerChangeCb?.(menu, [{ type: menu.toUpperCase(), northEastCoordinate, southWestCoordinate }], list)
      break
    case DrawType.Polygon:
      const shellCoordinateList = list[0]?.geometry?.coordinates?.[0] ?? []
      drawerChangeCb?.(menu, [{ type: menu.toUpperCase(), shellCoordinateList }], list)
    default:
  }
}
const setType = (typeValue, callback = null) => {
  type.value = typeValue
  if (typeof callback === 'function') drawerChangeCb = callback
  if (typeValue) setTimeout(() => (currentMenu.value = getMapType(typeValue)[0]), 100)
}

const setData = geosArr => {
  if (!geosArr.length) return
  setTimeout(() => {
    Object.keys(allDrawers).forEach(key => allDrawers[key]?.setData([]))
    geosArr.forEach(item => {
      switch (item.type) {
        case DrawType.Point.toUpperCase():
          if (item.coordinate?.length === 2) {
            allDrawers[DrawType.Point]?.setData([
              ...(allDrawers[DrawType.Point]?.getData() ?? []),
              { type: 'Feature', geometry: { coordinates: item.coordinate, type: 'Point' }, properties: {} },
            ])
          }
          break
        case DrawType.Line.toUpperCase():
          if (item.coordinateList?.length) {
            allDrawers[DrawType.Line]?.setData([
              ...(allDrawers[DrawType.Line]?.getData() ?? []),
              { type: 'Feature', geometry: { coordinates: item.coordinateList, type: 'LineString' }, properties: {} },
            ])
          }
          break
        case DrawType.Circle.toUpperCase():
          if (item.centerCoordinate?.length === 2 && typeof item.radius === 'number' && item.radius > 0) {
            const center = item.centerCoordinate
            const radius = item.radius
            const circle = turf.circle(center, radius, { steps: 64, units: 'meters' })
            allDrawers[DrawType.Circle]?.setData([...(allDrawers[DrawType.Circle]?.getData() ?? []), circle])
          }
          break
        case DrawType.Rectangle.toUpperCase():
          if (item.northEastCoordinate?.length === 2 && item.southWestCoordinate?.length === 2) {
            const bbox = [...item.southWestCoordinate, ...item.northEastCoordinate]
            const rect = turf.bboxPolygon(bbox)
            allDrawers[DrawType.Rectangle]?.setData([...(allDrawers[DrawType.Rectangle]?.getData() ?? []), rect])
          }
          break
        case DrawType.Polygon.toUpperCase():
          if (item.shellCoordinateList?.length) {
            allDrawers[DrawType.Polygon]?.setData([
              ...(allDrawers[DrawType.Polygon]?.getData() ?? []),
              {
                type: 'Feature',
                geometry: { coordinates: [item.shellCoordinateList], type: 'Polygon' },
                properties: {},
              },
            ])
          }
        default:
          break
      }
    })
    const features = turf.featureCollection([
      ...(allDrawers[DrawType.Point]?.getData() ?? []),
      ...(allDrawers[DrawType.Line]?.getData() ?? []),
      ...(allDrawers[DrawType.Circle]?.getData() ?? []),
      ...(allDrawers[DrawType.Rectangle]?.getData() ?? []),
      ...(allDrawers[DrawType.Polygon]?.getData() ?? []),
    ])
    if (features.features.length) {
      const bounds = turf.bbox(features)
      scene.value.map.fitBounds(bounds, {
        padding: 200,
        duration: 4000,
      })
    }
  }, 300)
}

const setStyle = style => {
  if (!Object.keys(style).length) return
  setTimeout(() => {
    if (!allDrawers[DrawType.Point]) return
    const pointStyle = allDrawers[DrawType.Point]?.options?.style?.point
    pointStyle.active.color = style?.color || '#ED9D48'
    pointStyle.hover.color = style?.color + 'A0' || '#1990FF'
    pointStyle.normal.color = style?.color + 'A0' || '#1990FF'
    pointStyle.active.size = style?.size || 8
    pointStyle.hover.size = style?.size || 8
    pointStyle.normal.size = Math.max(1, style?.size - 2) || 6

    if (!allDrawers[DrawType.Line]) return
    const lineStyle = allDrawers[DrawType.Line]?.options?.style?.line
    lineStyle.active.color = style?.color || '#ED9D48'
    lineStyle.hover.color = style?.color + 'A0' || '#1990FF'
    lineStyle.normal.color = style?.color + 'A0' || '#1990FF'
    lineStyle.active.size = style?.size || 2
    lineStyle.hover.size = style?.size || 2
    lineStyle.normal.size = style?.size || 2

    if (!allDrawers[DrawType.Polygon]) return
    const polygonStyle = allDrawers[DrawType.Polygon]?.options?.style?.polygon
    const polygonLineStyle = allDrawers[DrawType.Polygon]?.options?.style?.line
    polygonLineStyle.active.color = style?.color || '#ED9D48'
    polygonLineStyle.hover.color = style?.color + 'A0' || '#1990FF'
    polygonLineStyle.normal.color = style?.color + 'A0' || '#1990FF'
    polygonStyle.active.color = style?.color || '#ED9D48'
    polygonStyle.hover.color = style?.color + 'A0' || '#1990FF'
    polygonStyle.normal.color = style?.color + 'A0' || '#1990FF'

    if (!allDrawers[DrawType.Circle]) return
    const circleStyle = allDrawers[DrawType.Circle]?.options?.style?.polygon
    const circleLineStyle = allDrawers[DrawType.Circle]?.options?.style?.line
    circleLineStyle.active.color = style?.color || '#ED9D48'
    circleLineStyle.hover.color = style?.color + 'A0' || '#1990FF'
    circleLineStyle.normal.color = style?.color + 'A0' || '#1990FF'
    circleStyle.active.color = style?.color || '#ED9D48'
    circleStyle.hover.color = style?.color + 'A0' || '#1990FF'
    circleStyle.normal.color = style?.color + 'A0' || '#1990FF'

    if (!allDrawers[DrawType.Rectangle]) return
    const rectStyle = allDrawers[DrawType.Rectangle]?.options?.style?.polygon
    const rectLineStyle = allDrawers[DrawType.Rectangle]?.options?.style?.line
    rectLineStyle.active.color = style?.color || '#ED9D48'
    rectLineStyle.hover.color = style?.color + 'A0' || '#1990FF'
    rectLineStyle.normal.color = style?.color + 'A0' || '#1990FF'
    rectStyle.active.color = style?.color || '#ED9D48'
    rectStyle.hover.color = style?.color + 'A0' || '#1990FF'
    rectStyle.normal.color = style?.color + 'A0' || '#1990FF'
  }, 200)
}
const changeMenu = menu => {
  if (currentMenu.value === menu) return
  const doChange = () => {
    drawers.value[currentMenu.value]?.setData([])
    drawerChangeCb?.(currentMenu.value)
    currentMenu.value = menu
  }
  if (drawers.value[currentMenu.value]?.getData()?.length) {
    ElMessageBox.confirm('更换绘制方式，将清除当前绘制的数据', '更换提示')
      .then(doChange)
      .catch(() => {})
  } else currentMenu.value = menu
}
const onRemove = () => {
  if (drawers.value[currentMenu.value]?.getData()?.length) {
    ElMessageBox.confirm('确认清除当前绘制数据?', '删除确认')
      .then(() => {
        drawers.value[currentMenu.value]?.setData([])
        drawerChangeCb?.(currentMenu.value)
        drawers.value[currentMenu.value]?.enable()
      })
      .catch(() => {})
  }
}

watch(mapContainer, value => {
  if (!value) return
  map.value = new Mapbox({
    pitch: 0,
    style: MapboxStyle,
    center: [120.6074545, 30.0148306],
    maxZoom: 16,
    minZoom: 8,
    maxBounds: [
      [103.999178, 18.562035],
      [137.215731, 41.448997],
    ],
    zoom: 9.5,
    antialias: false,
    dragRotate: false,
    touchRotate: false,
  })
  const sceneInstance = new Scene({
    id: mountId.value,
    map: map.value,
    logoVisible: false,
    renderer: 'regl',
  })
  sceneInstance.on('loaded', () => {
    scene.value = sceneInstance
    render(h(Menu, { type: type.value }), mapContainer.value)
    initDrawers()
  })
})

const useMapBase = mapId => {
  if (!mapId) return { scene, map, allDrawers, setType, setData, setStyle, drawers, currentMenu, changeMenu, onRemove }
  clear()
  nextTick(() => (mountId.value = mapId)).then()
  return { scene, map, allDrawers, setType, setData, setStyle, drawers, currentMenu, changeMenu, onRemove }
}

export default useMapBase
