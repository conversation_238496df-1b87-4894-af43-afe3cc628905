import { defineAsyncComponent, reactive } from 'vue'
import MenusData from '@/data/MenusData'
import { useStorage } from '@vueuse/core'
import { ElMessageBox } from 'element-plus'

export const PathType = {
  normal: 'normal',
  add: 'add',
  edit: 'edit',
  detail: 'detail',
  inhibit: 'inhibit', // 禁止转跳，如考试中，考完转跳用 successBack
  successBack: 'success-back', // 正常回退， 指新增，编辑数据保存成功后回退， 或者禁止转跳页面的前置转跳
}
const BlockCommonTip = '跳转其他页面, 当前数据将会丢失, 是否继续?'
const BlockInhibitTip = '当前页面禁止转跳, 请完成当前页面内容!'

// 组件对应Key的映射表
const UrlToKey = {}
// 根路径
const RootPath = { name: '平台' }

const pathsData = useStorage(
  'air-space-router',
  {
    path: [RootPath, { name: MenusData[0].name, url: MenusData[0].url }],
    param: {},
  },
  sessionStorage,
)

const modules = import.meta.glob('../components/**/*.vue')
const asyncComponents = shallowRef(
  Object.keys(modules).map(key => {
    const url = key.replace('../components/', '')
    return {
      url,
      component: defineAsyncComponent({
        loader: () =>
          modules[key]().then(module => {
            module.default.name = url
            return markRaw(module)
          }),
      }),
    }
  }),
)

const currentComponent = computed(
  () =>
    asyncComponents.value.find(item => item.url === pathsData.value.path.at(-1).url)?.component ||
    defineAsyncComponent(() => import('../components/common/Empty.vue')),
)
const currentMenuActive = computed(() => {
  const filterPath = pathsData.value.path.filter(item => item.nodeIndex || item.nodeIndex === 0)
  if (filterPath.length) return filterPath.map(item => item.nodeIndex).join('-')
  return null
})

const pathData = computed(() => pathsData.value.path.at(-1))

const refreshKeyCount = ref(0)
const freshOption = reactive({ componentKey: '', forceRefresh: false })

const useComponent = () => {
  const findPath = (arr, url, data) => {
    for (let i = 0; i < arr.length; i++) {
      data.push({ name: arr[i].name, url: arr[i]?.url, isHidden: arr[i].isHidden, nodeIndex: i })
      if (arr[i]?.url?.toUpperCase() === url.toUpperCase()) return data
      if (arr[i].nodeList?.length) {
        const result = findPath(arr[i].nodeList, url, data)
        if (result) return result
      }
      data.pop()
    }
    return null
  }
  const setPath = (url, param = null, options = { type: PathType.normal }) => {
    if (!url) return
    const doPath = () => {
      let data = findPath(MenusData, url, [])
      data?.unshift(RootPath)
      if (!data && asyncComponents.value.find(item => item.url === url)) {
        data = []
        data.push(...pathsData.value.path, { url, name: pathsData.value.path.at(-1)?.name ?? '未知标题' })
      }
      if (!data || !data.length) return

      freshOption.forceRefresh = data?.[1]?.url !== pathsData.value.path?.[1]?.url
      // 是否存在这个组件，如果是Empty.vue, 需要Count++
      const isExist = !!asyncComponents.value.find(item => item.url === pathsData.value.path.at(-1).url)
      freshOption.componentKey =
        options.type === PathType.normal && isExist
          ? (UrlToKey[url] ?? data?.[data?.length - 1]?.url)
          : data[data.length - 1].url + refreshKeyCount.value++
      UrlToKey[url] = freshOption.componentKey

      pathsData.value.param = {
        from: data?.[data?.length - 2]?.url || '',
        to: data?.[data?.length - 1]?.url,
        data: param,
      }

      const lastItem = data?.[data?.length - 1]
      lastItem.options = options
      if (options.name) {
        lastItem.name = options.name
      } else {
        if (options.type === PathType.add) lastItem.name = '新增' + lastItem.name
        if (options.type === PathType.edit) lastItem.name = '修改' + lastItem.name
        if (options.type === PathType.detail) lastItem.name = lastItem.name + '详情'
      }
      pathsData.value.path = data
    }
    if (options.type === PathType.successBack) {
      doPath()
    } else if ([PathType.add, PathType.edit].includes(pathData.value.options?.type)) {
      const msg = pathData.value.options?.msg ?? BlockCommonTip
      const title = pathData.value.options?.title ?? '提示'
      ElMessageBox.confirm(msg, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(doPath)
        .catch(() => {})
    } else if ([PathType.inhibit].includes(pathData.value.options?.type)) {
      const msg = pathData.value.options?.msg ?? BlockInhibitTip
      const title = pathData.value.options?.title ?? '提示'
      ElMessageBox.alert(msg, title, {
        confirmButtonText: '确定',
      }).then(() => {})
    } else doPath()
  }
  const setPathBack = (level = 1) => {
    const counts = pathsData.value.path.filter(i => i.url).length
    const maxLevel = pathsData.value.path.at(-1)?.url ? counts - 1 : counts
    let realLevel = level
    if (level > maxLevel || level <= 0) realLevel = maxLevel
    const backUrl = pathsData.value.path.at(-(realLevel + 1))?.url
    if (backUrl) setPath(backUrl)
  }
  const getParam = () => {
    if (pathsData.value.param.to !== pathsData.value.path.at(-1)?.url) return null
    return pathsData.value.param
  }

  return { setPath, setPathBack, pathsData, pathData, freshOption, getParam, currentComponent, currentMenuActive }
}

export default useComponent
