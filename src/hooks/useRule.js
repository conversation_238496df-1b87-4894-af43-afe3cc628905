import { reactive } from 'vue'
import { rule_list } from '@/request/api/index'

export const useRule = () => {
  const ruleList = reactive([])
  const params = {
    limit: 999,
    offset: 0,
    deleted: 0,
    status: 1,
  }
  rule_list(params).then(res => {
    if (res && res.list && res.list.length > 0) {
      const items = res.list.map(item => ({
        label: `${item.ruleName}(${item.ruleType === 'STATIC' ? '静态' : '动态'})`,
        value: item.id,
      }))
      ruleList.splice(0, ruleList.length, ...items)
    }
  })
  return {
    ruleList,
  }
}
