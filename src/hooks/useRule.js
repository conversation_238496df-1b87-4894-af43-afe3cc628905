import { reactive } from 'vue'
import { rule_aggregate_list } from '@/request/api/index'

export const RuleType = {
  适飞空域: '-3qdLv7XOQPkBFJmQLi6z',
  管制空域: 'v-SCeN1sNFDFFRIm9ZmNo',
  禁飞空域: 'H6fsNhNikaD-Af1hCI_sv',
  低空航路默认: 'yGGVau1DBP_IApKQVHgLB',
  低空航线默认: 'kN1fY8gUs-FHlOQNeXucE',
  低空障碍物默认: 'CGIZQ-39aGtCFf8Eu8mtf',
  超高120米: 'SQ1x-x04eMNVehv8ct6jr',
  超高300米: 'vTVMLPVSgFSz_kZH8PNSd',
}

export const useRule = () => {
  const ruleList = reactive([])
  const params = {
    limit: 999,
    offset: 0,
    deleted: 0,
    status: 1,
  }
  rule_aggregate_list(params).then(res => {
    if (res && res.list && res.list.length > 0) {
      const items = res.list
        .sort((a, b) => {
          const indexA = Object.values(RuleType).indexOf(a.id)
          const indexB = Object.values(RuleType).indexOf(b.id)
          const defaultIndexA = a.ruleType === 'STATIC' ? 998 : 999
          const defaultIndexB = b.ruleType === 'STATIC' ? 998 : 999
          const realA = indexA === -1 ? defaultIndexA : indexA
          const realB = indexB === -1 ? defaultIndexB : indexB
          return realA - realB
        })
        .map(item => ({
          label: `${item.ruleName} (${item.ruleType === 'STATIC' ? '静态' : '动态'})`,
          value: item.id,
        }))
      ruleList.splice(0, ruleList.length, ...items)
    }
  })
  return {
    ruleList,
  }
}
