import { ref, reactive } from 'vue'
import { dic_list } from '@/request/api/index'

// 缓存字典数据，避免重复请求
const dictionaryCache = new Map()

/**
 * @description 获取字典数据
 * @param {String} id 字典id
 * @returns {Array} 字典数据数组
 */
export function useDictionary(id) {
  if (!id) {
    return reactive([])
  }

  // 如果已经缓存过，直接返回
  if (dictionaryCache.has(id)) {
    return dictionaryCache.get(id)
  }

  const dictionaryData = reactive([])

  // 缓存
  dictionaryCache.set(id, dictionaryData)

  // 获取字典数据
  dic_list({ dicId: id }).then(res => {
    if (res && res.list && res.list.length > 0 && res.list[0].itemList) {
      const items = res.list[0].itemList.map(item => ({
        label: item.itemValue,
        value: item.itemId,
      }))

      dictionaryData.splice(0, dictionaryData.length, ...items)
    }
  })

  return dictionaryData
}
