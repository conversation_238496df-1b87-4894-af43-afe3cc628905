import { ref, reactive } from 'vue'
import { dic_list } from '@/request/api/index'

// 缓存字典数据，避免重复请求
//const dictionaryCache = new Map()

// 中文名称到字典ID的映射关系
const chineseNameToIdMapping = {
  // 障碍物相关
  障碍物类型: 'f1lep9eXuuwQDv4eMCRS6',
  POI类型: 'H4vW9Iv7l5YEafkCDaeFi',

  // 无人机操控员相关
  无人机操控员执照种类: 'a8VWBHcr7sLBSivDZPP6S',
  无人机操控员级别等级: 'nHw3xvd1uK2uyoLScVWgc',
  无人机操控员超视距等级: 'GegoURoj3h96YyznYobgv',
  无人机操控员教员等级: 'QTqgyIoC5gfrM1AsPRsiT',
  无人机操控员类别等级: 'BCtGOJH2pDFsmuquxJrSF',

  无人机产品类型: 'jN6oZrEbpBDhiFB7s8meZ',

  // 航线航路相关
  航线标签: 'CGNsVgaSAAruUWUvx9Wv4',
  航线种类: 'CToT8I9kqsni0Pv2d9z44',

  // 地点相关
  地点地点类型: 'kJzmm7u8c3d7lAu9WnImz',

  // 证件国家
  证件国家: 'QQbpEYsJJkSiTJD0228cK',

  // 飞行计划相关
  飞行计划任务性质: '5sHjYaJlS0MsDl5aGp0Xf',

  // 民族
  民族: 'yfzIzQSGD76Z7C6prEgqf',

  // 标签相关
  POI标签: 'waHjyEuf_sEt5xxvu9xgq',
  空域标签: 'GCwu_MvDFMFT1KcqZa79O',
  无人机操控员标签: 'j5ZqCRKdx7BnGWpI3ukd8',
  无人机标签: 'jSoc65nmk8CnKVkK9X9wt',
  障碍物标签: '9hoid7keUt3zCiEreUt7o',
  航路标签: 'k3nTzweAqXLoytNvAib7e',

  // 所属地区
  所属地区: 'Vhl4KW9R5LcpE2CEvhQuR',

  // 规则
  规则: '6Kx0Y55Z50FDBJT5WQLeX',
}

/**
 * @description 获取字典数据
 * @param {String} idOrChineseName 字典id或中文名称
 * @returns {Array} 字典数据数组
 */
export function useDictionary(idOrChineseName) {
  if (!idOrChineseName) {
    return reactive([])
  }

  // 如果传入的是中文名称，映射到对应的ID
  let actualId = idOrChineseName
  if (chineseNameToIdMapping[idOrChineseName]) {
    actualId = chineseNameToIdMapping[idOrChineseName]
  }

  // 如果已经缓存过，直接返回
  // if (dictionaryCache.has(actualId)) {
  //   return dictionaryCache.get(actualId)
  // }

  const dictionaryData = reactive([])

  // 缓存
  //dictionaryCache.set(actualId, dictionaryData)

  // 获取字典数据
  dic_list({ dicId: actualId, deleted: 0 }).then(res => {
    if (res && res.list && res.list.length > 0 && res.list[0].itemList) {
      const items = res.list[0].itemList.map(item => ({
        label: item.itemValue,
        value: item.itemId,
      }))

      dictionaryData.splice(0, dictionaryData.length, ...items)
    }
  })

  return dictionaryData
}
