import useMapBase from '@/hooks/useMapBase.js'
import { watch } from 'vue'
import useShowData from '@/hooks/useShowData.js'

const useShowMap = (mapId, mapData) => {
  const { setData, setStyle, setExtra } = useMapBase(mapId)
  const { currentId } = useShowData()
  watch(
    () => JSON.stringify(mapData?.value),
    (value, oldValue) => {
      if (value === undefined) return
      const newVal = JSON.parse(value)
      currentId.value = newVal?.id
      const oldVal = JSON.parse(oldValue || '{}')
      if (JSON.stringify(newVal.geometries) !== JSON.stringify(oldVal.geometries)) setData?.(newVal?.geometries ?? [])
      if (JSON.stringify(newVal.displayStyle) !== JSON.stringify(oldVal.displayStyle)) setStyle?.(newVal.displayStyle)
      if (
        JSON.stringify(newVal.extra) !== JSON.stringify(oldVal.extra) ||
        JSON.stringify(newVal.geometries) !== JSON.stringify(oldVal.geometries) ||
        JSON.stringify(newVal.displayStyle) !== JSON.stringify(oldVal.displayStyle)
      )
        setExtra?.(newVal?.geometries ?? [], newVal.extra, newVal.displayStyle)
    },
    { immediate: true },
  )
}

export default useShowMap
