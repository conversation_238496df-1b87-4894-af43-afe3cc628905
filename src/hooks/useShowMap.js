import useMapBase from '@/hooks/useMapBase.js'
import { watch } from 'vue'

const useShowMap = (mapId, mapData) => {
  const { setData } = useMapBase(mapId)
  watch(
    () => JSON.stringify(mapData?.value),
    (value, oldValue) => {
      const newVal = JSON.parse(value)
      const oldVal = JSON.parse(oldValue)
      if (JSON.stringify(newVal.geometries) !== JSON.stringify(oldVal.geometries)) setData?.(newVal.geometries)
    },
  )
}

export default useShowMap
