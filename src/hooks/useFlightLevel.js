import { flight_level_list } from '@/request/api/index.js'

const flightLevel = reactive([])
const init = ref(false)

const useFlightLevel = () => {
  if (init.value) return flightLevel
  init.value = true
  flight_level_list({ offset: 0, limit: 10 }).then(res => {
    flightLevel.splice(
      0,
      flightLevel.length,
      ...res.list.map((level, index) => ({
        id: level.id,
        label: level.flightLevelName,
        bottom: level.bot,
        top: level.top,
        key: level.flightLevelNo,
        index: index,
        dataLoaded: false,
        airspaces: [],
      })),
    )
  })
  return flightLevel
}

export default useFlightLevel
