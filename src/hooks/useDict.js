import { ref } from 'vue'
import useRequestQueue from '@/hooks/useRequestQueue.js'
import { list } from '@/request/api/dict.js'

const dictList = reactive([])
const tagList = reactive([])

const init = ref(false)

const fetchData = () => {
  list({}).then(res => {
    dictList.splice(0, dictList.length)
    tagList.splice(0, tagList.length)
    res.list.forEach(item => {
      let data = { id: item.dicId, options: [] }
      item.itemList.forEach(r => {
        data.options.push({
          jsonStrValue: JSON.stringify({ id: r.itemId, value: r.itemValue }),
          jsonValue: { id: r.itemId, value: r.itemValue },
          label: r.itemValue,
          value: r.itemId,
        })
      })
      if (item.usage === 'CATEGORY') dictList.push(data)
      else tagList.push(data)
    })
  })
}
const { triggerFetch } = useRequestQueue(fetchData, init)

const useDict = () => {
  if (init.value) return { dictList, tagList }
  triggerFetch?.()
  return { dictList, tagList }
}
export default useDict
