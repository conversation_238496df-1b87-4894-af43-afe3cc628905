import { ref, computed } from 'vue'

// 请求队列存储
const queues = new Map()
const useRequestQueue = (fetchFn, init) => {
  const queueKey = fetchFn
  // 初始化队列
  if (!queues.has(queueKey)) {
    queues.set(queueKey, [])
  }
  // 获取当前请求队列
  const queue = queues.get(queueKey)
  const isLoading = ref(false)
  async function triggerFetch(...args) {
    if (init.value) return
    const task = async () => {
      if (init.value) return
      isLoading.value = true
      try {
        await fetchFn(...args)
        init.value = true
        queue.splice(0, queue.length)
      } catch (err) {
        console.log('triggerFetch', err)
      } finally {
        // 请求完成后移除当前任务并执行下一个
        queue.shift()
        isLoading.value = queue.length > 0
        if (queue.length > 0) {
          queue[0]()
        }
      }
    }
    queue.push(task)
    if (queue.length === 1) {
      isLoading.value = true
      return task()
    }
  }

  return {
    triggerFetch,
    isLoading: computed(() => isLoading.value),
  }
}

export default useRequestQueue
