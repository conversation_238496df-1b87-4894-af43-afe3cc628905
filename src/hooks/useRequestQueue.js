import { ref } from 'vue'

const useRequestQueue = (fetchFn, init) => {
  const isLoading = ref(false)

  const triggerFetch = async (...args) => {
    if (init.value) return
    isLoading.value = true
    try {
      await fetchFn(...args)
      init.value = true
    } catch (err) {
      throw err
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    triggerFetch,
  }
}
export default useRequestQueue
