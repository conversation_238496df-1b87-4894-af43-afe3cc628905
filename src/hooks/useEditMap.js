import useMapBase from '@/hooks/useMapBase.js'
import { watch } from 'vue'
import { ModeType } from '@/data/MapConstant.js'
import useShowData from '@/hooks/useShowData.js'

const useEditMap = (mapId, mapData, type) => {
  const { setData, setStyle, setType, setExtra } = useMapBase(mapId, ModeType.ADD)
  const { currentId } = useShowData()
  setType?.(type, (menu, data, geoJson) => {
    mapData.value.geometries = data || []
    mapData.value.geoJson = geoJson || []
    mapData.value.drawerCount = mapData.value.drawerCount ? mapData.value.drawerCount + 1 : 1
  })

  watch(
    () => JSON.stringify(mapData?.value),
    (value, oldValue) => {
      const newVal = JSON.parse(value)
      currentId.value = newVal?.id
      const oldVal = JSON.parse(oldValue)
      if (newVal.drawerCount !== oldVal.drawerCount) return
      if (JSON.stringify(newVal.geometries) !== JSON.stringify(oldVal.geometries)) setData?.(newVal.geometries)
      if (JSON.stringify(newVal.displayStyle) !== JSON.stringify(oldVal.displayStyle)) setStyle?.(newVal.displayStyle)
      if (
        JSON.stringify(newVal.extra) !== JSON.stringify(oldVal.extra) ||
        JSON.stringify(newVal.geometries) !== JSON.stringify(oldVal.geometries) ||
        JSON.stringify(newVal.displayStyle) !== JSON.stringify(oldVal.displayStyle)
      )
        setExtra?.(newVal?.geometries ?? [], newVal.extra, newVal.displayStyle)
    },
  )
}

export default useEditMap
