import useFlightLevel from '@/hooks/useFlightLevel.js'

const flightLevel = useFlightLevel()
const showMenu = reactive({
  airspace: {
    name: '空域',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'], values: ['RUNNING', 'PLANNING'] },
    airspaceCategory: {
      label: '种类',
      case: [0, 1, 2],
      options: ['适飞区域', '管制区域', '禁飞区域'],
      values: ['SUITABLE', 'CONTROLLED', 'NO_FLY'],
    },
    flightLevel: { label: '高度层', case: [], options: [], values: [] },
  },
  airway: {
    name: '航路',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'], values: ['RUNNING', 'PLANNING'] },
  },
  airline: {
    name: '航线',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'], values: ['RUNNING', 'PLANNING'] },
  },
  obstacle: {
    name: '障碍物',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'], values: ['RUNNING', 'PLANNING'] },
    obstacleKind: {
      label: '种类',
      case: [0, 1, 2],
      options: ['地点', '路线', '区域'],
      values: ['POINT', 'LINE', 'PLANE'],
    },
  },
  poi: {
    name: 'POI',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'], values: ['RUNNING', 'PLANNING'] },
  },
})

watch(flightLevel, () => {
  showMenu.airspace.flightLevel.options = flightLevel.map(i => i.label)
  showMenu.airspace.flightLevel.values = flightLevel.map(i => i.id)
  showMenu.airspace.flightLevel.case = Array.from({ length: flightLevel.length }, (_, i) => i)
})

const useShowMenu = () => showMenu
export default useShowMenu
