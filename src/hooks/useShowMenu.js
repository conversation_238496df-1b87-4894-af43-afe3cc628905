import useFlightLevel from '@/hooks/useFlightLevel.js'

const flightLevel = useFlightLevel()
const showMenu = reactive({
  airspacePlanning: {
    name: '空域规划',
    enabled: false,
    validStatus: { label: '当前有效期内', case: [0], options: ['有效', '无效'], values: ['VALID', 'INVALID'] },
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'], values: ['RUNNING', 'PLANNING'] },
    airspaceCategory: {
      label: '种类',
      case: [0, 1, 2],
      options: ['适飞区域', '管制区域', '禁飞区域'],
      values: ['SUITABLE', 'CONTROLLED', 'NO_FLY'],
    },
    flightLevel: { label: '高度层', case: [], options: [], values: [] },
  },
  airway: {
    name: '航路',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'], values: ['RUNNING', 'PLANNING'] },
  },
  airline: {
    name: '航线',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'], values: ['RUNNING', 'PLANNING'] },
  },
  airspaceCommonUsed: {
    name: '常用飞行区域',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'], values: ['RUNNING', 'PLANNING'] },
    flightLevel: { label: '高度层', case: [], options: [], values: [] },
  },
  importantLocation: {
    name: '重要地理位置',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'], values: ['RUNNING', 'PLANNING'] },
    type: { label: '类型', case: [0, 1], options: ['障碍物', 'POI点'] },
    obstacleKind: {
      label: '障碍物种类',
      case: [0, 1, 2],
      options: ['地点', '路线', '区域'],
      values: ['POINT', 'LINE', 'PLANE'],
    },
  },
  airspaceTemporary: {
    name: '临时区域',
    enabled: false,
    validStatus: { label: '当前有效期内', case: [0], options: ['有效', '无效'], values: ['VALID', 'INVALID'] },
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'], values: ['RUNNING', 'PLANNING'] },
    airspaceCategory: {
      label: '种类',
      case: [0, 1],
      options: ['临时管制', '临时禁飞'],
      values: ['CONTROLLED', 'NO_FLY'],
    },
    flightLevel: { label: '高度层', case: [], options: [], values: [] },
  },
})

watch(flightLevel, () => {
  showMenu.airspacePlanning.flightLevel.options = flightLevel.map(i => i.label)
  showMenu.airspacePlanning.flightLevel.values = flightLevel.map(i => i.id)
  showMenu.airspacePlanning.flightLevel.case = Array.from({ length: flightLevel.length }, (_, i) => i)

  showMenu.airspaceCommonUsed.flightLevel.options = flightLevel.map(i => i.label)
  showMenu.airspaceCommonUsed.flightLevel.values = flightLevel.map(i => i.id)
  showMenu.airspaceCommonUsed.flightLevel.case = Array.from({ length: flightLevel.length }, (_, i) => i)

  showMenu.airspaceTemporary.flightLevel.options = flightLevel.map(i => i.label)
  showMenu.airspaceTemporary.flightLevel.values = flightLevel.map(i => i.id)
  showMenu.airspaceTemporary.flightLevel.case = Array.from({ length: flightLevel.length }, (_, i) => i)
})

const useShowMenu = () => showMenu
export default useShowMenu
