import useFlightLevel from '@/hooks/useFlightLevel.js'

const flightLevel = useFlightLevel()
const showMenu = reactive({
  airspace: {
    name: '空域',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'] },
    airspaceCategory: { label: '种类', case: [0, 1, 2], options: ['适飞区域', '管制区域', '禁飞区域'] },
    flightLevel: { label: '高度层', case: [], options: [] },
  },
  airway: {
    name: '航路',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'] },
  },
  airline: {
    name: '航线',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'] },
  },
  obstacle: {
    name: '障碍物',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'] },
    obstacleKind: { label: '种类', case: [0, 1, 2], options: ['地点', '路线', '区域'] },
  },
  poi: {
    name: 'POI',
    enabled: false,
    runningStatus: { label: '状态', case: [0, 1], options: ['运行中', '规划中'] },
  },
})

watch(flightLevel, () => {
  showMenu.airspace.flightLevel.options = flightLevel.map(i => i.label)
  showMenu.airspace.flightLevel.case = Array.from({ length: flightLevel.length }, (_, i) => i)
})

const useShowMenu = () => showMenu
export default useShowMenu
