import useShowMenu from '@/hooks/useShowMenu.js'

const data = reactive({})

const showMenu = useShowMenu()

watch(
  () => JSON.stringify(showMenu.airspace),
  newVal => {},
)

watch(
  () => JSON.stringify(showMenu.airway),
  newVal => {},
)

watch(
  () => JSON.stringify(showMenu.airline),
  newVal => {},
)

watch(
  () => JSON.stringify(showMenu.obstacle),
  newVal => {},
)

watch(
  () => JSON.stringify(showMenu.poi),
  newVal => {},
)

const useMenuData = () => data
export default useMenuData
