import useShowMenu from '@/hooks/useShowMenu.js'
import * as api from '@/request/api/index.js'
import { EmptyData, SpaceDefaultColor } from '@/data/MapConstant.js'
import { useEventBus } from '@vueuse/core'
import LayerName from '@/components/common/map/data/LayerName.js'
import { geometriesToGeoJson } from '@/utils/mapHelper.js'

const currentId = ref()
const airspace = reactive({ ...EmptyData })
const airway = reactive({ ...EmptyData })
const airline = reactive({ ...EmptyData })
const poi = reactive({ ...EmptyData })
const obstacle_point = reactive({ ...EmptyData })
const obstacle_line = reactive({ ...EmptyData })
const obstacle_plane = reactive({ ...EmptyData })

const showMenu = useShowMenu()
const layerRefreshBus = useEventBus('LayerRefresh')

watch(
  () => JSON.stringify([showMenu.airspace, currentId.value]),
  () => {
    airspace.features.splice(0, airspace.features.length)
    if (
      !showMenu.airspace.enabled ||
      !showMenu.airspace.runningStatus.case.length ||
      !showMenu.airspace.flightLevel.case.length ||
      !showMenu.airspace.airspaceCategory.case.length
    ) {
      layerRefreshBus.emit({ key: LayerName.airSpace, data: EmptyData })
      return
    }
    const params = {
      deleted: 0,
      offset: 0,
      limit: 999,
      status: 1,
      flightLevelIdList: JSON.stringify(
        showMenu.airspace.flightLevel.case.map(i => showMenu.airspace.flightLevel.values[i]),
      ),
      airspaceCategoryList: JSON.stringify(
        showMenu.airspace.airspaceCategory.case.map(i => showMenu.airspace.airspaceCategory.values[i]),
      ),
      ...(showMenu.airspace.runningStatus.case.length === 2
        ? {}
        : { runningStatus: showMenu.airspace.runningStatus.values[showMenu.airspace.runningStatus.case[0]] }),
    }
    api.airspace_aggregate_list(params).then(res => {
      airspace.features.push(
        ...res.list
          .filter(item => item.id !== currentId.value)
          .map(i => {
            return {
              type: 'Feature',
              properties: {
                id: i.id,
                name: i.airspaceName,
                category: i.airspaceCategory,
                tags: i.airspaceTags,
                tagNames: i.airspaceTagNames,
                cityCode: i.cityCode,
                cityName: i.cityName,
                color: i.displayStyle?.color || SpaceDefaultColor[i.airspaceCategory] || '#F7712F',
              },
              geometry: geometriesToGeoJson(i.geometries),
            }
          }),
      )
      layerRefreshBus.emit({ key: LayerName.airSpace, data: airspace })
    })
  },
)

watch(
  () => JSON.stringify([showMenu.airway, currentId.value]),
  () => {
    airway.features.splice(0, airway.features.length)
    if (!showMenu.airway.enabled || !showMenu.airway.runningStatus.case.length) {
      layerRefreshBus.emit({ key: LayerName.airWay, data: EmptyData })
      layerRefreshBus.emit({ key: LayerName.airWayCover, data: EmptyData })
      return
    }
    const params = {
      deleted: 0,
      offset: 0,
      limit: 999,
      status: 1,
      ...(showMenu.airway.runningStatus.case.length === 2
        ? {}
        : { runningStatus: showMenu.airway.runningStatus.values[showMenu.airway.runningStatus.case[0]] }),
    }
    api.la_airway_list(params).then(res => {
      airway.features.push(
        ...res.list
          .filter(item => item.id !== currentId.value)
          .map(i => {
            return {
              type: 'Feature',
              properties: {
                id: i.id,
                name: i.name,
                tags: i.tags,
                tagNames: i.tagNames,
                cityCode: i.cityCode,
                cityName: i.cityName,
                color: i.displayStyle?.color || '#F7712F',
                coverWidth: i.protectedAreaWidth ? (i.protectedAreaWidth / 200) * 6 : 2,
              },
              geometry: { coordinates: i.airwayPoints.map(p => [p.longitude, p.latitude]), type: 'LineString' },
            }
          }),
      )
      layerRefreshBus.emit({ key: LayerName.airWay, data: airway })
      layerRefreshBus.emit({ key: LayerName.airWayCover, data: airway })
    })
  },
)

watch(
  () => JSON.stringify([showMenu.airline, currentId.value]),
  () => {
    airline.features.splice(0, airline.features.length)
    if (!showMenu.airline.enabled || !showMenu.airline.runningStatus.case.length) {
      layerRefreshBus.emit({ key: LayerName.airLine, data: EmptyData })
      return
    }
    const params = {
      deleted: 0,
      offset: 0,
      limit: 999,
      status: 1,
      ...(showMenu.airline.runningStatus.case.length === 2
        ? {}
        : { runningStatus: showMenu.airline.runningStatus.values[showMenu.airline.runningStatus.case[0]] }),
    }
    api.airline_aggregate_list(params).then(res => {
      airline.features.push(
        ...res.list
          .filter(item => item.id !== currentId.value)
          .map(i => {
            return {
              type: 'Feature',
              properties: {
                id: i.id,
                name: i.airlineTitle,
                tags: i.airlineTags,
                tagNames: i.airlineTagNames,
                cityCode: i.cityCode,
                cityName: i.cityName,
                color: i.displayStyle?.color || '#F7712F',
              },
              geometry: { coordinates: i.geometries[0].coordinateList, type: 'LineString' },
            }
          }),
      )
      layerRefreshBus.emit({ key: LayerName.airLine, data: airline })
    })
  },
)

watch(
  () => JSON.stringify([showMenu.obstacle, currentId.value]),
  () => {
    obstacle_point.features.splice(0, obstacle_point.features.length)
    obstacle_line.features.splice(0, obstacle_line.features.length)
    obstacle_plane.features.splice(0, obstacle_plane.features.length)
    if (
      !showMenu.obstacle.enabled ||
      !showMenu.obstacle.runningStatus.case.length ||
      !showMenu.obstacle.obstacleKind.case.length
    ) {
      layerRefreshBus.emit({ key: LayerName.obstaclePoint, data: EmptyData })
      layerRefreshBus.emit({ key: LayerName.obstacleLine, data: EmptyData })
      layerRefreshBus.emit({ key: LayerName.obstacleArea, data: EmptyData })
      return
    }
    const params = {
      deleted: 0,
      offset: 0,
      limit: 999,
      status: 1,
      ...(showMenu.obstacle.runningStatus.case.length === 2
        ? {}
        : { runningStatus: showMenu.obstacle.runningStatus.values[showMenu.obstacle.runningStatus.case[0]] }),
      kindList: JSON.stringify(showMenu.obstacle.obstacleKind.case.map(i => showMenu.obstacle.obstacleKind.values[i])),
    }
    api.obstacle_list(params).then(res => {
      res.list.forEach(i => {
        const item = {
          type: 'Feature',
          properties: {
            id: i.id,
            name: i.name,
            tags: i.tags,
            tagNames: i.tagNames,
            cityCode: i.cityCode,
            cityName: i.cityName,
            color: i.displayStyle?.color || '#F7712F',
          },
        }
        switch (i.kind) {
          case 'POINT':
            item.geometry = { coordinates: i.geometries[0].coordinate, type: 'Point' }
            obstacle_point.features.push(item)
            break
          case 'LINE':
            item.geometry = { coordinates: i.geometries[0].coordinateList, type: 'LineString' }
            obstacle_line.features.push(item)
            break
          case 'PLANE':
            item.geometry = geometriesToGeoJson(i.geometries)
            obstacle_plane.features.push(item)
            break
        }
      })
      layerRefreshBus.emit({ key: LayerName.obstaclePoint, data: obstacle_point })
      layerRefreshBus.emit({ key: LayerName.obstacleLine, data: obstacle_line })
      layerRefreshBus.emit({ key: LayerName.obstacleArea, data: obstacle_plane })
    })
  },
)

watch(
  () => JSON.stringify([showMenu.poi, currentId.value]),
  () => {
    poi.features.splice(0, poi.features.length)
    if (!showMenu.poi.enabled || !showMenu.poi.runningStatus.case.length) {
      layerRefreshBus.emit({ key: LayerName.poi, data: EmptyData })
      return
    }
    const params = {
      deleted: 0,
      offset: 0,
      limit: 999,
      status: 1,
      ...(showMenu.poi.runningStatus.case.length === 2
        ? {}
        : { runningStatus: showMenu.poi.runningStatus.values[showMenu.poi.runningStatus.case[0]] }),
    }
    api.poi_list(params).then(res => {
      poi.features.push(
        ...res.list
          .filter(item => item.id !== currentId.value)
          .map(i => {
            return {
              type: 'Feature',
              properties: {
                id: i.id,
                name: i.name,
                tags: i.tags,
                tagNames: i.tagNames,
                cityCode: i.cityCode,
                cityName: i.cityName,
                color: i.displayStyle?.color || '#F7712F',
              },
              geometry: { coordinates: [i.longitude, i.latitude], type: 'Point' },
            }
          }),
      )
      layerRefreshBus.emit({ key: LayerName.poi, data: poi })
    })
  },
)

const useShowData = () => ({ currentId, airspace, airway, airline, poi, obstacle_point, obstacle_line, obstacle_plane })
export default useShowData
