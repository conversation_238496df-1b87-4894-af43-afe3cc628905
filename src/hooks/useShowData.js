import useShowMenu from '@/hooks/useShowMenu.js'
import * as api from '@/request/api/index.js'
import { EmptyData, SpaceDefaultColor } from '@/data/MapConstant.js'
import { useEventBus } from '@vueuse/core'
import LayerName from '@/components/common/map/data/LayerName.js'
import { geometriesToGeoJson } from '@/utils/mapHelper.js'

// 当前编辑的id
const currentId = ref()

// 图层数据
const airspacePlanning = reactive({ ...EmptyData })
const airway = reactive({ ...EmptyData })
const airline = reactive({ ...EmptyData })
const airspaceCommonUsed = reactive({ ...EmptyData })
const poi = reactive({ ...EmptyData })
const obstacle_point = reactive({ ...EmptyData })
const obstacle_line = reactive({ ...EmptyData })
const obstacle_plane = reactive({ ...EmptyData })

// 额外数据
const extra = ref({})

const showMenu = useShowMenu()
const layerRefreshBus = useEventBus('LayerRefresh')

watch(
  () => JSON.stringify([showMenu.airspacePlanning, currentId.value]),
  () => {
    airspacePlanning.features.splice(0, airspacePlanning.features.length)
    if (
      !showMenu.airspacePlanning.enabled ||
      !showMenu.airspacePlanning.runningStatus.case.length ||
      !showMenu.airspacePlanning.flightLevel.case.length ||
      !showMenu.airspacePlanning.airspaceCategory.case.length
    ) {
      layerRefreshBus.emit({ key: LayerName.airspacePlanning, data: EmptyData })
      return
    }
    const params = {
      deleted: 0,
      offset: 0,
      limit: 999,
      status: 1,
      source: 'DELIMITATION',
      flightLevelIdList: JSON.stringify(
        showMenu.airspacePlanning.flightLevel.case.map(i => showMenu.airspacePlanning.flightLevel.values[i]),
      ),
      airspaceCategoryList: JSON.stringify(
        showMenu.airspacePlanning.airspaceCategory.case.map(i => showMenu.airspacePlanning.airspaceCategory.values[i]),
      ),
      ...(showMenu.airspacePlanning.runningStatus.case.length === 2
        ? {}
        : {
            runningStatus:
              showMenu.airspacePlanning.runningStatus.values[showMenu.airspacePlanning.runningStatus.case[0]],
          }),
    }
    api.airspace_aggregate_list(params).then(res => {
      airspacePlanning.features.push(
        ...res.list
          .filter(item => item.id !== currentId.value)
          .map(i => {
            return {
              type: 'Feature',
              properties: {
                id: i.id,
                name: i.airspaceName,
                category: i.airspaceCategory,
                tags: i.airspaceTags,
                tagNames: i.airspaceTagNames,
                cityCode: i.cityCode,
                cityName: i.cityName,
                color: i.displayStyle?.color || SpaceDefaultColor[i.airspaceCategory] || '#F7712F',
              },
              geometry: geometriesToGeoJson(i.geometries),
            }
          }),
      )
      layerRefreshBus.emit({ key: LayerName.airspacePlanning, data: airspacePlanning })
    })
  },
)

watch(
  () => JSON.stringify([showMenu.airway, currentId.value]),
  () => {
    airway.features.splice(0, airway.features.length)
    if (!showMenu.airway.enabled || !showMenu.airway.runningStatus.case.length) {
      layerRefreshBus.emit({ key: LayerName.airWay, data: EmptyData })
      layerRefreshBus.emit({ key: LayerName.airWayCover, data: EmptyData })
      return
    }
    const params = {
      deleted: 0,
      offset: 0,
      limit: 999,
      status: 1,
      ...(showMenu.airway.runningStatus.case.length === 2
        ? {}
        : { runningStatus: showMenu.airway.runningStatus.values[showMenu.airway.runningStatus.case[0]] }),
    }
    api.la_airway_list(params).then(res => {
      airway.features.push(
        ...res.list
          .filter(item => item.id !== currentId.value)
          .map(i => {
            return {
              type: 'Feature',
              properties: {
                id: i.id,
                name: i.name,
                tags: i.tags,
                tagNames: i.tagNames,
                cityCode: i.cityCode,
                cityName: i.cityName,
                color: i.displayStyle?.color || '#F7712F',
                coverWidth: i.protectedAreaWidth ? (i.protectedAreaWidth / 200) * 6 : 2,
              },
              geometry: { coordinates: i.airwayPoints.map(p => [p.longitude, p.latitude]), type: 'LineString' },
            }
          }),
      )
      layerRefreshBus.emit({ key: LayerName.airWay, data: airway })
      layerRefreshBus.emit({ key: LayerName.airWayCover, data: airway })
    })
  },
)

watch(
  () => JSON.stringify([showMenu.airline, currentId.value]),
  () => {
    airline.features.splice(0, airline.features.length)
    if (!showMenu.airline.enabled || !showMenu.airline.runningStatus.case.length) {
      layerRefreshBus.emit({ key: LayerName.airLine, data: EmptyData })
      return
    }
    const params = {
      deleted: 0,
      offset: 0,
      limit: 999,
      status: 1,
      ...(showMenu.airline.runningStatus.case.length === 2
        ? {}
        : { runningStatus: showMenu.airline.runningStatus.values[showMenu.airline.runningStatus.case[0]] }),
    }
    api.airline_aggregate_list(params).then(res => {
      airline.features.push(
        ...res.list
          .filter(item => item.id !== currentId.value)
          .map(i => {
            return {
              type: 'Feature',
              properties: {
                id: i.id,
                name: i.airlineTitle,
                tags: i.airlineTags,
                tagNames: i.airlineTagNames,
                cityCode: i.cityCode,
                cityName: i.cityName,
                color: i.displayStyle?.color || '#F7712F',
              },
              geometry: { coordinates: i.geometries[0].coordinateList, type: 'LineString' },
            }
          }),
      )
      layerRefreshBus.emit({ key: LayerName.airLine, data: airline })
    })
  },
)

watch(
  () => JSON.stringify([showMenu.airspaceCommonUsed, currentId.value]),
  () => {
    airspaceCommonUsed.features.splice(0, airspaceCommonUsed.features.length)
    if (
      !showMenu.airspaceCommonUsed.enabled ||
      !showMenu.airspaceCommonUsed.runningStatus.case.length ||
      !showMenu.airspaceCommonUsed.flightLevel.case.length
    ) {
      layerRefreshBus.emit({ key: LayerName.airspaceCommonUsed, data: EmptyData })
      return
    }
    const params = {
      deleted: 0,
      offset: 0,
      limit: 999,
      status: 1,
      source: 'COMMON',
      flightLevelIdList: JSON.stringify(
        showMenu.airspaceCommonUsed.flightLevel.case.map(i => showMenu.airspaceCommonUsed.flightLevel.values[i]),
      ),
      ...(showMenu.airspaceCommonUsed.runningStatus.case.length === 2
        ? {}
        : {
            runningStatus:
              showMenu.airspaceCommonUsed.runningStatus.values[showMenu.airspaceCommonUsed.runningStatus.case[0]],
          }),
    }
    api.airspace_aggregate_list(params).then(res => {
      airspaceCommonUsed.features.push(
        ...res.list
          .filter(item => item.id !== currentId.value)
          .map(i => {
            return {
              type: 'Feature',
              properties: {
                id: i.id,
                name: i.airspaceName,
                category: i.airspaceCategory,
                tags: i.airspaceTags,
                tagNames: i.airspaceTagNames,
                cityCode: i.cityCode,
                cityName: i.cityName,
                color: i.displayStyle?.color || SpaceDefaultColor[i.airspaceCategory] || '#F7712F',
              },
              geometry: geometriesToGeoJson(i.geometries),
            }
          }),
      )
      layerRefreshBus.emit({ key: LayerName.airspaceCommonUsed, data: airspaceCommonUsed })
    })
  },
)

watch(
  () => JSON.stringify([showMenu.importantLocation, currentId.value]),
  () => {
    poi.features.splice(0, poi.features.length)
    obstacle_point.features.splice(0, obstacle_point.features.length)
    obstacle_line.features.splice(0, obstacle_line.features.length)
    obstacle_plane.features.splice(0, obstacle_plane.features.length)
    if (!showMenu.importantLocation.enabled || !showMenu.importantLocation.runningStatus.case.length) {
      layerRefreshBus.emit({ key: LayerName.poi, data: EmptyData })
      layerRefreshBus.emit({ key: LayerName.obstaclePoint, data: EmptyData })
      layerRefreshBus.emit({ key: LayerName.obstacleLine, data: EmptyData })
      layerRefreshBus.emit({ key: LayerName.obstacleArea, data: EmptyData })
      return
    }
    if (!showMenu.importantLocation.obstacleKind.case.length || !showMenu.importantLocation.type.case.includes(0)) {
      layerRefreshBus.emit({ key: LayerName.obstaclePoint, data: EmptyData })
      layerRefreshBus.emit({ key: LayerName.obstacleLine, data: EmptyData })
      layerRefreshBus.emit({ key: LayerName.obstacleArea, data: EmptyData })
    } else {
      const params = {
        deleted: 0,
        offset: 0,
        limit: 999,
        status: 1,
        ...(showMenu.importantLocation.runningStatus.case.length === 2
          ? {}
          : {
              runningStatus:
                showMenu.importantLocation.runningStatus.values[showMenu.importantLocation.runningStatus.case[0]],
            }),
        kindList: JSON.stringify(
          showMenu.importantLocation.obstacleKind.case.map(i => showMenu.importantLocation.obstacleKind.values[i]),
        ),
      }
      api.obstacle_list(params).then(res => {
        res.list.forEach(i => {
          const item = {
            type: 'Feature',
            properties: {
              id: i.id,
              name: i.name,
              tags: i.tags,
              tagNames: i.tagNames,
              cityCode: i.cityCode,
              cityName: i.cityName,
              color: i.displayStyle?.color || '#F7712F',
            },
          }
          switch (i.kind) {
            case 'POINT':
              item.geometry = { coordinates: i.geometries[0].coordinate, type: 'Point' }
              obstacle_point.features.push(item)
              break
            case 'LINE':
              item.geometry = { coordinates: i.geometries[0].coordinateList, type: 'LineString' }
              obstacle_line.features.push(item)
              break
            case 'PLANE':
              item.geometry = geometriesToGeoJson(i.geometries)
              obstacle_plane.features.push(item)
              break
          }
        })
        layerRefreshBus.emit({ key: LayerName.obstaclePoint, data: obstacle_point })
        layerRefreshBus.emit({ key: LayerName.obstacleLine, data: obstacle_line })
        layerRefreshBus.emit({ key: LayerName.obstacleArea, data: obstacle_plane })
      })
    }

    if (!showMenu.importantLocation.type.case.includes(1)) {
      layerRefreshBus.emit({ key: LayerName.poi, data: EmptyData })
    } else {
      const params = {
        deleted: 0,
        offset: 0,
        limit: 999,
        status: 1,
        ...(showMenu.importantLocation.runningStatus.case.length === 2
          ? {}
          : {
              runningStatus:
                showMenu.importantLocation.runningStatus.values[showMenu.importantLocation.runningStatus.case[0]],
            }),
      }

      api.poi_list(params).then(res => {
        poi.features.push(
          ...res.list
            .filter(item => item.id !== currentId.value)
            .map(i => {
              return {
                type: 'Feature',
                properties: {
                  id: i.id,
                  name: i.name,
                  tags: i.tags,
                  tagNames: i.tagNames,
                  cityCode: i.cityCode,
                  cityName: i.cityName,
                  color: i.displayStyle?.color || '#F7712F',
                },
                geometry: { coordinates: [i.longitude, i.latitude], type: 'Point' },
              }
            }),
        )
        layerRefreshBus.emit({ key: LayerName.poi, data: poi })
      })
    }
  },
)

watch(
  () => JSON.stringify(extra.value),
  () => {
    if (extra.value?.baseData?.[0]?.type !== 'LINE' || !extra.value?.protectedAreaWidth) {
      layerRefreshBus.emit({ key: LayerName.extraLine, data: EmptyData })
    } else {
      const newExtraLineData = {
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            properties: {
              color: extra.value?.style?.color || '#F7712F',
              coverWidth: extra.value?.protectedAreaWidth ? (extra.value?.protectedAreaWidth / 200) * 6 : 2,
            },
            geometry: {
              type: 'LineString',
              coordinates: extra.value?.baseData?.[0]?.coordinateList,
            },
          },
        ],
      }
      layerRefreshBus.emit({ key: LayerName.extraLine, data: newExtraLineData })
    }

    const airports = (extra.value?.airports ?? []).filter(
      i => i.longitude < 180 && i.longitude > -180 && i.latitude < 90 && i.latitude > -90,
    )
    const emergencyAirports = (extra.value?.emergencyAirports ?? []).filter(
      i => i.longitude < 180 && i.longitude > -180 && i.latitude < 90 && i.latitude > -90,
    )
    if (extra.value?.baseData?.[0]?.type !== 'LINE' || airports.length + emergencyAirports.length === 0) {
      layerRefreshBus.emit({ key: LayerName.extraPoint, data: EmptyData })
    } else {
      const newExtraPointData = {
        type: 'FeatureCollection',
        features: [
          ...airports.map(a => ({
            type: 'Feature',
            properties: {
              color: extra.value?.style?.color || '#F7712F',
              size: 10,
            },
            geometry: {
              type: 'Point',
              coordinates: [a.longitude, a.latitude],
            },
          })),
          ...emergencyAirports.map(e => ({
            type: 'Feature',
            properties: {
              color: extra.value?.style?.color || '#F7712F',
              size: 15,
            },
            geometry: {
              type: 'Point',
              coordinates: [e.longitude, e.latitude],
            },
          })),
        ],
      }
      layerRefreshBus.emit({ key: LayerName.extraPoint, data: newExtraPointData })
    }
  },
  { immediate: true },
)

const useShowData = () => ({
  currentId,
  airspacePlanning,
  airspaceCommonUsed,
  airway,
  airline,
  poi,
  obstacle_point,
  obstacle_line,
  obstacle_plane,
  extra,
})
export default useShowData
