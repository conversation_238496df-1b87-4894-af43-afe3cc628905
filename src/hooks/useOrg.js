import { ref } from 'vue'
import useRequestQueue from '@/hooks/useRequestQueue.js'
import { list } from '@/request/api/org.js'
import { useStorage } from '@vueuse/core'
import useUser from '@/store/user.js'
import { getSession } from '@/request/api/index.js'
import { CityCodeEnum, CityEnum } from '@/data/City.js'

const user = useUser()

const orgList = reactive([])

const orgStorage = useStorage('flight-air-space-org', {}, localStorage)

const orgId = ref(orgStorage.value?.id || null)

const orgName = computed(() => {
  return orgList?.find(a => a.id === orgId.value)?.orgName || orgStorage.value?.orgName || null
})

const cityCode = computed(() => {
  return orgList?.find(a => a.id === orgId.value)?.cityCode || orgStorage.value?.cityCode || null
})

const cityName = computed(() => {
  return orgList?.find(a => a.id === orgId.value)?.cityName || orgStorage.value?.cityName || null
})
const cityOption = ref(orgStorage.value?.cityOption ?? [])
watch(cityCode, value => {
  if (!value) return
  cityOption.value = CityEnum.filter(i => value === CityCodeEnum.all || i.value === value)
  orgStorage.value.cityOption = cityOption.value
})

const init = ref(false)

const fetchData = () => {
  if (!user.getUserInfo()) return
  list().then(res => {
    getSession().then(session => {
      user.setUserInfo(session.userSession)
      orgList.splice(0, orgList.length)
      const userOrgList = user.getUserInfo()?.orgIdList
      res.list
        .sort((a, b) => a.sequence - b.sequence)
        .forEach(item => {
          orgList.push({ ...item, enable: userOrgList?.find(a => a === item.id) })
        })
      if (user.getUserInfo()?.orgIdList.length === 1) orgId.value = user.getUserInfo()?.orgIdList[0]
      else orgId.value = orgStorage.value?.id || null
    })
  })
}
const { triggerFetch } = useRequestQueue(fetchData, init)

const useOrg = () => {
  const selectOrg = id => {
    orgId.value = id
    orgStorage.value = { ...orgList?.find(a => a.id === id), cityOption: cityOption.value }
  }

  if (init.value) return { orgId, orgName, orgList, selectOrg, cityCode, cityName, cityOption, init }
  triggerFetch?.()
  return { orgId, orgName, orgList, selectOrg, cityCode, cityName, cityOption, init }
}
export default useOrg
