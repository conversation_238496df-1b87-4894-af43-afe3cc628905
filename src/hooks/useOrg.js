import { ref } from 'vue'
import useRequestQueue from '@/hooks/useRequestQueue.js'
import { list } from '@/request/api/org.js'
import { useStorage, useThrottleFn } from '@vueuse/core'
import useUser from '@/store/user.js'
import { getSession } from '@/request/api/index.js'
import { CityCodeEnum, CityEnum } from '@/data/City.js'

const user = useUser()

// 所有的机构
const allOrg = reactive([])
// 所有的机构（和当前登录机构无关），id: parentOrg映射
const allOrgParent = reactive({})

const orgList = reactive([])

const orgStorage = useStorage('flight-air-space-org', {}, localStorage)

const orgId = ref(orgStorage.value?.id || null)
const clearOrgStorage = () => {
  orgId.value = null
  cityOption.value = []
  orgList.splice(0, orgList.length)
  orgStorage.value = {}
}

const orgName = computed(() => {
  return orgList?.find(a => a.id === orgId.value)?.orgName || orgStorage.value?.orgName || null
})

const cityCode = computed(() => {
  return orgList?.find(a => a.id === orgId.value)?.cityCode || orgStorage.value?.cityCode || null
})

const cityName = computed(() => {
  return orgList?.find(a => a.id === orgId.value)?.cityName || orgStorage.value?.cityName || null
})
const cityOption = ref(orgStorage.value?.cityOption ?? [])
watch(cityCode, value => {
  if (!value) return
  cityOption.value = CityEnum.filter(i => value === CityCodeEnum.all || i.value === value)
  orgStorage.value.cityOption = cityOption.value
})

const approvalOrgOption = computed(() => {
  if (!orgId.value || !allOrgParent[orgId.value]) return []
  return [allOrgParent[orgId.value]]
})

const init = ref(false)
const fetchData = () => {
  if (!user.getAccessId() || !user.getUserInfo()) {
    throw new Error('当前未登录')
    return
  }
  const prcessData = () => {
    getSession().then(session => {
      user.setUserInfo(session.userSession)
      orgList.splice(0, orgList.length)
      const userOrgList = user.getUserInfo()?.orgIdList
      allOrg.forEach(item => orgList.push({ ...item, enable: userOrgList?.find(a => a === item.id) }))
      if (user.getUserInfo()?.orgIdList.length === 1) orgId.value = user.getUserInfo()?.orgIdList[0]
      else orgId.value = orgStorage.value?.id || null
    })
  }

  if (!allOrg.length) {
    list().then(res => {
      const data = res.list ?? []
      data
        .sort((a, b) => a.sequence - b.sequence)
        .forEach(item => {
          if (item.parentOrgId) {
            const findParentItem = data.find(i => i.id === item.parentOrgId)
            if (findParentItem) {
              allOrgParent[item.id] = { ...findParentItem, label: findParentItem.orgName, value: findParentItem.id }
            } else allOrgParent[item.id] = { ...item, label: item.orgName, value: item.id }
          } else {
            allOrgParent[item.id] = { ...item, label: item.orgName, value: item.id }
          }
          allOrg.push({ ...item, label: item.orgName, value: item.id })
        })
      prcessData()
    })
  } else prcessData()
}
const { triggerFetch } = useRequestQueue(fetchData, init)

const useOrg = () => {
  const selectOrg = id => {
    orgId.value = id
    orgStorage.value = { ...orgList?.find(a => a.id === id), cityOption: cityOption.value }
  }
  if (init.value)
    return {
      orgId,
      orgName,
      orgList,
      selectOrg,
      approvalOrgOption,
      cityCode,
      cityName,
      cityOption,
      init,
      clearOrgStorage,
    }
  triggerFetch?.()
  return {
    orgId,
    orgName,
    orgList,
    selectOrg,
    approvalOrgOption,
    cityCode,
    cityName,
    cityOption,
    init,
    clearOrgStorage,
  }
}
export default useOrg
