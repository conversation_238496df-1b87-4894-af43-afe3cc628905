import { ref } from 'vue'
import useRequestQueue from '@/hooks/useRequestQueue.js'
import { list } from '@/request/api/org.js'
import { useStorage } from '@vueuse/core'
import useUser from '@/store/user.js'
import { getSession } from '@/request/api/index.js'

const user = useUser()

const orgList = reactive([])

const idStorage = useStorage('flight-service-org-id', null, localStorage)

const orgId = ref(idStorage)

const orgName = computed(() => {
  return orgList?.find(a => a.id === orgId.value)?.orgName || null
})

const cityCode = computed(() => {
  return orgList?.find(a => a.id === orgId.value)?.cityCode || null
})

const cityName = computed(() => {
  return orgList?.find(a => a.id === orgId.value)?.cityName || null
})

const init = ref(false)

const fetchData = () => {
  if (!user.getUserInfo()) return
  list({ status: 1, deleted: 0 }).then(res => {
    getSession({}).then(session => {
      user.setUserInfo(session.userSession)
      orgList.splice(0, orgList.length)
      const userOrgList = user.getUserInfo()?.orgIdList
      res.list
        .sort((a, b) => a.sequence - b.sequence)
        .forEach(item => {
          orgList.push({ ...item, enable: userOrgList?.find(a => a === item.id) })
        })
      if (user.getUserInfo()?.orgIdList.length === 1) orgId.value = user.getUserInfo()?.orgIdList[0]
      else
        orgId.value =
          !idStorage.value || !orgList?.filter(a => a.enable).find(a => a.id === idStorage.value)
            ? null
            : idStorage.value
    })
  })
}
const { triggerFetch } = useRequestQueue(fetchData, init)

const useOrg = () => {
  const selectOrg = id => {
    orgId.value = id
    idStorage.value = id
  }

  if (init.value) return { orgId, orgName, orgList, selectOrg, cityCode, cityName, init }
  triggerFetch?.()
  return { orgId, orgName, orgList, selectOrg, cityCode, cityName, init }
}
export default useOrg
