import { ref } from 'vue'
import useRequestQueue from '@/hooks/useRequestQueue.js'
import { list } from '@/request/api/org.js'
import { useStorage } from '@vueuse/core'
import { CityEnum } from '@/data/City.js'

// 机构项
const orgList = reactive([])

// 可供新增， 编辑时的区县选择项, 筛选的时候直接用 CityEnum
const optionList = computed(() => {
  const item = CityEnum.find(i => i.code.toString() === orgId.value)
  if (!item) return []
  if (item.code === 330600) return CityEnum
  return [item]
})

const idStorage = useStorage('flight-air-space-org-id', null, sessionStorage)
const orgId = ref(idStorage)

const orgName = computed(() => {
  return orgList?.find(a => a.id === orgId.value)?.orgName || null
})
const cityName = computed(() => {
  return orgList?.find(a => a.id === orgId.value)?.cityName || null
})

const init = ref(false)

const fetchData = () => {
  list({ status: 1, deleted: 0 }).then(res => {
    if (!res.list?.length) return
    orgList.splice(0, orgList.length)
    orgList.push(...res.list)
    orgId.value = !idStorage.value || !orgList?.find(a => a.id === idStorage.value) ? null : idStorage.value
  })
}
const { triggerFetch } = useRequestQueue(fetchData, init)

const useOrg = () => {
  const selectOrg = id => {
    orgId.value = id
    idStorage.value = id
  }

  if (init.value) return { orgId, orgName, cityName, orgList, optionList, selectOrg }
  triggerFetch?.()
  return { orgId, orgName, cityName, orgList, optionList, selectOrg }
}
export default useOrg
