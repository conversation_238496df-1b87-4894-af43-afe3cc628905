/* http://meyerweb.com/eric/tools/css/reset/ */
/* v1.0 | 20080212 */

* {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
  font-family: 'PingFang SC';
  font-weight: 400;
}

body {
  line-height: 1;
}

img {
  -webkit-user-drag: none; /* 禁止拖拽（适用于WebKit浏览器） */
}

/* 定义滚动条的宽度 */
::-webkit-scrollbar {
  width: 12px; /* 垂直滚动条的宽度 */
  height: 12px; /* 水平滚动条的宽度 */
}

/* 滚动条轨道的样式 */
::-webkit-scrollbar-track {
  background: transparent; /* 轨道透明 */
}

/* 滚动条滑块的样式 */
::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2); /* 滑块半透明 */
  border-radius: 6px; /* 滑块圆角 */
}

/* 滚动条滑块在鼠标悬停时的样式 */
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5); /* 鼠标悬停时更不透明 */
}

.scroll-container::-webkit-scrollbar-track {
  background-color: transparent;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}

/* remember to define focus styles! */
:focus {
  outline: 0;
}

/* remember to highlight inserts somehow! */
ins {
  text-decoration: none;
}

del {
  text-decoration: line-through;
}

/* tables still need 'cellspacing="0"' in the markup */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/*民航popup-container的样式*/
.map-label-popup-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  border-radius: 4px;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.12);
  min-width: 80px;
  min-height: 40px;
  background: rgba(29, 31, 34, 0.75);
}

.map-aircraft-popup-up {
  font-size: 16px;
  color: #ffffff;

  text-align: center;
  line-height: 24px;
}

.map-aircraft-popup-down {
  margin-top: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #ffffff;
  text-align: center;
  line-height: 18px;
}

.map-drone-popup-up {
  display: flex;
  align-items: center;
  height: 24px;
}

.map-drone-popup-up-serial {
  font-size: 16px;
  color: #ffffff;

  text-align: center;
  line-height: 24px;
}

.map-drone-popup-down {
  display: flex;
  align-items: center;
  height: 18px;
  margin-top: 3px;
}
