// prettier.config.js
/**
 * @type {import('prettier').Config}
 * @see https://www.prettier.cn/docs/options.html
 */
export default {
  trailingComma: 'all',
  singleQuote: true,
  semi: false,
  printWidth: 120,
  arrowParens: 'avoid', // 用于控制箭头函数参数周围的括号。其值设置为"avoid"，意味着尽可能避免在箭头函数参数周围添加括号，除非需要。
  proseWrap: 'always',
  endOfLine: 'auto',
  experimentalTernaries: false,
  tabWidth: 2,
  useTabs: false,
  quoteProps: 'consistent',
  jsxSingleQuote: false,
  bracketSpacing: true,
  bracketSameLine: false,
  jsxBracketSameLine: false,
  vueIndentScriptAndStyle: false,
  singleAttributePerLine: false,
}
